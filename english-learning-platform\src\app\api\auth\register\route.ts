import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { validateEmail, validatePassword } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { name, email, password, confirmPassword, role = 'student' } = body;

    // Validation
    const errors: Record<string, string> = {};

    if (!name || name.trim().length < 2) {
      errors.name = 'Tên phải có ít nhất 2 ký tự';
    }

    if (!email) {
      errors.email = 'Email là bắt buộc';
    } else if (!validateEmail(email)) {
      errors.email = 'Email không hợp lệ';
    }

    if (!password) {
      errors.password = 'Mật khẩu là bắt buộc';
    } else {
      const passwordValidation = validatePassword(password);
      if (!passwordValidation.isValid) {
        errors.password = passwordValidation.errors[0];
      }
    }

    if (!confirmPassword) {
      errors.confirmPassword = '<PERSON><PERSON>c nhận mật khẩu là bắt buộc';
    } else if (password !== confirmPassword) {
      errors.confirmPassword = 'Mật khẩu xác nhận không khớp';
    }

    if (Object.keys(errors).length > 0) {
      return NextResponse.json({
        success: false,
        message: 'Dữ liệu không hợp lệ',
        errors
      }, { status: 400 });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return NextResponse.json({
        success: false,
        message: 'Email đã được sử dụng',
        errors: { email: 'Email đã được đăng ký' }
      }, { status: 409 });
    }

    // Create new user
    const user = new User({
      name: name.trim(),
      email: email.toLowerCase(),
      password,
      role: ['admin', 'teacher', 'student'].includes(role) ? role : 'student'
    });

    await user.save();

    // Remove password from response
    const userResponse = user.toJSON();

    return NextResponse.json({
      success: true,
      message: 'Đăng ký thành công',
      data: {
        user: userResponse
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Registration error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi hệ thống, vui lòng thử lại sau',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
