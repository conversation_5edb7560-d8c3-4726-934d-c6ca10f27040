import Course from '@/models/Course';
import User from '@/models/User';

const sampleCourses = [
  {
    title: 'Tiếng Anh Cơ Bản cho Người Mới Bắt Đầu',
    description: '<PERSON><PERSON><PERSON><PERSON> học tiếng <PERSON>h cơ bản dành cho người mới bắt đầu. <PERSON><PERSON><PERSON> từ vựng, ngữ pháp và giao tiếp cơ bản.',
    thumbnail: '/images/courses/basic-english.jpg',
    price: 299000,
    originalPrice: 499000,
    level: 'beginner',
    duration: 1200, // 20 hours
    language: 'vietnamese',
    category: 'general',
    tags: ['c<PERSON> bản', 'từ vựng', 'ngữ pháp', 'giao tiếp'],
    curriculum: [
      {
        title: 'Giới thiệu về tiếng Anh',
        description: 'Tổng quan về tiếng Anh và cách học hiệu quả',
        duration: 30,
        videoUrl: '/videos/lesson1.mp4',
        materials: ['slide1.pdf', 'vocabulary1.pdf']
      },
      {
        title: '<PERSON>ảng chữ cái và phát âm',
        description: '<PERSON><PERSON><PERSON> bảng chữ cái tiếng Anh và cách phát âm chuẩn',
        duration: 45,
        videoUrl: '/videos/lesson2.mp4',
        materials: ['alphabet.pdf', 'pronunciation.mp3']
      },
      {
        title: 'Từ vựng cơ bản - Gia đình',
        description: 'Học từ vựng về gia đình và các mối quan hệ',
        duration: 40,
        videoUrl: '/videos/lesson3.mp4',
        materials: ['family-vocab.pdf']
      }
    ],
    requirements: ['Không cần kiến thức trước', 'Máy tính hoặc điện thoại có internet'],
    objectives: [
      'Nắm vững 500 từ vựng cơ bản',
      'Hiểu và sử dụng ngữ pháp cơ bản',
      'Giao tiếp đơn giản trong cuộc sống hàng ngày'
    ],
    isPublished: true,
    rating: 4.8,
    totalRatings: 156,
    totalStudents: 1250
  },
  {
    title: 'Luyện Nghe IELTS 6.5+',
    description: 'Khóa học luyện nghe IELTS chuyên sâu, giúp đạt band 6.5 trở lên.',
    thumbnail: '/images/courses/ielts-listening.jpg',
    price: 599000,
    originalPrice: 899000,
    level: 'intermediate',
    duration: 1800, // 30 hours
    language: 'vietnamese',
    category: 'ielts',
    tags: ['IELTS', 'listening', 'band 6.5', 'luyện thi'],
    curriculum: [
      {
        title: 'Giới thiệu về IELTS Listening',
        description: 'Tổng quan về bài thi IELTS Listening và chiến lược làm bài',
        duration: 60,
        videoUrl: '/videos/ielts-intro.mp4',
        materials: ['ielts-guide.pdf', 'strategies.pdf']
      },
      {
        title: 'Part 1: Conversation',
        description: 'Luyện tập Part 1 - Hội thoại trong cuộc sống hàng ngày',
        duration: 90,
        videoUrl: '/videos/part1.mp4',
        materials: ['part1-practice.pdf', 'audio-files.zip']
      },
      {
        title: 'Part 2: Monologue',
        description: 'Luyện tập Part 2 - Độc thoại về các chủ đề thường gặp',
        duration: 90,
        videoUrl: '/videos/part2.mp4',
        materials: ['part2-practice.pdf', 'monologue-samples.mp3']
      }
    ],
    requirements: ['Trình độ tiếng Anh trung cấp', 'Đã có kiến thức cơ bản về IELTS'],
    objectives: [
      'Đạt band 6.5+ trong IELTS Listening',
      'Nắm vững các kỹ thuật làm bài',
      'Cải thiện khả năng nghe hiểu tổng quát'
    ],
    isPublished: true,
    rating: 4.9,
    totalRatings: 89,
    totalStudents: 650
  },
  {
    title: 'Tiếng Anh Giao Tiếp Thực Tế',
    description: 'Học tiếng Anh giao tiếp thực tế trong các tình huống hàng ngày.',
    thumbnail: '/images/courses/conversation.jpg',
    price: 399000,
    originalPrice: 599000,
    level: 'intermediate',
    duration: 1500, // 25 hours
    language: 'vietnamese',
    category: 'conversation',
    tags: ['giao tiếp', 'thực tế', 'speaking', 'pronunciation'],
    curriculum: [
      {
        title: 'Giới thiệu bản thân',
        description: 'Cách giới thiệu bản thân một cách tự nhiên và ấn tượng',
        duration: 45,
        videoUrl: '/videos/introduction.mp4',
        materials: ['self-intro.pdf', 'examples.mp3']
      },
      {
        title: 'Giao tiếp tại cửa hàng',
        description: 'Các câu giao tiếp khi mua sắm và thanh toán',
        duration: 60,
        videoUrl: '/videos/shopping.mp4',
        materials: ['shopping-phrases.pdf', 'dialogues.mp3']
      },
      {
        title: 'Hỏi đường và chỉ đường',
        description: 'Cách hỏi đường và chỉ đường một cách lịch sự',
        duration: 50,
        videoUrl: '/videos/directions.mp4',
        materials: ['directions.pdf', 'map-vocab.pdf']
      }
    ],
    requirements: ['Biết tiếng Anh cơ bản', 'Muốn cải thiện khả năng giao tiếp'],
    objectives: [
      'Giao tiếp tự tin trong cuộc sống hàng ngày',
      'Phát âm chuẩn và tự nhiên',
      'Hiểu và sử dụng các cụm từ thông dụng'
    ],
    isPublished: true,
    rating: 4.7,
    totalRatings: 234,
    totalStudents: 890
  },
  {
    title: 'Ngữ Pháp Tiếng Anh Nâng Cao',
    description: 'Khóa học ngữ pháp tiếng Anh nâng cao cho người muốn hoàn thiện kiến thức.',
    thumbnail: '/images/courses/advanced-grammar.jpg',
    price: 449000,
    originalPrice: 699000,
    level: 'advanced',
    duration: 2000, // 33 hours
    language: 'vietnamese',
    category: 'grammar',
    tags: ['ngữ pháp', 'nâng cao', 'grammar', 'advanced'],
    curriculum: [
      {
        title: 'Các thì phức tạp',
        description: 'Học các thì phức tạp và cách sử dụng chính xác',
        duration: 120,
        videoUrl: '/videos/complex-tenses.mp4',
        materials: ['tenses-guide.pdf', 'exercises.pdf']
      },
      {
        title: 'Câu điều kiện nâng cao',
        description: 'Các loại câu điều kiện phức tạp và cách áp dụng',
        duration: 90,
        videoUrl: '/videos/conditionals.mp4',
        materials: ['conditionals.pdf', 'practice.pdf']
      }
    ],
    requirements: ['Đã có nền tảng ngữ pháp cơ bản', 'Trình độ trung cấp trở lên'],
    objectives: [
      'Nắm vững ngữ pháp tiếng Anh nâng cao',
      'Sử dụng chính xác các cấu trúc phức tạp',
      'Cải thiện kỹ năng viết và nói'
    ],
    isPublished: true,
    rating: 4.6,
    totalRatings: 67,
    totalStudents: 420
  },
  {
    title: 'Tiếng Anh Thương Mại Cơ Bản',
    description: 'Học tiếng Anh thương mại cơ bản cho môi trường công việc.',
    thumbnail: '/images/courses/business-english.jpg',
    price: 549000,
    originalPrice: 799000,
    level: 'intermediate',
    duration: 1600, // 27 hours
    language: 'vietnamese',
    category: 'business',
    tags: ['thương mại', 'business', 'công việc', 'email'],
    curriculum: [
      {
        title: 'Email thương mại',
        description: 'Cách viết email chuyên nghiệp trong công việc',
        duration: 75,
        videoUrl: '/videos/business-email.mp4',
        materials: ['email-templates.pdf', 'examples.pdf']
      },
      {
        title: 'Thuyết trình bằng tiếng Anh',
        description: 'Kỹ năng thuyết trình hiệu quả bằng tiếng Anh',
        duration: 90,
        videoUrl: '/videos/presentation.mp4',
        materials: ['presentation-tips.pdf', 'slides-template.pptx']
      }
    ],
    requirements: ['Tiếng Anh trung cấp', 'Đang làm việc hoặc chuẩn bị đi làm'],
    objectives: [
      'Giao tiếp hiệu quả trong môi trường công việc',
      'Viết email và báo cáo chuyên nghiệp',
      'Thuyết trình tự tin bằng tiếng Anh'
    ],
    isPublished: false, // Draft course
    rating: 0,
    totalRatings: 0,
    totalStudents: 0
  }
];

export async function seedCourses(users: any[]) {
  try {
    // Clear existing courses (optional)
    // await Course.deleteMany({});
    
    const courses = [];
    
    // Find teachers to assign as instructors
    const teachers = users.filter(user => user.role === 'teacher');
    
    for (let i = 0; i < sampleCourses.length; i++) {
      const courseData = sampleCourses[i];
      
      // Check if course already exists
      const existingCourse = await Course.findOne({ title: courseData.title });
      if (existingCourse) {
        console.log(`Course "${courseData.title}" already exists, skipping...`);
        courses.push(existingCourse);
        continue;
      }

      // Assign a random teacher as instructor
      const instructor = teachers[i % teachers.length];
      
      const course = new Course({
        ...courseData,
        instructor: instructor._id,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      await course.save();
      courses.push(course);
      console.log(`✅ Created course: ${courseData.title}`);
    }
    
    return courses;
  } catch (error) {
    console.error('Error seeding courses:', error);
    throw error;
  }
}
