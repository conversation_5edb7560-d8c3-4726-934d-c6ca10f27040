// Set environment variables directly for seeding
process.env.MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/e-lms';

import Course from '@/models/Course';
import User from '@/models/User';
import connectDB from '@/lib/mongodb';

const sampleCourses = [
  {
    title: 'Tiếng Anh Cơ Bản cho Người Mới Bắt Đầu',
    description: '<PERSON>h<PERSON><PERSON> học tiếng Anh cơ bản dành cho người mới bắt đầu. H<PERSON><PERSON> từ vựng, ngữ pháp và giao tiếp cơ bản.',
    shortDescription: 'Kh<PERSON>a học tiếng Anh cơ bản cho người mới bắt đầu với từ vựng và ngữ pháp cơ bản.',
    thumbnail: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=500',
    price: 299000,
    level: 'beginner',
    duration: 20, // 20 hours
    language: 'en',
    category: 'comprehensive',
    tags: ['c<PERSON> bản', 'từ vựng', 'ngữ pháp', 'giao tiếp'],
    curriculum: [
      {
        title: 'Giới thiệu về tiếng Anh',
        description: 'Tổng quan về tiếng Anh và cách học hiệu quả',
        duration: 30,
        order: 1,
        isPreview: true,
        videoUrl: 'https://www.youtube.com/watch?v=example1',
        materials: ['https://example.com/slide1.pdf', 'https://example.com/vocabulary1.pdf']
      },
      {
        title: 'Bảng chữ cái và phát âm',
        description: 'Học bảng chữ cái tiếng Anh và cách phát âm chuẩn',
        duration: 45,
        order: 2,
        isPreview: false,
        videoUrl: 'https://www.youtube.com/watch?v=example2',
        materials: ['https://example.com/alphabet.pdf', 'https://example.com/pronunciation.mp3']
      },
      {
        title: 'Từ vựng cơ bản - Gia đình',
        description: 'Học từ vựng về gia đình và các mối quan hệ',
        duration: 40,
        order: 3,
        isPreview: false,
        videoUrl: 'https://www.youtube.com/watch?v=example3',
        materials: ['https://example.com/family-vocab.pdf']
      }
    ],
    requirements: ['Không cần kiến thức trước', 'Máy tính hoặc điện thoại có internet'],
    whatYouWillLearn: [
      'Nắm vững 500 từ vựng cơ bản',
      'Hiểu và sử dụng ngữ pháp cơ bản',
      'Giao tiếp đơn giản trong cuộc sống hàng ngày'
    ],
    isPublished: true,
    averageRating: 4.8,
    totalStudents: 1250
  },
  {
    title: 'Luyện Nghe IELTS 6.5+',
    description: 'Khóa học luyện nghe IELTS chuyên sâu, giúp đạt band 6.5 trở lên.',
    shortDescription: 'Khóa học luyện nghe IELTS chuyên sâu với các kỹ thuật làm bài hiệu quả.',
    thumbnail: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500',
    price: 599000,
    level: 'intermediate',
    duration: 30, // 30 hours
    language: 'en',
    category: 'listening',
    tags: ['IELTS', 'listening', 'band 6.5', 'luyện thi'],
    curriculum: [
      {
        title: 'Giới thiệu về IELTS Listening',
        description: 'Tổng quan về bài thi IELTS Listening và chiến lược làm bài',
        duration: 60,
        order: 1,
        isPreview: true,
        videoUrl: 'https://www.youtube.com/watch?v=ielts-intro',
        materials: ['https://example.com/ielts-guide.pdf', 'https://example.com/strategies.pdf']
      },
      {
        title: 'Part 1: Conversation',
        description: 'Luyện tập Part 1 - Hội thoại trong cuộc sống hàng ngày',
        duration: 90,
        order: 2,
        isPreview: false,
        videoUrl: 'https://www.youtube.com/watch?v=part1',
        materials: ['https://example.com/part1-practice.pdf', 'https://example.com/audio-files.zip']
      },
      {
        title: 'Part 2: Monologue',
        description: 'Luyện tập Part 2 - Độc thoại về các chủ đề thường gặp',
        duration: 90,
        order: 3,
        isPreview: false,
        videoUrl: 'https://www.youtube.com/watch?v=part2',
        materials: ['https://example.com/part2-practice.pdf', 'https://example.com/monologue-samples.mp3']
      }
    ],
    requirements: ['Trình độ tiếng Anh trung cấp', 'Đã có kiến thức cơ bản về IELTS'],
    whatYouWillLearn: [
      'Đạt band 6.5+ trong IELTS Listening',
      'Nắm vững các kỹ thuật làm bài',
      'Cải thiện khả năng nghe hiểu tổng quát'
    ],
    isPublished: true,
    averageRating: 4.9,
    totalStudents: 650
  },
  {
    title: 'Tiếng Anh Giao Tiếp Thực Tế',
    description: 'Học tiếng Anh giao tiếp thực tế trong các tình huống hàng ngày.',
    shortDescription: 'Khóa học giao tiếp tiếng Anh thực tế cho cuộc sống hàng ngày.',
    thumbnail: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=500',
    price: 399000,
    level: 'intermediate',
    duration: 25, // 25 hours
    language: 'en',
    category: 'speaking',
    tags: ['giao tiếp', 'thực tế', 'speaking', 'pronunciation'],
    curriculum: [
      {
        title: 'Giới thiệu bản thân',
        description: 'Cách giới thiệu bản thân một cách tự nhiên và ấn tượng',
        duration: 45,
        order: 1,
        isPreview: true,
        videoUrl: 'https://www.youtube.com/watch?v=introduction',
        materials: ['https://example.com/self-intro.pdf', 'https://example.com/examples.mp3']
      },
      {
        title: 'Giao tiếp tại cửa hàng',
        description: 'Các câu giao tiếp khi mua sắm và thanh toán',
        duration: 60,
        order: 2,
        isPreview: false,
        videoUrl: 'https://www.youtube.com/watch?v=shopping',
        materials: ['https://example.com/shopping-phrases.pdf', 'https://example.com/dialogues.mp3']
      },
      {
        title: 'Hỏi đường và chỉ đường',
        description: 'Cách hỏi đường và chỉ đường một cách lịch sự',
        duration: 50,
        order: 3,
        isPreview: false,
        videoUrl: 'https://www.youtube.com/watch?v=directions',
        materials: ['https://example.com/directions.pdf', 'https://example.com/map-vocab.pdf']
      }
    ],
    requirements: ['Biết tiếng Anh cơ bản', 'Muốn cải thiện khả năng giao tiếp'],
    whatYouWillLearn: [
      'Giao tiếp tự tin trong cuộc sống hàng ngày',
      'Phát âm chuẩn và tự nhiên',
      'Hiểu và sử dụng các cụm từ thông dụng'
    ],
    isPublished: true,
    averageRating: 4.7,
    totalStudents: 890
  },
  {
    title: 'Ngữ Pháp Tiếng Anh Nâng Cao',
    description: 'Khóa học ngữ pháp tiếng Anh nâng cao cho người muốn hoàn thiện kiến thức.',
    shortDescription: 'Khóa học ngữ pháp tiếng Anh nâng cao với các cấu trúc phức tạp.',
    thumbnail: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500',
    price: 449000,
    level: 'advanced',
    duration: 33, // 33 hours
    language: 'en',
    category: 'comprehensive',
    tags: ['ngữ pháp', 'nâng cao', 'grammar', 'advanced'],
    curriculum: [
      {
        title: 'Các thì phức tạp',
        description: 'Học các thì phức tạp và cách sử dụng chính xác',
        duration: 120,
        order: 1,
        isPreview: true,
        videoUrl: 'https://www.youtube.com/watch?v=complex-tenses',
        materials: ['https://example.com/tenses-guide.pdf', 'https://example.com/exercises.pdf']
      },
      {
        title: 'Câu điều kiện nâng cao',
        description: 'Các loại câu điều kiện phức tạp và cách áp dụng',
        duration: 90,
        order: 2,
        isPreview: false,
        videoUrl: 'https://www.youtube.com/watch?v=conditionals',
        materials: ['https://example.com/conditionals.pdf', 'https://example.com/practice.pdf']
      }
    ],
    requirements: ['Đã có nền tảng ngữ pháp cơ bản', 'Trình độ trung cấp trở lên'],
    whatYouWillLearn: [
      'Nắm vững ngữ pháp tiếng Anh nâng cao',
      'Sử dụng chính xác các cấu trúc phức tạp',
      'Cải thiện kỹ năng viết và nói'
    ],
    isPublished: true,
    averageRating: 4.6,
    totalStudents: 420
  },
  {
    title: 'Tiếng Anh Thương Mại Cơ Bản',
    description: 'Học tiếng Anh thương mại cơ bản cho môi trường công việc.',
    shortDescription: 'Khóa học tiếng Anh thương mại cơ bản cho môi trường công việc chuyên nghiệp.',
    thumbnail: 'https://images.unsplash.com/photo-1507679799987-c73779587ccf?w=500',
    price: 549000,
    level: 'intermediate',
    duration: 27, // 27 hours
    language: 'en',
    category: 'comprehensive',
    tags: ['thương mại', 'business', 'công việc', 'email'],
    curriculum: [
      {
        title: 'Email thương mại',
        description: 'Cách viết email chuyên nghiệp trong công việc',
        duration: 75,
        order: 1,
        isPreview: true,
        videoUrl: 'https://www.youtube.com/watch?v=business-email',
        materials: ['https://example.com/email-templates.pdf', 'https://example.com/examples.pdf']
      },
      {
        title: 'Thuyết trình bằng tiếng Anh',
        description: 'Kỹ năng thuyết trình hiệu quả bằng tiếng Anh',
        duration: 90,
        order: 2,
        isPreview: false,
        videoUrl: 'https://www.youtube.com/watch?v=presentation',
        materials: ['https://example.com/presentation-tips.pdf', 'https://example.com/slides-template.pptx']
      }
    ],
    requirements: ['Tiếng Anh trung cấp', 'Đang làm việc hoặc chuẩn bị đi làm'],
    whatYouWillLearn: [
      'Giao tiếp hiệu quả trong môi trường công việc',
      'Viết email và báo cáo chuyên nghiệp',
      'Thuyết trình tự tin bằng tiếng Anh'
    ],
    isPublished: false, // Draft course
    averageRating: 0,
    totalStudents: 0
  }
];

export async function seedCourses(users: any[]) {
  try {
    // Clear existing courses (optional)
    // await Course.deleteMany({});
    
    const courses = [];
    
    // Find teachers to assign as instructors
    const teachers = users.filter(user => user.role === 'teacher');
    
    for (let i = 0; i < sampleCourses.length; i++) {
      const courseData = sampleCourses[i];
      
      // Check if course already exists
      const existingCourse = await Course.findOne({ title: courseData.title });
      if (existingCourse) {
        console.log(`Course "${courseData.title}" already exists, skipping...`);
        courses.push(existingCourse);
        continue;
      }

      // Assign a random teacher as instructor
      const instructor = teachers[i % teachers.length];
      
      const course = new Course({
        ...courseData,
        teacherId: instructor._id,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      await course.save();
      courses.push(course);
      console.log(`✅ Created course: ${courseData.title}`);
    }
    
    return courses;
  } catch (error) {
    console.error('Error seeding courses:', error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  async function runCourseSeeder() {
    try {
      await connectDB();
      console.log('✅ Connected to MongoDB');

      // Get users first
      const users = await User.find({});
      console.log(`Found ${users.length} users in database`);

      const courses = await seedCourses(users);
      console.log(`🎉 Successfully seeded ${courses.length} courses`);
      process.exit(0);
    } catch (error) {
      console.error('❌ Error running course seeder:', error);
      process.exit(1);
    }
  }
  runCourseSeeder();
}
