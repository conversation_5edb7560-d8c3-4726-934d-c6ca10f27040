import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Comment from '@/models/Comment';
import Course from '@/models/Course';
import Enrollment from '@/models/Enrollment';
import { getAuthUser } from '@/lib/auth';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = params;
    const { searchParams } = new URL(request.url);
    const lessonId = searchParams.get('lessonId');
    const type = searchParams.get('type') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build filter
    const filter: any = {
      courseId: id,
      isDeleted: false,
      parentId: null // Only get top-level comments
    };

    if (lessonId) {
      filter.lessonId = lessonId;
    }

    if (type !== 'all') {
      filter.type = type;
    }

    // Build sort
    const sort: any = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get comments
    const comments = await Comment.find(filter)
      .populate('userId', 'name avatar role')
      .populate('moderatedBy', 'name')
      .sort(sort)
      .skip(skip)
      .limit(limit);

    // Get replies for each comment
    const commentsWithReplies = await Promise.all(
      comments.map(async (comment) => {
        const replies = await Comment.find({
          parentId: comment._id,
          isDeleted: false
        })
        .populate('userId', 'name avatar role')
        .sort({ createdAt: 1 })
        .limit(10); // Limit replies to prevent huge responses

        return {
          ...comment.toJSON(),
          replies,
          replyCount: replies.length
        };
      })
    );

    // Get total count
    const total = await Comment.countDocuments(filter);
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: commentsWithReplies,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Get comments error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi tải bình luận',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Check if user has access to course
    const course = await Course.findById(id);
    if (!course) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy khóa học'
      }, { status: 404 });
    }

    // Check enrollment for students
    if (user.role === 'student') {
      const enrollment = await Enrollment.findOne({
        studentId: user.userId,
        courseId: id
      });

      if (!enrollment) {
        return NextResponse.json({
          success: false,
          message: 'Bạn cần đăng ký khóa học để bình luận'
        }, { status: 403 });
      }
    }

    const body = await request.json();
    const { content, type, lessonId, parentId } = body;

    // Validation
    if (!content || content.trim().length < 3) {
      return NextResponse.json({
        success: false,
        message: 'Nội dung bình luận phải có ít nhất 3 ký tự'
      }, { status: 400 });
    }

    if (content.length > 2000) {
      return NextResponse.json({
        success: false,
        message: 'Nội dung bình luận không được vượt quá 2000 ký tự'
      }, { status: 400 });
    }

    // Check if parent comment exists (for replies)
    if (parentId) {
      const parentComment = await Comment.findById(parentId);
      if (!parentComment || parentComment.isDeleted) {
        return NextResponse.json({
          success: false,
          message: 'Bình luận gốc không tồn tại'
        }, { status: 404 });
      }
    }

    // Determine if user is instructor
    const isInstructor = user.role === 'teacher' && user.userId === course.instructor.toString();

    // Create comment
    const comment = new Comment({
      userId: user.userId,
      courseId: id,
      lessonId,
      content: content.trim(),
      type: type || 'comment',
      parentId: parentId || null,
      isInstructor
    });

    await comment.save();

    // Populate user info
    await comment.populate('userId', 'name avatar role');

    return NextResponse.json({
      success: true,
      message: 'Đã thêm bình luận',
      data: comment
    }, { status: 201 });

  } catch (error) {
    console.error('Create comment error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi tạo bình luận',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
