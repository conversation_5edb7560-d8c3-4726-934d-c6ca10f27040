"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext_float16 = void 0;
const base_config_1 = require("./base-config");
const es2015_iterable_1 = require("./es2015.iterable");
const es2015_symbol_1 = require("./es2015.symbol");
exports.esnext_float16 = {
    libs: [es2015_symbol_1.es2015_symbol, es2015_iterable_1.es2015_iterable],
    variables: [
        ['Float16Array', base_config_1.TYPE_VALUE],
        ['Float16ArrayConstructor', base_config_1.TYPE],
        ['Math', base_config_1.TYPE],
        ['DataView', base_config_1.TYPE],
    ],
};
