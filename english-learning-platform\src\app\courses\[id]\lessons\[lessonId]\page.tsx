"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { 
  ChevronLeft, 
  ChevronRight, 
  Download, 
  FileText, 
  CheckCircle, 
  Clock,
  BookOpen,
  MessageSquare,
  Star
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { LoadingPage } from "@/components/ui/loading";
import { ErrorPage } from "@/components/ui/error";
import Header from "@/components/layout/Header";
import VideoPlayer from "@/components/course/VideoPlayer";
import { formatDuration } from "@/lib/utils";
import { toast } from "sonner";

interface LessonPageProps {
  params: {
    id: string;
    lessonId: string;
  };
}

// Interfaces
interface Lesson {
  _id: string;
  title: string;
  description: string;
  videoUrl?: string;
  duration: number;
  order: number;
  isPreview: boolean;
  materials?: string[];
  transcript?: string;
  objectives?: string[];
}

interface Course {
  _id: string;
  title: string;
  curriculum: Lesson[];
}

interface LessonProgress {
  lessonId: string;
  completedAt?: Date;
  timeSpent: number;
  videoProgress: number;
  isCompleted: boolean;
  bookmarks?: Array<{
    timestamp: number;
    note: string;
    id: string;
  }>;
  notes?: string;
}
    "Hiểu được 50 từ vựng cơ bản",
    "Sử dụng từ vựng trong câu đơn giản",
    "Phát âm chính xác các từ đã học"
  ],
  quiz: {
    questions: [
      {
        question: "What does 'hello' mean?",
        options: ["Xin chào", "Tạm biệt", "Cảm ơn", "Xin lỗi"],
        correctAnswer: "Xin chào",
        explanation: "'Hello' có nghĩa là 'Xin chào' trong tiếng Việt."
      }
    ]
  },
  progress: {
    timeSpent: 300,
    videoProgress: 45,
    isCompleted: false,
    quizScore: null,
    notes: "Ghi chú của tôi về bài học này...",
    bookmarks: [
      {
        timestamp: 120,
        note: "Phần quan trọng về phát âm",
        createdAt: new Date()
      }
    ]
  },
  courseInfo: {
    id: "course-1",
    title: "Tiếng Anh cơ bản cho người mới bắt đầu",
    instructor: "Nguyễn Văn A"
  }
};

export default function LessonPage({ params }: LessonPageProps) {
  const router = useRouter();
  const [lesson, setLesson] = useState(mockLesson);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [notes, setNotes] = useState(lesson.progress?.notes || "");
  const [isSavingProgress, setIsSavingProgress] = useState(false);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const breadcrumbItems = [
    { label: "Khóa học", href: "/courses" },
    { label: lesson.courseInfo.title, href: `/courses/${lesson.courseInfo.id}` },
    { label: lesson.title, current: true }
  ];

  const handleProgressUpdate = async (progress: number, timeSpent: number) => {
    if (isSavingProgress) return;

    setIsSavingProgress(true);
    
    try {
      // TODO: Call API to update progress
      console.log("Updating progress:", { progress, timeSpent });
      
      // Update local state
      setLesson(prev => ({
        ...prev,
        progress: {
          ...prev.progress!,
          videoProgress: progress,
          timeSpent: timeSpent,
          isCompleted: progress >= 90
        }
      }));

    } catch (error) {
      console.error("Progress update error:", error);
    } finally {
      setIsSavingProgress(false);
    }
  };

  const handleBookmark = async (timestamp: number, note: string) => {
    try {
      // TODO: Call API to add bookmark
      console.log("Adding bookmark:", { timestamp, note });
      
      const newBookmark = {
        timestamp,
        note,
        createdAt: new Date()
      };

      setLesson(prev => ({
        ...prev,
        progress: {
          ...prev.progress!,
          bookmarks: [...(prev.progress?.bookmarks || []), newBookmark]
        }
      }));

      toast.success("Đã thêm bookmark");
    } catch (error) {
      console.error("Bookmark error:", error);
      toast.error("Lỗi khi thêm bookmark");
    }
  };

  const handleSaveNotes = async () => {
    try {
      // TODO: Call API to save notes
      console.log("Saving notes:", notes);
      
      setLesson(prev => ({
        ...prev,
        progress: {
          ...prev.progress!,
          notes
        }
      }));

      toast.success("Đã lưu ghi chú");
    } catch (error) {
      console.error("Save notes error:", error);
      toast.error("Lỗi khi lưu ghi chú");
    }
  };

  const handleCompleteLesson = async () => {
    try {
      // TODO: Call API to mark lesson as completed
      console.log("Completing lesson");
      
      setLesson(prev => ({
        ...prev,
        progress: {
          ...prev.progress!,
          isCompleted: true
        }
      }));

      toast.success("Đã hoàn thành bài học!");
    } catch (error) {
      console.error("Complete lesson error:", error);
      toast.error("Lỗi khi hoàn thành bài học");
    }
  };

  if (isLoading) {
    return <LoadingPage message="Đang tải bài học..." />;
  }

  if (error) {
    return <ErrorPage title="Không thể tải bài học" message={error} />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Breadcrumb items={breadcrumbItems} className="mb-6" />
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Video Player */}
            <Card>
              <CardContent className="p-0">
                <VideoPlayer
                  src={lesson.videoUrl}
                  title={lesson.title}
                  duration={lesson.duration * 60}
                  initialProgress={lesson.progress?.videoProgress || 0}
                  onProgressUpdate={handleProgressUpdate}
                  onBookmark={handleBookmark}
                  bookmarks={lesson.progress?.bookmarks || []}
                  allowDownload={true}
                />
              </CardContent>
            </Card>

            {/* Lesson Content */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-2xl">{lesson.title}</CardTitle>
                    <CardDescription className="mt-2">
                      {lesson.description}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">
                      <Clock className="h-3 w-3 mr-1" />
                      {formatDuration(lesson.duration)}
                    </Badge>
                    {lesson.progress?.isCompleted && (
                      <Badge variant="default">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Hoàn thành
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="overview">Tổng quan</TabsTrigger>
                    <TabsTrigger value="materials">Tài liệu</TabsTrigger>
                    <TabsTrigger value="transcript">Transcript</TabsTrigger>
                    <TabsTrigger value="quiz">Bài tập</TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="space-y-4">
                    <div>
                      <h3 className="font-medium text-gray-900 mb-3">Mục tiêu học tập</h3>
                      <ul className="space-y-2">
                        {lesson.objectives.map((objective, index) => (
                          <li key={index} className="flex items-start">
                            <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700">{objective}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h3 className="font-medium text-gray-900 mb-3">Tiến độ học tập</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Video đã xem</span>
                          <span>{Math.round(lesson.progress?.videoProgress || 0)}%</span>
                        </div>
                        <Progress value={lesson.progress?.videoProgress || 0} />
                        <div className="text-sm text-gray-600">
                          Thời gian học: {formatDuration(Math.floor((lesson.progress?.timeSpent || 0) / 60))}
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="materials" className="space-y-4">
                    <div>
                      <h3 className="font-medium text-gray-900 mb-3">Tài liệu tham khảo</h3>
                      {lesson.materials.length > 0 ? (
                        <div className="space-y-2">
                          {lesson.materials.map((material, index) => (
                            <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                              <div className="flex items-center">
                                <FileText className="h-4 w-4 text-blue-600 mr-2" />
                                <span className="text-sm">Tài liệu {index + 1}</span>
                              </div>
                              <Button size="sm" variant="outline">
                                <Download className="h-3 w-3 mr-1" />
                                Tải xuống
                              </Button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 text-center py-8">
                          Chưa có tài liệu tham khảo
                        </p>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="transcript" className="space-y-4">
                    <div>
                      <h3 className="font-medium text-gray-900 mb-3">Transcript</h3>
                      {lesson.transcript ? (
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                            {lesson.transcript}
                          </p>
                        </div>
                      ) : (
                        <p className="text-gray-500 text-center py-8">
                          Transcript chưa có sẵn
                        </p>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="quiz" className="space-y-4">
                    <div>
                      <h3 className="font-medium text-gray-900 mb-3">Bài tập kiểm tra</h3>
                      {lesson.quiz?.questions.length > 0 ? (
                        <div className="space-y-4">
                          {lesson.quiz.questions.map((question, index) => (
                            <Card key={index}>
                              <CardHeader>
                                <CardTitle className="text-lg">Câu {index + 1}</CardTitle>
                              </CardHeader>
                              <CardContent>
                                <p className="mb-4">{question.question}</p>
                                <div className="space-y-2">
                                  {question.options.map((option, optionIndex) => (
                                    <label key={optionIndex} className="flex items-center p-2 border rounded cursor-pointer hover:bg-gray-50">
                                      <input type="radio" name={`question-${index}`} className="mr-2" />
                                      <span>{option}</span>
                                    </label>
                                  ))}
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                          <Button className="w-full">Nộp bài</Button>
                        </div>
                      ) : (
                        <p className="text-gray-500 text-center py-8">
                          Chưa có bài tập cho bài học này
                        </p>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Notes */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Ghi chú của tôi
                </CardTitle>
              </CardHeader>
              <CardContent>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Thêm ghi chú cho bài học này..."
                  className="w-full p-3 border rounded-md resize-none"
                  rows={6}
                />
                <Button onClick={handleSaveNotes} className="w-full mt-3">
                  Lưu ghi chú
                </Button>
              </CardContent>
            </Card>

            {/* Bookmarks */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Bookmarks ({lesson.progress?.bookmarks?.length || 0})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {lesson.progress?.bookmarks && lesson.progress.bookmarks.length > 0 ? (
                  <div className="space-y-3">
                    {lesson.progress.bookmarks.map((bookmark, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <Badge variant="outline" className="text-xs">
                            {Math.floor(bookmark.timestamp / 60)}:{String(Math.floor(bookmark.timestamp % 60)).padStart(2, '0')}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-700">{bookmark.note}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">
                    Chưa có bookmark nào
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  {!lesson.progress?.isCompleted && (
                    <Button onClick={handleCompleteLesson} className="w-full">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Hoàn thành bài học
                    </Button>
                  )}
                  
                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="outline" size="sm">
                      <ChevronLeft className="h-4 w-4 mr-1" />
                      Bài trước
                    </Button>
                    <Button variant="outline" size="sm">
                      Bài sau
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
