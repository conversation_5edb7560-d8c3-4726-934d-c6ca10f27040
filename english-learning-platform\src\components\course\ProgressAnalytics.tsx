"use client";

import { useState } from "react";
import { 
  TrendingUp, 
  Clock, 
  Target, 
  Award, 
  BookOpen, 
  PlayCircle,
  CheckCircle,
  Calendar,
  BarChart3
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { formatDuration } from "@/lib/utils";

interface ProgressAnalyticsProps {
  courseId: string;
  enrollment: {
    progress: number;
    completedLessons: string[];
    totalTimeSpent: number;
    enrolledAt: Date;
    completedAt?: Date;
    lessonProgress: Array<{
      lessonId: string;
      timeSpent: number;
      videoProgress: number;
      isCompleted: boolean;
      completedAt?: Date;
    }>;
  };
  course: {
    curriculum: Array<{
      _id: string;
      title: string;
      duration: number;
      order: number;
    }>;
    totalDuration: number;
  };
}

export default function ProgressAnalytics({ 
  courseId, 
  enrollment, 
  course 
}: ProgressAnalyticsProps) {
  const [activeTab, setActiveTab] = useState("overview");

  // Calculate statistics
  const totalLessons = course.curriculum.length;
  const completedLessons = enrollment.completedLessons.length;
  const progressPercentage = enrollment.progress;
  const totalCourseDuration = course.totalDuration * 60; // Convert to seconds
  const timeSpentPercentage = totalCourseDuration > 0 
    ? Math.min((enrollment.totalTimeSpent / totalCourseDuration) * 100, 100)
    : 0;

  // Calculate average session time
  const sessionsCount = enrollment.lessonProgress.filter(lp => lp.timeSpent > 0).length;
  const averageSessionTime = sessionsCount > 0 
    ? enrollment.totalTimeSpent / sessionsCount 
    : 0;

  // Calculate completion rate
  const daysEnrolled = Math.ceil(
    (Date.now() - new Date(enrollment.enrolledAt).getTime()) / (1000 * 60 * 60 * 24)
  );
  const expectedProgress = Math.min(daysEnrolled * 5, 100); // Assuming 5% progress per day
  const completionRate = expectedProgress > 0 ? (progressPercentage / expectedProgress) * 100 : 0;

  // Get recent activity (last 7 days)
  const recentActivity = enrollment.lessonProgress
    .filter(lp => lp.completedAt && 
      new Date(lp.completedAt).getTime() > Date.now() - (7 * 24 * 60 * 60 * 1000)
    )
    .sort((a, b) => new Date(b.completedAt!).getTime() - new Date(a.completedAt!).getTime());

  // Calculate weekly progress
  const weeklyProgress = Array.from({ length: 7 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const dayStart = new Date(date.setHours(0, 0, 0, 0));
    const dayEnd = new Date(date.setHours(23, 59, 59, 999));
    
    const dayActivity = enrollment.lessonProgress.filter(lp => 
      lp.completedAt && 
      new Date(lp.completedAt) >= dayStart && 
      new Date(lp.completedAt) <= dayEnd
    );

    return {
      date: dayStart,
      lessonsCompleted: dayActivity.length,
      timeSpent: dayActivity.reduce((sum, lp) => sum + lp.timeSpent, 0)
    };
  }).reverse();

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return "text-green-600";
    if (percentage >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getCompletionBadge = (percentage: number) => {
    if (percentage >= 90) return { label: "Xuất sắc", variant: "default" as const };
    if (percentage >= 70) return { label: "Tốt", variant: "default" as const };
    if (percentage >= 50) return { label: "Khá", variant: "secondary" as const };
    return { label: "Cần cải thiện", variant: "destructive" as const };
  };

  const completionBadge = getCompletionBadge(progressPercentage);

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tiến độ tổng thể</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getProgressColor(progressPercentage)}`}>
              {progressPercentage}%
            </div>
            <p className="text-xs text-muted-foreground">
              {completedLessons}/{totalLessons} bài học
            </p>
            <Progress value={progressPercentage} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Thời gian học</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDuration(Math.floor(enrollment.totalTimeSpent / 60))}
            </div>
            <p className="text-xs text-muted-foreground">
              {timeSpentPercentage.toFixed(1)}% tổng thời lượng
            </p>
            <Progress value={timeSpentPercentage} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Phiên học TB</CardTitle>
            <PlayCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDuration(Math.floor(averageSessionTime / 60))}
            </div>
            <p className="text-xs text-muted-foreground">
              {sessionsCount} phiên học
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tỷ lệ hoàn thành</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.min(completionRate, 100).toFixed(0)}%
            </div>
            <p className="text-xs text-muted-foreground">
              So với kế hoạch
            </p>
            <Badge variant={completionBadge.variant} className="mt-2">
              {completionBadge.label}
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Card>
        <CardHeader>
          <CardTitle>Phân tích chi tiết</CardTitle>
          <CardDescription>
            Theo dõi tiến độ học tập và hiệu suất của bạn
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Tổng quan</TabsTrigger>
              <TabsTrigger value="lessons">Bài học</TabsTrigger>
              <TabsTrigger value="activity">Hoạt động</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Progress Chart */}
              <div>
                <h3 className="text-lg font-medium mb-4">Tiến độ 7 ngày qua</h3>
                <div className="space-y-3">
                  {weeklyProgress.map((day, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-16 text-sm text-gray-600">
                          {day.date.toLocaleDateString('vi-VN', { weekday: 'short' })}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <div className="w-32">
                              <Progress 
                                value={day.lessonsCompleted > 0 ? (day.lessonsCompleted / 3) * 100 : 0} 
                                className="h-2"
                              />
                            </div>
                            <span className="text-sm text-gray-600">
                              {day.lessonsCompleted} bài
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="text-sm text-gray-600">
                        {formatDuration(Math.floor(day.timeSpent / 60))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Learning Insights */}
              <div>
                <h3 className="text-lg font-medium mb-4">Thông tin học tập</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Calendar className="h-4 w-4 text-blue-600" />
                      <span className="font-medium text-blue-900">Thời gian học</span>
                    </div>
                    <p className="text-sm text-blue-800">
                      Bạn đã học {daysEnrolled} ngày kể từ khi đăng ký. 
                      Trung bình {(enrollment.totalTimeSpent / daysEnrolled / 60).toFixed(1)} phút/ngày.
                    </p>
                  </div>

                  <div className="p-4 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Award className="h-4 w-4 text-green-600" />
                      <span className="font-medium text-green-900">Thành tích</span>
                    </div>
                    <p className="text-sm text-green-800">
                      {progressPercentage >= 50 
                        ? "Bạn đang học rất tốt! Tiếp tục duy trì nhịp độ này."
                        : "Hãy dành thêm thời gian để đạt được mục tiêu học tập."
                      }
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="lessons" className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-4">Tiến độ từng bài học</h3>
                <div className="space-y-3">
                  {course.curriculum.map((lesson) => {
                    const lessonProgress = enrollment.lessonProgress.find(
                      lp => lp.lessonId === lesson._id
                    );
                    const isCompleted = enrollment.completedLessons.includes(lesson._id);

                    return (
                      <div key={lesson._id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            isCompleted ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'
                          }`}>
                            {isCompleted ? (
                              <CheckCircle className="h-4 w-4" />
                            ) : (
                              <span className="text-sm font-medium">{lesson.order}</span>
                            )}
                          </div>
                          <div>
                            <div className="font-medium">{lesson.title}</div>
                            <div className="text-sm text-gray-600">
                              {formatDuration(lesson.duration)}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          {lessonProgress ? (
                            <div>
                              <div className="text-sm font-medium">
                                {lessonProgress.videoProgress.toFixed(0)}%
                              </div>
                              <div className="text-xs text-gray-600">
                                {formatDuration(Math.floor(lessonProgress.timeSpent / 60))}
                              </div>
                            </div>
                          ) : (
                            <div className="text-sm text-gray-400">Chưa bắt đầu</div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="activity" className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-4">Hoạt động gần đây</h3>
                {recentActivity.length > 0 ? (
                  <div className="space-y-3">
                    {recentActivity.map((activity, index) => {
                      const lesson = course.curriculum.find(l => l._id === activity.lessonId);
                      return (
                        <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <div className="flex-1">
                            <div className="font-medium">{lesson?.title}</div>
                            <div className="text-sm text-gray-600">
                              Hoàn thành vào {new Date(activity.completedAt!).toLocaleDateString('vi-VN')}
                            </div>
                          </div>
                          <Badge variant="outline">
                            {formatDuration(Math.floor(activity.timeSpent / 60))}
                          </Badge>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <BookOpen className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>Chưa có hoạt động nào trong 7 ngày qua</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
