module.exports = {

"[project]/.next-internal/server/app/api/courses/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Vui lòng định nghĩa biến môi trường MONGODB_URI trong .env.local');
}
/**
 * Global được sử dụng để duy trì kết nối cached trong môi trường development.
 * Điều này ngăn chặn việc tạo quá nhiều kết nối trong quá trình hot reloading.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            console.log('✅ Kết nối MongoDB thành công');
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        console.error('❌ Lỗi kết nối MongoDB:', e);
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[project]/src/types/index.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Enum types
__turbopack_context__.s({
    "CourseCategory": (()=>CourseCategory),
    "CourseLevel": (()=>CourseLevel),
    "PaymentStatus": (()=>PaymentStatus),
    "QuestionType": (()=>QuestionType),
    "TestType": (()=>TestType),
    "UserRole": (()=>UserRole)
});
var UserRole = /*#__PURE__*/ function(UserRole) {
    UserRole["ADMIN"] = "admin";
    UserRole["TEACHER"] = "teacher";
    UserRole["STUDENT"] = "student";
    return UserRole;
}({});
var CourseCategory = /*#__PURE__*/ function(CourseCategory) {
    CourseCategory["LISTENING"] = "listening";
    CourseCategory["SPEAKING"] = "speaking";
    CourseCategory["READING"] = "reading";
    CourseCategory["WRITING"] = "writing";
    CourseCategory["COMPREHENSIVE"] = "comprehensive";
    return CourseCategory;
}({});
var CourseLevel = /*#__PURE__*/ function(CourseLevel) {
    CourseLevel["BEGINNER"] = "beginner";
    CourseLevel["INTERMEDIATE"] = "intermediate";
    CourseLevel["ADVANCED"] = "advanced";
    return CourseLevel;
}({});
var TestType = /*#__PURE__*/ function(TestType) {
    TestType["VOCABULARY"] = "vocabulary";
    TestType["GRAMMAR"] = "grammar";
    TestType["LISTENING"] = "listening";
    TestType["SPEAKING"] = "speaking";
    TestType["READING"] = "reading";
    TestType["WRITING"] = "writing";
    TestType["COMPREHENSIVE"] = "comprehensive";
    TestType["PRACTICE"] = "practice";
    return TestType;
}({});
var QuestionType = /*#__PURE__*/ function(QuestionType) {
    QuestionType["MULTIPLE_CHOICE"] = "multiple_choice";
    QuestionType["FILL_IN_BLANK"] = "fill_in_blank";
    QuestionType["TRUE_FALSE"] = "true_false";
    QuestionType["ESSAY"] = "essay";
    QuestionType["AUDIO_RESPONSE"] = "audio_response";
    QuestionType["DRAG_DROP"] = "drag_drop";
    QuestionType["MATCHING"] = "matching";
    // Enhanced question types for language skills
    QuestionType["LISTENING_MULTIPLE_CHOICE"] = "listening_multiple_choice";
    QuestionType["LISTENING_FILL_BLANK"] = "listening_fill_blank";
    QuestionType["SPEAKING_RECORD"] = "speaking_record";
    QuestionType["READING_COMPREHENSION"] = "reading_comprehension";
    QuestionType["WRITING_ESSAY"] = "writing_essay";
    QuestionType["WRITING_SHORT_ANSWER"] = "writing_short_answer";
    return QuestionType;
}({});
var PaymentStatus = /*#__PURE__*/ function(PaymentStatus) {
    PaymentStatus["PENDING"] = "pending";
    PaymentStatus["COMPLETED"] = "completed";
    PaymentStatus["FAILED"] = "failed";
    return PaymentStatus;
}({});
}}),
"[project]/src/models/Course.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-route] (ecmascript)");
;
;
const QuizQuestionSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    question: {
        type: String,
        required: [
            true,
            'Câu hỏi là bắt buộc'
        ],
        maxlength: [
            500,
            'Câu hỏi không được vượt quá 500 ký tự'
        ]
    },
    options: [
        {
            type: String,
            required: [
                true,
                'Lựa chọn là bắt buộc'
            ],
            maxlength: [
                200,
                'Lựa chọn không được vượt quá 200 ký tự'
            ]
        }
    ],
    correctAnswer: {
        type: String,
        required: [
            true,
            'Đáp án đúng là bắt buộc'
        ]
    },
    explanation: {
        type: String,
        maxlength: [
            300,
            'Giải thích không được vượt quá 300 ký tự'
        ]
    }
});
const LessonSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    title: {
        type: String,
        required: [
            true,
            'Tiêu đề bài học là bắt buộc'
        ],
        trim: true,
        maxlength: [
            200,
            'Tiêu đề bài học không được vượt quá 200 ký tự'
        ]
    },
    description: {
        type: String,
        required: [
            true,
            'Mô tả bài học là bắt buộc'
        ],
        maxlength: [
            1000,
            'Mô tả bài học không được vượt quá 1000 ký tự'
        ]
    },
    videoUrl: {
        type: String,
        validate: {
            validator: function(v) {
                return !v || /^https?:\/\/.+/.test(v);
            },
            message: 'URL video không hợp lệ'
        }
    },
    materials: [
        {
            type: String,
            validate: {
                validator: function(v) {
                    return /^https?:\/\/.+/.test(v);
                },
                message: 'URL tài liệu không hợp lệ'
            }
        }
    ],
    duration: {
        type: Number,
        required: [
            true,
            'Thời lượng bài học là bắt buộc'
        ],
        min: [
            1,
            'Thời lượng bài học phải ít nhất 1 phút'
        ]
    },
    order: {
        type: Number,
        required: [
            true,
            'Thứ tự bài học là bắt buộc'
        ],
        min: [
            1,
            'Thứ tự bài học phải bắt đầu từ 1'
        ]
    },
    isPreview: {
        type: Boolean,
        default: false
    },
    transcript: {
        type: String,
        maxlength: [
            10000,
            'Transcript không được vượt quá 10000 ký tự'
        ]
    },
    objectives: [
        {
            type: String,
            maxlength: [
                200,
                'Mục tiêu học tập không được vượt quá 200 ký tự'
            ]
        }
    ],
    quiz: {
        questions: [
            QuizQuestionSchema
        ]
    }
});
const RatingSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    userId: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
        ref: 'User',
        required: true
    },
    rating: {
        type: Number,
        required: [
            true,
            'Đánh giá là bắt buộc'
        ],
        min: [
            1,
            'Đánh giá phải từ 1 đến 5'
        ],
        max: [
            5,
            'Đánh giá phải từ 1 đến 5'
        ]
    },
    comment: {
        type: String,
        maxlength: [
            500,
            'Nhận xét không được vượt quá 500 ký tự'
        ]
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
});
const CourseSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    title: {
        type: String,
        required: [
            true,
            'Tiêu đề khóa học là bắt buộc'
        ],
        trim: true,
        maxlength: [
            200,
            'Tiêu đề khóa học không được vượt quá 200 ký tự'
        ]
    },
    description: {
        type: String,
        required: [
            true,
            'Mô tả khóa học là bắt buộc'
        ],
        maxlength: [
            5000,
            'Mô tả khóa học không được vượt quá 5000 ký tự'
        ]
    },
    shortDescription: {
        type: String,
        required: [
            true,
            'Mô tả ngắn là bắt buộc'
        ],
        maxlength: [
            300,
            'Mô tả ngắn không được vượt quá 300 ký tự'
        ]
    },
    teacherId: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
        ref: 'User',
        required: [
            true,
            'Giáo viên là bắt buộc'
        ]
    },
    category: {
        type: String,
        enum: Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CourseCategory"]),
        required: [
            true,
            'Danh mục khóa học là bắt buộc'
        ]
    },
    level: {
        type: String,
        enum: Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CourseLevel"]),
        required: [
            true,
            'Trình độ khóa học là bắt buộc'
        ]
    },
    price: {
        type: Number,
        required: [
            true,
            'Giá khóa học là bắt buộc'
        ],
        min: [
            0,
            'Giá khóa học không được âm'
        ]
    },
    duration: {
        type: Number,
        required: [
            true,
            'Thời lượng khóa học là bắt buộc'
        ],
        min: [
            1,
            'Thời lượng khóa học phải ít nhất 1 giờ'
        ]
    },
    thumbnail: {
        type: String,
        validate: {
            validator: function(v) {
                return !v || /^https?:\/\/.+/.test(v);
            },
            message: 'URL hình ảnh không hợp lệ'
        }
    },
    videoIntro: {
        type: String,
        validate: {
            validator: function(v) {
                return !v || /^https?:\/\/.+/.test(v);
            },
            message: 'URL video giới thiệu không hợp lệ'
        }
    },
    isPublished: {
        type: Boolean,
        default: false
    },
    curriculum: [
        LessonSchema
    ],
    ratings: [
        RatingSchema
    ],
    averageRating: {
        type: Number,
        default: 0,
        min: 0,
        max: 5
    },
    totalStudents: {
        type: Number,
        default: 0,
        min: 0
    },
    tags: [
        {
            type: String,
            trim: true,
            maxlength: [
                50,
                'Tag không được vượt quá 50 ký tự'
            ]
        }
    ],
    requirements: [
        {
            type: String,
            maxlength: [
                200,
                'Yêu cầu không được vượt quá 200 ký tự'
            ]
        }
    ],
    whatYouWillLearn: [
        {
            type: String,
            maxlength: [
                200,
                'Mục tiêu học tập không được vượt quá 200 ký tự'
            ]
        }
    ],
    language: {
        type: String,
        default: 'vi',
        enum: [
            'vi',
            'en',
            'both'
        ]
    },
    subtitles: [
        {
            type: String,
            enum: [
                'vi',
                'en'
            ]
        }
    ]
}, {
    timestamps: true
});
// Indexes cho tìm kiếm và hiệu suất
// CourseSchema.index({ title: 'text', description: 'text' }, { default_language: 'none' });
CourseSchema.index({
    category: 1,
    level: 1
});
CourseSchema.index({
    teacherId: 1
});
CourseSchema.index({
    isPublished: 1
});
CourseSchema.index({
    averageRating: -1
});
CourseSchema.index({
    totalStudents: -1
});
CourseSchema.index({
    createdAt: -1
});
// Middleware để tính toán averageRating
CourseSchema.pre('save', function(next) {
    if (this.ratings && this.ratings.length > 0) {
        const totalRating = this.ratings.reduce((sum, rating)=>sum + rating.rating, 0);
        this.averageRating = Math.round(totalRating / this.ratings.length * 10) / 10;
    }
    next();
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Course || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Course', CourseSchema);
}}),
"[project]/src/models/Enrollment.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-route] (ecmascript)");
;
;
const BookmarkSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    timestamp: {
        type: Number,
        required: [
            true,
            'Timestamp là bắt buộc'
        ],
        min: [
            0,
            'Timestamp không được âm'
        ]
    },
    note: {
        type: String,
        required: [
            true,
            'Ghi chú là bắt buộc'
        ],
        maxlength: [
            500,
            'Ghi chú không được vượt quá 500 ký tự'
        ]
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
});
const LessonProgressSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    lessonId: {
        type: String,
        required: [
            true,
            'ID bài học là bắt buộc'
        ]
    },
    completedAt: {
        type: Date
    },
    timeSpent: {
        type: Number,
        default: 0,
        min: [
            0,
            'Thời gian không được âm'
        ]
    },
    videoProgress: {
        type: Number,
        default: 0,
        min: [
            0,
            'Tiến độ video không được âm'
        ],
        max: [
            100,
            'Tiến độ video không được vượt quá 100%'
        ]
    },
    quizScore: {
        type: Number,
        min: [
            0,
            'Điểm quiz không được âm'
        ],
        max: [
            100,
            'Điểm quiz không được vượt quá 100%'
        ]
    },
    notes: {
        type: String,
        maxlength: [
            2000,
            'Ghi chú không được vượt quá 2000 ký tự'
        ]
    },
    bookmarks: [
        BookmarkSchema
    ],
    isCompleted: {
        type: Boolean,
        default: false
    }
});
const EnrollmentSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    studentId: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
        ref: 'User',
        required: [
            true,
            'ID học viên là bắt buộc'
        ]
    },
    courseId: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
        ref: 'Course',
        required: [
            true,
            'ID khóa học là bắt buộc'
        ]
    },
    enrolledAt: {
        type: Date,
        default: Date.now
    },
    completedAt: {
        type: Date
    },
    progress: {
        type: Number,
        default: 0,
        min: [
            0,
            'Tiến độ không được âm'
        ],
        max: [
            100,
            'Tiến độ không được vượt quá 100%'
        ]
    },
    completedLessons: [
        {
            type: String
        }
    ],
    lessonProgress: [
        LessonProgressSchema
    ],
    lastAccessedAt: {
        type: Date,
        default: Date.now
    },
    paymentStatus: {
        type: String,
        enum: Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PaymentStatus"]),
        default: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PaymentStatus"].PENDING
    },
    paymentId: {
        type: String,
        sparse: true
    },
    paymentAmount: {
        type: Number,
        min: [
            0,
            'Số tiền thanh toán không được âm'
        ]
    },
    certificateIssued: {
        type: Boolean,
        default: false
    },
    certificateId: {
        type: String,
        sparse: true
    },
    totalTimeSpent: {
        type: Number,
        default: 0,
        min: [
            0,
            'Tổng thời gian học không được âm'
        ]
    },
    currentLesson: {
        type: String
    },
    rating: {
        type: Number,
        min: [
            1,
            'Đánh giá tối thiểu là 1 sao'
        ],
        max: [
            5,
            'Đánh giá tối đa là 5 sao'
        ]
    },
    review: {
        type: String,
        maxlength: [
            1000,
            'Đánh giá không được vượt quá 1000 ký tự'
        ]
    },
    reviewedAt: {
        type: Date
    }
}, {
    timestamps: true
});
// Compound index để đảm bảo một học viên chỉ đăng ký một khóa học một lần
EnrollmentSchema.index({
    studentId: 1,
    courseId: 1
}, {
    unique: true
});
// Indexes khác
EnrollmentSchema.index({
    studentId: 1
});
EnrollmentSchema.index({
    courseId: 1
});
EnrollmentSchema.index({
    paymentStatus: 1
});
EnrollmentSchema.index({
    enrolledAt: -1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Enrollment || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Enrollment', EnrollmentSchema);
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/models/User.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-route] (ecmascript)");
;
;
;
const UserSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    email: {
        type: String,
        required: [
            true,
            'Email là bắt buộc'
        ],
        unique: true,
        lowercase: true,
        trim: true,
        match: [
            /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
            'Email không hợp lệ'
        ]
    },
    password: {
        type: String,
        required: [
            true,
            'Mật khẩu là bắt buộc'
        ],
        minlength: [
            6,
            'Mật khẩu phải có ít nhất 6 ký tự'
        ]
    },
    name: {
        type: String,
        required: [
            true,
            'Tên là bắt buộc'
        ],
        trim: true,
        maxlength: [
            100,
            'Tên không được vượt quá 100 ký tự'
        ]
    },
    role: {
        type: String,
        enum: Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserRole"]),
        default: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserRole"].STUDENT
    },
    avatar: {
        type: String,
        default: null
    },
    phone: {
        type: String,
        match: [
            /^[0-9]{10,11}$/,
            'Số điện thoại không hợp lệ'
        ]
    },
    dateOfBirth: {
        type: Date
    },
    isEmailVerified: {
        type: Boolean,
        default: false
    },
    profile: {
        bio: {
            type: String,
            maxlength: [
                500,
                'Bio không được vượt quá 500 ký tự'
            ]
        },
        experience: {
            type: String,
            maxlength: [
                1000,
                'Kinh nghiệm không được vượt quá 1000 ký tự'
            ]
        },
        education: {
            type: String,
            maxlength: [
                500,
                'Học vấn không được vượt quá 500 ký tự'
            ]
        },
        skills: [
            {
                type: String,
                maxlength: [
                    50,
                    'Kỹ năng không được vượt quá 50 ký tự'
                ]
            }
        ]
    }
}, {
    timestamps: true
});
// Index cho tìm kiếm
UserSchema.index({
    email: 1
});
UserSchema.index({
    role: 1
});
// Hash password trước khi lưu
UserSchema.pre('save', async function(next) {
    if (!this.isModified('password')) return next();
    try {
        const salt = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].genSalt(12);
        this.password = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(this.password, salt);
        next();
    } catch (error) {
        next(error);
    }
});
// Method để so sánh password
UserSchema.methods.comparePassword = async function(candidatePassword) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(candidatePassword, this.password);
};
// Loại bỏ password khỏi JSON response
UserSchema.methods.toJSON = function() {
    const userObject = this.toObject();
    delete userObject.password;
    return userObject;
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.User || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('User', UserSchema);
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateToken": (()=>generateToken),
    "getAuthUser": (()=>getAuthUser),
    "getCurrentUser": (()=>getCurrentUser),
    "hashPassword": (()=>hashPassword),
    "requireAuth": (()=>requireAuth),
    "requireRole": (()=>requireRole),
    "verifyToken": (()=>verifyToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.ts [app-route] (ecmascript)");
;
;
;
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
async function verifyToken(token) {
    try {
        const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, JWT_SECRET);
        return decoded;
    } catch (error) {
        console.error('Token verification failed:', error);
        return null;
    }
}
async function getAuthUser(request) {
    try {
        // Try to get token from cookie first
        let token = request.cookies.get('auth-token')?.value;
        // If no cookie, try Authorization header
        if (!token) {
            const authHeader = request.headers.get('authorization');
            if (authHeader && authHeader.startsWith('Bearer ')) {
                token = authHeader.substring(7);
            }
        }
        if (!token) {
            return null;
        }
        return await verifyToken(token);
    } catch (error) {
        console.error('Get auth user failed:', error);
        return null;
    }
}
async function requireAuth(request) {
    const user = await getAuthUser(request);
    if (!user) {
        return {
            error: new Response(JSON.stringify({
                success: false,
                message: 'Unauthorized - Token required'
            }), {
                status: 401,
                headers: {
                    'Content-Type': 'application/json'
                }
            })
        };
    }
    return {
        user
    };
}
async function requireRole(request, allowedRoles) {
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
        return authResult;
    }
    if (!allowedRoles.includes(authResult.user.role)) {
        return {
            error: new Response(JSON.stringify({
                success: false,
                message: 'Forbidden - Insufficient permissions'
            }), {
                status: 403,
                headers: {
                    'Content-Type': 'application/json'
                }
            })
        };
    }
    return authResult;
}
async function getCurrentUser(userId) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(userId).select('-password');
        return user;
    } catch (error) {
        console.error('Get current user failed:', error);
        return null;
    }
}
function generateToken(payload) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign(payload, JWT_SECRET, {
        expiresIn: '7d'
    });
}
async function hashPassword(password) {
    const bcrypt = __turbopack_context__.r("[project]/node_modules/bcryptjs/umd/index.js [app-route] (ecmascript)");
    const salt = await bcrypt.genSalt(12);
    return bcrypt.hash(password, salt);
}
}}),
"[project]/src/app/api/courses/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Course$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Course.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Enrollment$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Enrollment.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-route] (ecmascript)");
;
;
;
;
;
;
async function GET(request) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '12');
        const category = searchParams.get('category');
        const level = searchParams.get('level');
        const search = searchParams.get('search');
        const sortBy = searchParams.get('sortBy') || 'createdAt';
        const sortOrder = searchParams.get('sortOrder') || 'desc';
        const priceMin = searchParams.get('priceMin');
        const priceMax = searchParams.get('priceMax');
        const instructor = searchParams.get('instructor');
        // Build filter object
        const filter = {
            isPublished: true
        };
        if (category && category !== 'all') {
            filter.category = category;
        }
        if (level && level !== 'all') {
            filter.level = level;
        }
        if (search) {
            filter.$or = [
                {
                    title: {
                        $regex: search,
                        $options: 'i'
                    }
                },
                {
                    description: {
                        $regex: search,
                        $options: 'i'
                    }
                },
                {
                    tags: {
                        $in: [
                            new RegExp(search, 'i')
                        ]
                    }
                }
            ];
        }
        if (priceMin || priceMax) {
            filter.price = {};
            if (priceMin) filter.price.$gte = parseFloat(priceMin);
            if (priceMax) filter.price.$lte = parseFloat(priceMax);
        }
        if (instructor) {
            filter.teacherId = instructor;
        }
        // Build sort object
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
        // Calculate pagination
        const skip = (page - 1) * limit;
        // Get courses with pagination
        const courses = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Course$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].find(filter).populate('teacherId', 'name email profile.avatar').sort(sort).skip(skip).limit(limit);
        // Get total count for pagination
        const total = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Course$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments(filter);
        const totalPages = Math.ceil(total / limit);
        // Get enrollment counts for each course
        const courseIds = courses.map((course)=>course._id);
        const enrollmentCounts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Enrollment$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $match: {
                    courseId: {
                        $in: courseIds
                    }
                }
            },
            {
                $group: {
                    _id: '$courseId',
                    count: {
                        $sum: 1
                    }
                }
            }
        ]);
        // Map enrollment counts to courses
        const coursesWithEnrollments = courses.map((course)=>{
            const enrollmentData = enrollmentCounts.find((item)=>item._id.toString() === course._id.toString());
            return {
                ...course.toJSON(),
                enrollmentCount: enrollmentData?.count || 0
            };
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: coursesWithEnrollments,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1
            }
        });
    } catch (error) {
        console.error('Get courses error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: 'Lỗi khi tải danh sách khóa học',
            error: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        // Check authentication and role
        const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAuthUser"])(request);
        if (!user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Unauthorized - Vui lòng đăng nhập'
            }, {
                status: 401
            });
        }
        if (user.role !== 'teacher' && user.role !== 'admin') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Forbidden - Chỉ giáo viên và admin mới có thể tạo khóa học'
            }, {
                status: 403
            });
        }
        const body = await request.json();
        const { title, description, category, level, price, thumbnail, curriculum, tags, requirements, whatYouWillLearn, language, subtitles } = body;
        // Validation
        const errors = {};
        if (!title || title.trim().length < 5) {
            errors.title = 'Tiêu đề phải có ít nhất 5 ký tự';
        }
        if (!description || description.trim().length < 20) {
            errors.description = 'Mô tả phải có ít nhất 20 ký tự';
        }
        if (!category || !Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CourseCategory"]).includes(category)) {
            errors.category = 'Danh mục khóa học không hợp lệ';
        }
        if (!level || !Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CourseLevel"]).includes(level)) {
            errors.level = 'Trình độ khóa học không hợp lệ';
        }
        if (!price || price < 0) {
            errors.price = 'Giá khóa học phải lớn hơn hoặc bằng 0';
        }
        if (!curriculum || !Array.isArray(curriculum) || curriculum.length === 0) {
            errors.curriculum = 'Phải có ít nhất 1 bài học';
        }
        if (Object.keys(errors).length > 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Dữ liệu không hợp lệ',
                errors
            }, {
                status: 400
            });
        }
        // Validate curriculum
        const curriculumErrors = [];
        curriculum.forEach((lesson, index)=>{
            if (!lesson.title || lesson.title.trim().length < 3) {
                curriculumErrors.push(`Bài ${index + 1}: Tiêu đề quá ngắn`);
            }
            if (!lesson.description || lesson.description.trim().length < 10) {
                curriculumErrors.push(`Bài ${index + 1}: Mô tả quá ngắn`);
            }
            if (!lesson.duration || lesson.duration <= 0) {
                curriculumErrors.push(`Bài ${index + 1}: Thời lượng không hợp lệ`);
            }
        });
        if (curriculumErrors.length > 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Curriculum không hợp lệ',
                errors: {
                    curriculum: curriculumErrors
                }
            }, {
                status: 400
            });
        }
        // Calculate total duration
        const totalDuration = curriculum.reduce((sum, lesson)=>sum + lesson.duration, 0);
        // Create course
        const course = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Course$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
            title: title.trim(),
            description: description.trim(),
            category,
            level,
            price,
            thumbnail,
            teacherId: user.userId,
            curriculum: curriculum.map((lesson, index)=>({
                    ...lesson,
                    order: index + 1
                })),
            totalDuration,
            tags: tags || [],
            requirements: requirements || [],
            whatYouWillLearn: whatYouWillLearn || [],
            language: language || 'vi',
            subtitles: subtitles || [],
            isPublished: false // Default to draft
        });
        await course.save();
        // Populate instructor info
        await course.populate('teacherId', 'name email profile.avatar');
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Tạo khóa học thành công',
            data: course
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Create course error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: 'Lỗi khi tạo khóa học',
            error: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__1c419363._.js.map