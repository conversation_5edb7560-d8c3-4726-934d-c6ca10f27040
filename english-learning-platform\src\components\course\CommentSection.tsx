"use client";

import { useState, useEffect } from "react";
import { 
  MessageSquare, 
  ThumbsUp, 
  ThumbsDown, 
  Reply, 
  Edit, 
  Trash2, 
  CheckCircle,
  AlertCircle,
  Send
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";

interface Comment {
  _id: string;
  userId: {
    _id: string;
    name: string;
    avatar?: string;
    role: string;
  };
  content: string;
  type: 'comment' | 'question' | 'answer';
  isInstructor: boolean;
  isResolved?: boolean;
  likes: string[];
  dislikes: string[];
  replies?: Comment[];
  replyCount: number;
  createdAt: string;
  editedAt?: string;
}

interface CommentSectionProps {
  courseId: string;
  lessonId?: string;
  currentUserId?: string;
  currentUserRole?: string;
}

export default function CommentSection({ 
  courseId, 
  lessonId, 
  currentUserId,
  currentUserRole 
}: CommentSectionProps) {
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");
  const [newComment, setNewComment] = useState("");
  const [commentType, setCommentType] = useState<'comment' | 'question'>('comment');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState("");
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editContent, setEditContent] = useState("");

  useEffect(() => {
    loadComments();
  }, [courseId, lessonId, activeTab]);

  const loadComments = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (lessonId) params.append('lessonId', lessonId);
      if (activeTab !== 'all') params.append('type', activeTab);

      const response = await fetch(`/api/courses/${courseId}/comments?${params.toString()}`);
      const data = await response.json();

      if (data.success) {
        setComments(data.data);
      } else {
        toast.error(data.message || 'Lỗi khi tải bình luận');
      }
    } catch (error) {
      console.error('Load comments error:', error);
      toast.error('Lỗi kết nối');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim()) {
      toast.error('Vui lòng nhập nội dung bình luận');
      return;
    }

    try {
      const response = await fetch(`/api/courses/${courseId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: newComment,
          type: commentType,
          lessonId
        }),
      });

      const data = await response.json();

      if (data.success) {
        setNewComment("");
        setCommentType('comment');
        toast.success('Đã thêm bình luận');
        loadComments();
      } else {
        toast.error(data.message || 'Lỗi khi thêm bình luận');
      }
    } catch (error) {
      console.error('Submit comment error:', error);
      toast.error('Lỗi kết nối');
    }
  };

  const handleSubmitReply = async (parentId: string) => {
    if (!replyContent.trim()) {
      toast.error('Vui lòng nhập nội dung trả lời');
      return;
    }

    try {
      const response = await fetch(`/api/courses/${courseId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: replyContent,
          type: 'answer',
          parentId,
          lessonId
        }),
      });

      const data = await response.json();

      if (data.success) {
        setReplyContent("");
        setReplyingTo(null);
        toast.success('Đã thêm trả lời');
        loadComments();
      } else {
        toast.error(data.message || 'Lỗi khi thêm trả lời');
      }
    } catch (error) {
      console.error('Submit reply error:', error);
      toast.error('Lỗi kết nối');
    }
  };

  const handleLike = async (commentId: string) => {
    try {
      const response = await fetch(`/api/courses/${courseId}/comments/${commentId}?action=like`, {
        method: 'POST',
      });

      const data = await response.json();

      if (data.success) {
        loadComments();
      } else {
        toast.error(data.message || 'Lỗi khi like');
      }
    } catch (error) {
      console.error('Like error:', error);
      toast.error('Lỗi kết nối');
    }
  };

  const handleResolveQuestion = async (commentId: string, isResolved: boolean) => {
    try {
      const response = await fetch(`/api/courses/${courseId}/comments/${commentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isResolved }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success(isResolved ? 'Đã đánh dấu đã giải quyết' : 'Đã đánh dấu chưa giải quyết');
        loadComments();
      } else {
        toast.error(data.message || 'Lỗi khi cập nhật');
      }
    } catch (error) {
      console.error('Resolve question error:', error);
      toast.error('Lỗi kết nối');
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Vừa xong';
    } else if (diffInHours < 24) {
      return `${diffInHours} giờ trước`;
    } else {
      return date.toLocaleDateString('vi-VN');
    }
  };

  const canEditComment = (comment: Comment) => {
    if (!currentUserId) return false;
    const isOwner = comment.userId._id === currentUserId;
    const isAdmin = currentUserRole === 'admin';
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
    const isRecent = new Date(comment.createdAt) > fifteenMinutesAgo;
    
    return (isOwner && isRecent) || isAdmin;
  };

  const canDeleteComment = (comment: Comment) => {
    if (!currentUserId) return false;
    const isOwner = comment.userId._id === currentUserId;
    const isAdmin = currentUserRole === 'admin';
    const isModerator = currentUserRole === 'teacher';
    
    return isOwner || isAdmin || isModerator;
  };

  const renderComment = (comment: Comment, isReply = false) => (
    <div key={comment._id} className={`${isReply ? 'ml-8 border-l-2 border-gray-200 pl-4' : ''}`}>
      <div className="flex gap-3">
        <Avatar className="w-8 h-8">
          <img 
            src={comment.userId.avatar || '/default-avatar.png'} 
            alt={comment.userId.name}
            className="w-full h-full object-cover"
          />
        </Avatar>
        
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-sm">{comment.userId.name}</span>
            {comment.isInstructor && (
              <Badge variant="default" className="text-xs">Giảng viên</Badge>
            )}
            {comment.type === 'question' && (
              <Badge variant={comment.isResolved ? "default" : "secondary"} className="text-xs">
                {comment.isResolved ? (
                  <>
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Đã giải quyết
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Câu hỏi
                  </>
                )}
              </Badge>
            )}
            <span className="text-xs text-gray-500">{formatDate(comment.createdAt)}</span>
            {comment.editedAt && (
              <span className="text-xs text-gray-400">(đã chỉnh sửa)</span>
            )}
          </div>
          
          <div className="text-sm text-gray-700 mb-2 whitespace-pre-wrap">
            {comment.content}
          </div>
          
          <div className="flex items-center gap-4 text-xs">
            <button
              onClick={() => handleLike(comment._id)}
              className="flex items-center gap-1 text-gray-500 hover:text-blue-600"
            >
              <ThumbsUp className="h-3 w-3" />
              {comment.likes.length}
            </button>
            
            {!isReply && (
              <button
                onClick={() => setReplyingTo(comment._id)}
                className="flex items-center gap-1 text-gray-500 hover:text-blue-600"
              >
                <Reply className="h-3 w-3" />
                Trả lời
              </button>
            )}
            
            {comment.type === 'question' && comment.isInstructor && (
              <button
                onClick={() => handleResolveQuestion(comment._id, !comment.isResolved)}
                className="flex items-center gap-1 text-gray-500 hover:text-green-600"
              >
                <CheckCircle className="h-3 w-3" />
                {comment.isResolved ? 'Bỏ đánh dấu' : 'Đánh dấu đã giải quyết'}
              </button>
            )}
            
            {canEditComment(comment) && (
              <button
                onClick={() => {
                  setEditingComment(comment._id);
                  setEditContent(comment.content);
                }}
                className="flex items-center gap-1 text-gray-500 hover:text-yellow-600"
              >
                <Edit className="h-3 w-3" />
                Sửa
              </button>
            )}
            
            {canDeleteComment(comment) && (
              <button
                onClick={() => {
                  if (confirm('Bạn có chắc chắn muốn xóa bình luận này?')) {
                    // TODO: Implement delete
                  }
                }}
                className="flex items-center gap-1 text-gray-500 hover:text-red-600"
              >
                <Trash2 className="h-3 w-3" />
                Xóa
              </button>
            )}
          </div>
          
          {/* Reply Form */}
          {replyingTo === comment._id && (
            <div className="mt-3 p-3 bg-gray-50 rounded-lg">
              <textarea
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                placeholder="Viết trả lời..."
                className="w-full p-2 border rounded-md resize-none"
                rows={3}
              />
              <div className="flex gap-2 mt-2">
                <Button size="sm" onClick={() => handleSubmitReply(comment._id)}>
                  <Send className="h-3 w-3 mr-1" />
                  Gửi
                </Button>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => {
                    setReplyingTo(null);
                    setReplyContent("");
                  }}
                >
                  Hủy
                </Button>
              </div>
            </div>
          )}
          
          {/* Replies */}
          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-3 space-y-3">
              {comment.replies.map(reply => renderComment(reply, true))}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <MessageSquare className="h-4 w-4 mr-2" />
          Thảo luận ({comments.length})
        </CardTitle>
        <CardDescription>
          Đặt câu hỏi và thảo luận về bài học
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Comment Form */}
        {currentUserId && (
          <div className="space-y-3">
            <div className="flex gap-2">
              <Button
                variant={commentType === 'comment' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCommentType('comment')}
              >
                Bình luận
              </Button>
              <Button
                variant={commentType === 'question' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCommentType('question')}
              >
                Đặt câu hỏi
              </Button>
            </div>
            
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder={commentType === 'question' ? 'Đặt câu hỏi...' : 'Viết bình luận...'}
              className="w-full p-3 border rounded-md resize-none"
              rows={3}
            />
            
            <Button onClick={handleSubmitComment}>
              <Send className="h-4 w-4 mr-2" />
              {commentType === 'question' ? 'Đặt câu hỏi' : 'Gửi bình luận'}
            </Button>
          </div>
        )}

        {/* Filter Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="all">Tất cả</TabsTrigger>
            <TabsTrigger value="comment">Bình luận</TabsTrigger>
            <TabsTrigger value="question">Câu hỏi</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="space-y-4">
            {isLoading ? (
              <div className="text-center py-8 text-gray-500">
                Đang tải bình luận...
              </div>
            ) : comments.length > 0 ? (
              <div className="space-y-4">
                {comments.map(comment => renderComment(comment))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>Chưa có bình luận nào</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
