"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { 
  CheckCircle, 
  Clock, 
  Award, 
  Download, 
  Play, 
  BookOpen, 
  TrendingUp,
  Calendar,
  Target,
  BarChart3
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import Header from "@/components/layout/Header";
import { LoadingPage } from "@/components/ui/loading";
import { ErrorPage } from "@/components/ui/error";

interface CourseProgress {
  courseId: string;
  courseTitle: string;
  enrolledAt: string;
  lastAccessedAt: string;
  currentLesson: string | null;
  progress: {
    percentage: number;
    completedLessons: number;
    totalLessons: number;
    totalTimeSpent: number;
    averageTimePerLesson: number;
    isCompleted: boolean;
    completedAt: string | null;
  };
  curriculum: Array<{
    _id: string;
    title: string;
    description: string;
    duration: number;
    order: number;
    isPreview: boolean;
    progress: {
      lessonId: string;
      timeSpent: number;
      videoProgress: number;
      isCompleted: boolean;
      bookmarks: any[];
      notes: string;
    };
  }>;
  nextLesson: {
    id: string;
    title: string;
    order: number;
    duration: number;
  } | null;
  statistics: {
    totalVideoTime: number;
    completionRate: number;
    studyStreak: number;
    estimatedCompletionDate: string | null;
  };
  certificates: Array<{
    id: string;
    issuedAt: string;
    downloadUrl: string;
  }>;
}

export default function CourseProgressPage() {
  const params = useParams();
  const router = useRouter();
  const [courseProgress, setCourseProgress] = useState<CourseProgress | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isGeneratingCertificate, setIsGeneratingCertificate] = useState(false);

  const courseId = params.id as string;

  useEffect(() => {
    if (courseId) {
      loadCourseProgress();
    }
  }, [courseId]);

  const loadCourseProgress = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/courses/${courseId}/progress`);
      const data = await response.json();

      if (data.success) {
        setCourseProgress(data.data);
      } else {
        setError(data.message || 'Không thể tải tiến độ khóa học');
      }
    } catch (error) {
      console.error('Load course progress error:', error);
      setError('Lỗi kết nối. Vui lòng thử lại.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateCertificate = async () => {
    setIsGeneratingCertificate(true);

    try {
      const response = await fetch(`/api/courses/${courseId}/progress`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'generate_certificate' }),
      });

      const data = await response.json();

      if (data.success) {
        // Reload progress to get updated certificate info
        await loadCourseProgress();
        alert('Chứng chỉ đã được tạo thành công!');
      } else {
        alert(data.message || 'Không thể tạo chứng chỉ');
      }
    } catch (error) {
      console.error('Generate certificate error:', error);
      alert('Lỗi khi tạo chứng chỉ');
    } finally {
      setIsGeneratingCertificate(false);
    }
  };

  const handleDownloadCertificate = (certificateId: string) => {
    window.open(`/api/courses/${courseId}/certificate/${certificateId}/download`, '_blank');
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  if (isLoading) {
    return <LoadingPage message="Đang tải tiến độ khóa học..." />;
  }

  if (error || !courseProgress) {
    return <ErrorPage title="Không thể tải tiến độ" message={error || "Tiến độ khóa học không tồn tại"} />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Tiến độ học tập</h1>
          <p className="text-gray-600">{courseProgress.courseTitle}</p>
        </div>

        {/* Progress Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Target className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Tiến độ</p>
                  <p className="text-2xl font-bold text-gray-900">{courseProgress.progress.percentage}%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Bài học hoàn thành</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {courseProgress.progress.completedLessons}/{courseProgress.progress.totalLessons}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Clock className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Thời gian học</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {Math.floor(courseProgress.progress.totalTimeSpent / 3600)}h
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Streak</p>
                  <p className="text-2xl font-bold text-gray-900">{courseProgress.statistics.studyStreak} ngày</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Overall Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Tổng quan tiến độ</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Hoàn thành khóa học</span>
                      <span>{courseProgress.progress.percentage}%</span>
                    </div>
                    <Progress value={courseProgress.progress.percentage} className="h-3" />
                  </div>
                  
                  {courseProgress.progress.isCompleted ? (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 text-green-800">
                        <CheckCircle className="h-5 w-5" />
                        <span className="font-medium">Chúc mừng! Bạn đã hoàn thành khóa học</span>
                      </div>
                      <p className="text-sm text-green-700 mt-1">
                        Hoàn thành lúc: {new Date(courseProgress.progress.completedAt!).toLocaleString('vi-VN')}
                      </p>
                    </div>
                  ) : courseProgress.nextLesson ? (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-blue-900">Bài học tiếp theo</p>
                          <p className="text-sm text-blue-700">{courseProgress.nextLesson.title}</p>
                        </div>
                        <Button 
                          onClick={() => router.push(`/courses/${courseId}/lessons/${courseProgress.nextLesson!.order}`)}
                          size="sm"
                        >
                          <Play className="h-4 w-4 mr-1" />
                          Tiếp tục
                        </Button>
                      </div>
                    </div>
                  ) : null}
                </div>
              </CardContent>
            </Card>

            {/* Curriculum Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Tiến độ bài học</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {courseProgress.curriculum.map((lesson, index) => (
                    <div 
                      key={lesson._id} 
                      className={`p-4 border rounded-lg ${
                        lesson.progress.isCompleted ? 'bg-green-50 border-green-200' : 'bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                            lesson.progress.isCompleted 
                              ? 'bg-green-600 text-white' 
                              : 'bg-gray-300 text-gray-600'
                          }`}>
                            {lesson.progress.isCompleted ? <CheckCircle className="h-4 w-4" /> : lesson.order}
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">{lesson.title}</h4>
                            <p className="text-sm text-gray-600">
                              {formatDuration(lesson.duration)} • 
                              {lesson.progress.videoProgress > 0 ? ` ${Math.round(lesson.progress.videoProgress)}% đã xem` : ' Chưa bắt đầu'}
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/courses/${courseId}/lessons/${lesson.order}`)}
                        >
                          {lesson.progress.isCompleted ? 'Xem lại' : 'Học ngay'}
                        </Button>
                      </div>
                      {lesson.progress.videoProgress > 0 && lesson.progress.videoProgress < 100 && (
                        <div className="mt-3">
                          <Progress value={lesson.progress.videoProgress} className="h-1" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Certificate Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5" />
                  Chứng chỉ
                </CardTitle>
              </CardHeader>
              <CardContent>
                {courseProgress.progress.isCompleted ? (
                  courseProgress.certificates.length > 0 ? (
                    <div className="space-y-3">
                      {courseProgress.certificates.map((cert) => (
                        <div key={cert.id} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-gray-900">Chứng chỉ hoàn thành</p>
                              <p className="text-sm text-gray-600">
                                Cấp ngày: {new Date(cert.issuedAt).toLocaleDateString('vi-VN')}
                              </p>
                            </div>
                            <Button
                              size="sm"
                              onClick={() => handleDownloadCertificate(cert.id)}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Tải về
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <Award className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                      <p className="text-gray-600 mb-4">Bạn đã hoàn thành khóa học!</p>
                      <Button
                        onClick={handleGenerateCertificate}
                        disabled={isGeneratingCertificate}
                        className="w-full"
                      >
                        {isGeneratingCertificate ? 'Đang tạo...' : 'Tạo chứng chỉ'}
                      </Button>
                    </div>
                  )
                ) : (
                  <div className="text-center py-4">
                    <Award className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-600">Hoàn thành khóa học để nhận chứng chỉ</p>
                    <div className="mt-3">
                      <Progress value={courseProgress.progress.percentage} className="h-2" />
                      <p className="text-sm text-gray-500 mt-1">
                        Còn {courseProgress.progress.totalLessons - courseProgress.progress.completedLessons} bài học
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Statistics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Thống kê
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Ngày đăng ký:</span>
                  <span className="font-medium">
                    {new Date(courseProgress.enrolledAt).toLocaleDateString('vi-VN')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Lần truy cập cuối:</span>
                  <span className="font-medium">
                    {new Date(courseProgress.lastAccessedAt).toLocaleDateString('vi-VN')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Thời gian TB/bài:</span>
                  <span className="font-medium">
                    {Math.round(courseProgress.progress.averageTimePerLesson / 60)} phút
                  </span>
                </div>
                {courseProgress.statistics.estimatedCompletionDate && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Dự kiến hoàn thành:</span>
                    <span className="font-medium">
                      {new Date(courseProgress.statistics.estimatedCompletionDate).toLocaleDateString('vi-VN')}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
