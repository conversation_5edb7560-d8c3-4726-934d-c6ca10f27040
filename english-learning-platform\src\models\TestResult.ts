import mongoose, { Document, Schema } from 'mongoose';

export interface ITestResult extends Document {
  studentId: mongoose.Types.ObjectId;
  testId: mongoose.Types.ObjectId;
  answers: IAnswer[];
  totalScore: number;
  maxScore: number;
  percentage: number;
  timeSpent: number; // in seconds
  startedAt: Date;
  completedAt: Date;
  isPassed: boolean;
  feedback?: string;
  detailedAnalysis: {
    correctAnswers: number;
    incorrectAnswers: number;
    skippedAnswers: number;
    skillBreakdown: {
      skill: string;
      score: number;
      maxScore: number;
      percentage: number;
    }[];
  };
  gradingStatus: 'auto' | 'pending' | 'manual' | 'completed';
  gradedBy?: mongoose.Types.ObjectId;
  gradedAt?: Date;
}

export interface IAnswer {
  questionId: string;
  questionOrder: number;
  userAnswer: string | string[];
  correctAnswer: string | string[];
  isCorrect: boolean;
  points: number;
  maxPoints: number;
  timeSpent: number; // in seconds
  isSkipped: boolean;
  needsManualGrading: boolean; // for essay questions
  manualScore?: number;
  feedback?: string;
}

const AnswerSchema = new Schema({
  questionId: {
    type: String,
    required: [true, 'ID câu hỏi là bắt buộc']
  },
  questionOrder: {
    type: Number,
    required: [true, 'Thứ tự câu hỏi là bắt buộc']
  },
  userAnswer: {
    type: Schema.Types.Mixed, // Can be string or array
    default: null
  },
  correctAnswer: {
    type: Schema.Types.Mixed,
    required: [true, 'Đáp án đúng là bắt buộc']
  },
  isCorrect: {
    type: Boolean,
    default: false
  },
  points: {
    type: Number,
    default: 0,
    min: [0, 'Điểm không được âm']
  },
  maxPoints: {
    type: Number,
    required: [true, 'Điểm tối đa là bắt buộc'],
    min: [0, 'Điểm tối đa không được âm']
  },
  timeSpent: {
    type: Number,
    default: 0,
    min: [0, 'Thời gian không được âm']
  },
  isSkipped: {
    type: Boolean,
    default: false
  },
  needsManualGrading: {
    type: Boolean,
    default: false
  },
  manualScore: {
    type: Number,
    min: [0, 'Điểm thủ công không được âm']
  },
  feedback: {
    type: String,
    maxlength: [1000, 'Phản hồi không được vượt quá 1000 ký tự']
  }
});

const SkillBreakdownSchema = new Schema({
  skill: {
    type: String,
    required: [true, 'Tên kỹ năng là bắt buộc']
  },
  score: {
    type: Number,
    required: [true, 'Điểm kỹ năng là bắt buộc'],
    min: [0, 'Điểm không được âm']
  },
  maxScore: {
    type: Number,
    required: [true, 'Điểm tối đa kỹ năng là bắt buộc'],
    min: [0, 'Điểm tối đa không được âm']
  },
  percentage: {
    type: Number,
    required: [true, 'Phần trăm kỹ năng là bắt buộc'],
    min: [0, 'Phần trăm không được âm'],
    max: [100, 'Phần trăm không được vượt quá 100']
  }
});

const TestResultSchema = new Schema<ITestResult>({
  studentId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'ID học viên là bắt buộc']
  },
  testId: {
    type: Schema.Types.ObjectId,
    ref: 'Test',
    required: [true, 'ID bài kiểm tra là bắt buộc']
  },
  answers: [AnswerSchema],
  totalScore: {
    type: Number,
    required: [true, 'Tổng điểm là bắt buộc'],
    min: [0, 'Tổng điểm không được âm']
  },
  maxScore: {
    type: Number,
    required: [true, 'Điểm tối đa là bắt buộc'],
    min: [0, 'Điểm tối đa không được âm']
  },
  percentage: {
    type: Number,
    required: [true, 'Phần trăm điểm là bắt buộc'],
    min: [0, 'Phần trăm không được âm'],
    max: [100, 'Phần trăm không được vượt quá 100']
  },
  timeSpent: {
    type: Number,
    required: [true, 'Thời gian làm bài là bắt buộc'],
    min: [0, 'Thời gian không được âm']
  },
  startedAt: {
    type: Date,
    required: [true, 'Thời gian bắt đầu là bắt buộc']
  },
  completedAt: {
    type: Date,
    required: [true, 'Thời gian hoàn thành là bắt buộc']
  },
  isPassed: {
    type: Boolean,
    required: [true, 'Trạng thái đạt/không đạt là bắt buộc']
  },
  feedback: {
    type: String,
    maxlength: [2000, 'Phản hồi không được vượt quá 2000 ký tự']
  },
  detailedAnalysis: {
    correctAnswers: {
      type: Number,
      required: [true, 'Số câu đúng là bắt buộc'],
      min: [0, 'Số câu đúng không được âm']
    },
    incorrectAnswers: {
      type: Number,
      required: [true, 'Số câu sai là bắt buộc'],
      min: [0, 'Số câu sai không được âm']
    },
    skippedAnswers: {
      type: Number,
      required: [true, 'Số câu bỏ qua là bắt buộc'],
      min: [0, 'Số câu bỏ qua không được âm']
    },
    skillBreakdown: [SkillBreakdownSchema]
  },
  gradingStatus: {
    type: String,
    enum: ['auto', 'pending', 'manual', 'completed'],
    default: 'auto'
  },
  gradedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  gradedAt: {
    type: Date
  }
}, {
  timestamps: true
});

// Indexes cho tìm kiếm và hiệu suất
TestResultSchema.index({ studentId: 1, testId: 1 });
TestResultSchema.index({ studentId: 1, completedAt: -1 });
TestResultSchema.index({ testId: 1, completedAt: -1 });
TestResultSchema.index({ gradingStatus: 1 });
TestResultSchema.index({ isPassed: 1 });

// Middleware để tính toán các giá trị
TestResultSchema.pre('save', function(next) {
  // Tính phần trăm
  if (this.maxScore > 0) {
    this.percentage = Math.round((this.totalScore / this.maxScore) * 100);
  }
  
  // Cập nhật thời gian chấm điểm nếu trạng thái thay đổi
  if (this.isModified('gradingStatus') && this.gradingStatus === 'completed') {
    this.gradedAt = new Date();
  }
  
  next();
});

export default mongoose.models.TestResult || mongoose.model<ITestResult>('TestResult', TestResultSchema);
