{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('vi-VN', {\n    style: 'currency',\n    currency: 'VND'\n  }).format(price);\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('vi-VN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  }).format(dateObj);\n}\n\nexport function formatDuration(minutes: number): string {\n  const hours = Math.floor(minutes / 60);\n  const remainingMinutes = minutes % 60;\n\n  if (hours === 0) {\n    return `${remainingMinutes} phút`;\n  } else if (remainingMinutes === 0) {\n    return `${hours} giờ`;\n  } else {\n    return `${hours} giờ ${remainingMinutes} phút`;\n  }\n}\n\nexport function calculateProgress(completedLessons: number[], totalLessons: number): number {\n  if (totalLessons === 0) return 0;\n  return Math.round((completedLessons.length / totalLessons) * 100);\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2);\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '') // Remove diacritics\n    .replace(/[^a-z0-9 -]/g, '') // Remove special characters\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen\n    .trim();\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n\n  if (password.length < 6) {\n    errors.push('Mật khẩu phải có ít nhất 6 ký tự');\n  }\n\n  if (!/[A-Z]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 chữ hoa');\n  }\n\n  if (!/[a-z]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 chữ thường');\n  }\n\n  if (!/[0-9]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 số');\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\nexport function generateRandomString(length: number): string {\n  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += characters.charAt(Math.floor(Math.random() * characters.length));\n  }\n  return result;\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,mBAAmB,UAAU;IAEnC,IAAI,UAAU,GAAG;QACf,OAAO,GAAG,iBAAiB,KAAK,CAAC;IACnC,OAAO,IAAI,qBAAqB,GAAG;QACjC,OAAO,GAAG,MAAM,IAAI,CAAC;IACvB,OAAO;QACL,OAAO,GAAG,MAAM,KAAK,EAAE,iBAAiB,KAAK,CAAC;IAChD;AACF;AAEO,SAAS,kBAAkB,gBAA0B,EAAE,YAAoB;IAChF,IAAI,iBAAiB,GAAG,OAAO;IAC/B,OAAO,KAAK,KAAK,CAAC,AAAC,iBAAiB,MAAM,GAAG,eAAgB;AAC/D;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAAI,oBAAoB;KACpD,OAAO,CAAC,gBAAgB,IAAI,4BAA4B;KACxD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,OAAO,KAAK,8CAA8C;KAClE,IAAI;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,qBAAqB,MAAc;IACjD,MAAM,aAAa;IACnB,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,WAAW,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM;IAC1E;IACA,OAAO;AACT;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { ChevronRight, Home } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface BreadcrumbItem {\n  label: string;\n  href?: string;\n  current?: boolean;\n}\n\ninterface BreadcrumbProps {\n  items: BreadcrumbItem[];\n  className?: string;\n  showHome?: boolean;\n}\n\nexport function Breadcrumb({ items, className, showHome = true }: BreadcrumbProps) {\n  const allItems = showHome \n    ? [{ label: \"Trang chủ\", href: \"/\" }, ...items]\n    : items;\n\n  return (\n    <nav className={cn(\"flex\", className)} aria-label=\"Breadcrumb\">\n      <ol className=\"inline-flex items-center space-x-1 md:space-x-3\">\n        {allItems.map((item, index) => (\n          <li key={index} className=\"inline-flex items-center\">\n            {index > 0 && (\n              <ChevronRight className=\"w-4 h-4 text-gray-400 mx-1\" />\n            )}\n            \n            {item.current || !item.href ? (\n              <span className=\"text-sm font-medium text-gray-500 flex items-center\">\n                {index === 0 && showHome && (\n                  <Home className=\"w-4 h-4 mr-1\" />\n                )}\n                {item.label}\n              </span>\n            ) : (\n              <Link\n                href={item.href}\n                className=\"inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors\"\n              >\n                {index === 0 && showHome && (\n                  <Home className=\"w-4 h-4 mr-1\" />\n                )}\n                {item.label}\n              </Link>\n            )}\n          </li>\n        ))}\n      </ol>\n    </nav>\n  );\n}\n\n// Hook để tự động tạo breadcrumb từ URL\nexport function useBreadcrumb() {\n  if (typeof window === 'undefined') return [];\n  \n  const pathname = window.location.pathname;\n  const segments = pathname.split('/').filter(Boolean);\n  \n  const breadcrumbMap: Record<string, string> = {\n    'courses': 'Khóa học',\n    'tests': 'Luyện thi',\n    'dashboard': 'Dashboard',\n    'profile': 'Hồ sơ',\n    'about': 'Giới thiệu',\n    'contact': 'Liên hệ',\n    'auth': 'Xác thực',\n    'login': 'Đăng nhập',\n    'register': 'Đăng ký',\n    'teacher': 'Giáo viên',\n    'admin': 'Quản trị',\n    'student': 'Học viên'\n  };\n\n  const items: BreadcrumbItem[] = [];\n  let currentPath = '';\n\n  segments.forEach((segment, index) => {\n    currentPath += `/${segment}`;\n    const isLast = index === segments.length - 1;\n    \n    items.push({\n      label: breadcrumbMap[segment] || segment,\n      href: isLast ? undefined : currentPath,\n      current: isLast\n    });\n  });\n\n  return items;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;;;;;AAcO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,IAAI,EAAmB;IAC/E,MAAM,WAAW,WACb;QAAC;YAAE,OAAO;YAAa,MAAM;QAAI;WAAM;KAAM,GAC7C;IAEJ,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAY,cAAW;kBAChD,cAAA,6LAAC;YAAG,WAAU;sBACX,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC;oBAAe,WAAU;;wBACvB,QAAQ,mBACP,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAGzB,KAAK,OAAO,IAAI,CAAC,KAAK,IAAI,iBACzB,6LAAC;4BAAK,WAAU;;gCACb,UAAU,KAAK,0BACd,6LAAC,sMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAEjB,KAAK,KAAK;;;;;;iDAGb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAU;;gCAET,UAAU,KAAK,0BACd,6LAAC,sMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAEjB,KAAK,KAAK;;;;;;;;mBApBR;;;;;;;;;;;;;;;AA4BnB;KArCgB;AAwCT,SAAS;IACd,uCAAmC;;IAAS;IAE5C,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ;IACzC,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAE5C,MAAM,gBAAwC;QAC5C,WAAW;QACX,SAAS;QACT,aAAa;QACb,WAAW;QACX,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,WAAW;QACX,SAAS;QACT,WAAW;IACb;IAEA,MAAM,QAA0B,EAAE;IAClC,IAAI,cAAc;IAElB,SAAS,OAAO,CAAC,CAAC,SAAS;QACzB,eAAe,CAAC,CAAC,EAAE,SAAS;QAC5B,MAAM,SAAS,UAAU,SAAS,MAAM,GAAG;QAE3C,MAAM,IAAI,CAAC;YACT,OAAO,aAAa,CAAC,QAAQ,IAAI;YACjC,MAAM,SAAS,YAAY;YAC3B,SAAS;QACX;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/loading.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\n\ninterface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\";\n  className?: string;\n}\n\nexport function LoadingSpinner({ size = \"md\", className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: \"h-4 w-4\",\n    md: \"h-6 w-6\", \n    lg: \"h-8 w-8\"\n  };\n\n  return (\n    <div\n      className={cn(\n        \"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600\",\n        sizeClasses[size],\n        className\n      )}\n    />\n  );\n}\n\ninterface LoadingCardProps {\n  className?: string;\n}\n\nexport function LoadingCard({ className }: LoadingCardProps) {\n  return (\n    <div className={cn(\"animate-pulse\", className)}>\n      <div className=\"bg-gray-200 rounded-lg h-48 mb-4\"></div>\n      <div className=\"space-y-3\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-5/6\"></div>\n      </div>\n    </div>\n  );\n}\n\ninterface LoadingPageProps {\n  message?: string;\n}\n\nexport function LoadingPage({ message = \"Đang tải...\" }: LoadingPageProps) {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <LoadingSpinner size=\"lg\" className=\"mx-auto mb-4\" />\n        <p className=\"text-gray-600\">{message}</p>\n      </div>\n    </div>\n  );\n}\n\ninterface LoadingButtonProps {\n  children: React.ReactNode;\n  isLoading?: boolean;\n  className?: string;\n  disabled?: boolean;\n  onClick?: () => void;\n  type?: \"button\" | \"submit\" | \"reset\";\n}\n\nexport function LoadingButton({ \n  children, \n  isLoading = false, \n  className,\n  disabled,\n  onClick,\n  type = \"button\"\n}: LoadingButtonProps) {\n  return (\n    <button\n      type={type}\n      onClick={onClick}\n      disabled={disabled || isLoading}\n      className={cn(\n        \"inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n        className\n      )}\n    >\n      {isLoading && <LoadingSpinner size=\"sm\" className=\"mr-2\" />}\n      {children}\n    </button>\n  );\n}\n\nexport function LoadingOverlay() {\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 flex items-center space-x-3\">\n        <LoadingSpinner size=\"md\" />\n        <span className=\"text-gray-700\">Đang xử lý...</span>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR;KAhBgB;AAsBT,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;;0BAClC,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;MAXgB;AAiBT,SAAS,YAAY,EAAE,UAAU,aAAa,EAAoB;IACvE,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAe,MAAK;oBAAK,WAAU;;;;;;8BACpC,6LAAC;oBAAE,WAAU;8BAAiB;;;;;;;;;;;;;;;;;AAItC;MATgB;AAoBT,SAAS,cAAc,EAC5B,QAAQ,EACR,YAAY,KAAK,EACjB,SAAS,EACT,QAAQ,EACR,OAAO,EACP,OAAO,QAAQ,EACI;IACnB,qBACE,6LAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU,YAAY;QACtB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+QACA;;YAGD,2BAAa,6LAAC;gBAAe,MAAK;gBAAK,WAAU;;;;;;YACjD;;;;;;;AAGP;MAtBgB;AAwBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAe,MAAK;;;;;;8BACrB,6LAAC;oBAAK,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAIxC;MATgB", "debugId": null}}, {"offset": {"line": 649, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/error.tsx"], "sourcesContent": ["import { Alert<PERSON><PERSON>gle, <PERSON>fresh<PERSON><PERSON>, Home, ArrowLeft } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { cn } from \"@/lib/utils\";\n\ninterface ErrorMessageProps {\n  title?: string;\n  message?: string;\n  className?: string;\n}\n\nexport function ErrorMessage({ \n  title = \"Đã xảy ra lỗi\", \n  message = \"Vui lòng thử lại sau.\", \n  className \n}: ErrorMessageProps) {\n  return (\n    <div className={cn(\"flex items-center space-x-2 text-red-600\", className)}>\n      <AlertTriangle className=\"h-4 w-4\" />\n      <div>\n        <p className=\"font-medium text-sm\">{title}</p>\n        {message && <p className=\"text-xs text-red-500\">{message}</p>}\n      </div>\n    </div>\n  );\n}\n\ninterface ErrorCardProps {\n  title?: string;\n  message?: string;\n  onRetry?: () => void;\n  className?: string;\n}\n\nexport function ErrorCard({ \n  title = \"Không thể tải dữ liệu\", \n  message = \"Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại.\", \n  onRetry,\n  className \n}: ErrorCardProps) {\n  return (\n    <Card className={cn(\"border-red-200\", className)}>\n      <CardContent className=\"pt-6\">\n        <div className=\"text-center\">\n          <AlertTriangle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">{title}</h3>\n          <p className=\"text-gray-600 mb-4\">{message}</p>\n          {onRetry && (\n            <Button onClick={onRetry} variant=\"outline\">\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\n              Thử lại\n            </Button>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n\ninterface ErrorPageProps {\n  title?: string;\n  message?: string;\n  showHomeButton?: boolean;\n  showBackButton?: boolean;\n  onRetry?: () => void;\n}\n\nexport function ErrorPage({ \n  title = \"Oops! Đã xảy ra lỗi\", \n  message = \"Trang bạn đang tìm kiếm không tồn tại hoặc đã xảy ra lỗi hệ thống.\",\n  showHomeButton = true,\n  showBackButton = true,\n  onRetry\n}: ErrorPageProps) {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center px-4\">\n      <div className=\"max-w-md w-full text-center\">\n        <div className=\"mb-8\">\n          <AlertTriangle className=\"h-24 w-24 text-red-500 mx-auto mb-6\" />\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">{title}</h1>\n          <p className=\"text-gray-600 mb-8\">{message}</p>\n        </div>\n        \n        <div className=\"space-y-3\">\n          {onRetry && (\n            <Button onClick={onRetry} className=\"w-full\">\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\n              Thử lại\n            </Button>\n          )}\n          \n          {showHomeButton && (\n            <Button variant=\"outline\" className=\"w-full\" onClick={() => window.location.href = '/'}>\n              <Home className=\"h-4 w-4 mr-2\" />\n              Về trang chủ\n            </Button>\n          )}\n          \n          {showBackButton && (\n            <Button variant=\"ghost\" className=\"w-full\" onClick={() => window.history.back()}>\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Quay lại\n            </Button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface FormErrorProps {\n  errors: Record<string, string>;\n  className?: string;\n}\n\nexport function FormError({ errors, className }: FormErrorProps) {\n  const errorMessages = Object.values(errors).filter(Boolean);\n  \n  if (errorMessages.length === 0) return null;\n\n  return (\n    <div className={cn(\"bg-red-50 border border-red-200 rounded-md p-3\", className)}>\n      <div className=\"flex\">\n        <AlertTriangle className=\"h-4 w-4 text-red-400 mt-0.5 mr-2 flex-shrink-0\" />\n        <div className=\"text-sm\">\n          {errorMessages.length === 1 ? (\n            <p className=\"text-red-800\">{errorMessages[0]}</p>\n          ) : (\n            <div>\n              <p className=\"text-red-800 font-medium mb-1\">Vui lòng kiểm tra lại:</p>\n              <ul className=\"list-disc list-inside text-red-700 space-y-1\">\n                {errorMessages.map((error, index) => (\n                  <li key={index}>{error}</li>\n                ))}\n              </ul>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface NetworkErrorProps {\n  onRetry?: () => void;\n  className?: string;\n}\n\nexport function NetworkError({ onRetry, className }: NetworkErrorProps) {\n  return (\n    <div className={cn(\"text-center py-8\", className)}>\n      <div className=\"mb-4\">\n        <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n          <AlertTriangle className=\"h-8 w-8 text-red-600\" />\n        </div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n          Lỗi kết nối mạng\n        </h3>\n        <p className=\"text-gray-600 mb-4\">\n          Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối internet và thử lại.\n        </p>\n      </div>\n      \n      {onRetry && (\n        <Button onClick={onRetry} variant=\"outline\">\n          <RefreshCw className=\"h-4 w-4 mr-2\" />\n          Thử lại\n        </Button>\n      )}\n    </div>\n  );\n}\n\ninterface NotFoundProps {\n  title?: string;\n  message?: string;\n  showHomeButton?: boolean;\n}\n\nexport function NotFound({ \n  title = \"Không tìm thấy trang\", \n  message = \"Trang bạn đang tìm kiếm không tồn tại hoặc đã được di chuyển.\",\n  showHomeButton = true\n}: NotFoundProps) {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center px-4\">\n      <div className=\"max-w-md w-full text-center\">\n        <div className=\"mb-8\">\n          <div className=\"text-6xl font-bold text-gray-400 mb-4\">404</div>\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">{title}</h1>\n          <p className=\"text-gray-600 mb-8\">{message}</p>\n        </div>\n        \n        {showHomeButton && (\n          <Button onClick={() => window.location.href = '/'}>\n            <Home className=\"h-4 w-4 mr-2\" />\n            Về trang chủ\n          </Button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAQO,SAAS,aAAa,EAC3B,QAAQ,eAAe,EACvB,UAAU,uBAAuB,EACjC,SAAS,EACS;IAClB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;;0BAC7D,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,6LAAC;;kCACC,6LAAC;wBAAE,WAAU;kCAAuB;;;;;;oBACnC,yBAAW,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAIzD;KAdgB;AAuBT,SAAS,UAAU,EACxB,QAAQ,uBAAuB,EAC/B,UAAU,kDAAkD,EAC5D,OAAO,EACP,SAAS,EACM;IACf,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBACpC,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2NAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,6LAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;oBAClC,yBACC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAS,SAAQ;;0CAChC,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;;;;;AAQpD;MAvBgB;AAiCT,SAAS,UAAU,EACxB,QAAQ,qBAAqB,EAC7B,UAAU,oEAAoE,EAC9E,iBAAiB,IAAI,EACrB,iBAAiB,IAAI,EACrB,OAAO,EACQ;IACf,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAGrC,6LAAC;oBAAI,WAAU;;wBACZ,yBACC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAS,WAAU;;8CAClC,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;wBAKzC,gCACC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,WAAU;4BAAS,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8CACjF,6LAAC,sMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;wBAKpC,gCACC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,WAAU;4BAAS,SAAS,IAAM,OAAO,OAAO,CAAC,IAAI;;8CAC3E,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAQpD;MAzCgB;AAgDT,SAAS,UAAU,EAAE,MAAM,EAAE,SAAS,EAAkB;IAC7D,MAAM,gBAAgB,OAAO,MAAM,CAAC,QAAQ,MAAM,CAAC;IAEnD,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;kBACnE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;8BACzB,6LAAC;oBAAI,WAAU;8BACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;wBAAE,WAAU;kCAAgB,aAAa,CAAC,EAAE;;;;;6CAE7C,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;0CAC7C,6LAAC;gCAAG,WAAU;0CACX,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;kDAAgB;uCAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3B;MA1BgB;AAiCT,SAAS,aAAa,EAAE,OAAO,EAAE,SAAS,EAAqB;IACpE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;;0BACrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;kCAE3B,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;YAKnC,yBACC,6LAAC,qIAAA,CAAA,SAAM;gBAAC,SAAS;gBAAS,SAAQ;;kCAChC,6LAAC,mNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;;;;;;AAMhD;MAvBgB;AA+BT,SAAS,SAAS,EACvB,QAAQ,sBAAsB,EAC9B,UAAU,+DAA+D,EACzE,iBAAiB,IAAI,EACP;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAwC;;;;;;sCACvD,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;gBAGpC,gCACC,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;sCAC5C,6LAAC,sMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;;;;;AAO7C;MAvBgB", "debugId": null}}, {"offset": {"line": 1122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 1480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { useState } from \"react\";\nimport { Book<PERSON>pen, Menu, X } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { useAuth } from \"@/contexts/AuthContext\";\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const { user, isAuthenticated, logout } = useAuth();\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  const handleLogout = async () => {\n    await logout();\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <BookOpen className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-xl font-bold text-gray-900\">\n              English Learning Platform\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <Link\n              href=\"/courses\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Khóa Học\n            </Link>\n            <Link\n              href=\"/tests\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Luyện Thi\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Giới Thiệu\n            </Link>\n            <Link\n              href=\"/contact\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Liên Hệ\n            </Link>\n          </nav>\n\n          {/* Desktop Auth/User Menu */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {isAuthenticated && user ? (\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                    <Avatar className=\"h-8 w-8\">\n                      <AvatarImage src={user.avatar} alt={user.name} />\n                      <AvatarFallback>\n                        {user.name.split(' ').map((n: string) => n[0]).join('')}\n                      </AvatarFallback>\n                    </Avatar>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                  <DropdownMenuLabel className=\"font-normal\">\n                    <div className=\"flex flex-col space-y-1\">\n                      <p className=\"text-sm font-medium leading-none\">{user.name}</p>\n                      <p className=\"text-xs leading-none text-muted-foreground\">\n                        {user.email}\n                      </p>\n                    </div>\n                  </DropdownMenuLabel>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard\">Dashboard</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/profile\">Hồ sơ cá nhân</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/settings\">Cài đặt</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem onClick={handleLogout}>\n                    Đăng xuất\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            ) : (\n              <>\n                <Link href=\"/auth/login\">\n                  <Button variant=\"ghost\">\n                    Đăng Nhập\n                  </Button>\n                </Link>\n                <Link href=\"/auth/register\">\n                  <Button>\n                    Đăng Ký\n                  </Button>\n                </Link>\n              </>\n            )}\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            className=\"md:hidden p-2\"\n            onClick={toggleMenu}\n            aria-label=\"Toggle menu\"\n          >\n            {isMenuOpen ? (\n              <X className=\"h-6 w-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"h-6 w-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <Link\n                href=\"/courses\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Khóa Học\n              </Link>\n              <Link\n                href=\"/tests\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Luyện Thi\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Giới Thiệu\n              </Link>\n              <Link\n                href=\"/contact\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Liên Hệ\n              </Link>\n\n              <div className=\"flex flex-col space-y-2 pt-4 border-t border-gray-200\">\n                {isAuthenticated && user ? (\n                  <>\n                    <div className=\"flex items-center space-x-3 px-3 py-2\">\n                      <Avatar className=\"h-8 w-8\">\n                        <AvatarImage src={user.avatar} alt={user.name} />\n                        <AvatarFallback>\n                          {user.name.split(' ').map((n: string) => n[0]).join('')}\n                        </AvatarFallback>\n                      </Avatar>\n                      <div>\n                        <p className=\"text-sm font-medium\">{user.name}</p>\n                        <p className=\"text-xs text-gray-500\">{user.email}</p>\n                      </div>\n                    </div>\n                    <Link href=\"/dashboard\" onClick={() => setIsMenuOpen(false)}>\n                      <Button variant=\"ghost\" className=\"w-full justify-start\">\n                        Dashboard\n                      </Button>\n                    </Link>\n                    <Link href=\"/profile\" onClick={() => setIsMenuOpen(false)}>\n                      <Button variant=\"ghost\" className=\"w-full justify-start\">\n                        Hồ sơ cá nhân\n                      </Button>\n                    </Link>\n                    <Button\n                      variant=\"ghost\"\n                      className=\"w-full justify-start\"\n                      onClick={handleLogout}\n                    >\n                      Đăng xuất\n                    </Button>\n                  </>\n                ) : (\n                  <>\n                    <Link href=\"/auth/login\" onClick={() => setIsMenuOpen(false)}>\n                      <Button variant=\"ghost\" className=\"w-full justify-start\">\n                        Đăng Nhập\n                      </Button>\n                    </Link>\n                    <Link href=\"/auth/register\" onClick={() => setIsMenuOpen(false)}>\n                      <Button className=\"w-full\">\n                        Đăng Ký\n                      </Button>\n                    </Link>\n                  </>\n                )}\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAQA;AACA;;;AAfA;;;;;;;;AAiBe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhD,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,MAAM,eAAe;QACnB,MAAM;QACN,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAMzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACZ,mBAAmB,qBAClB,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,WAAU;sDAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,qIAAA,CAAA,cAAW;wDAAC,KAAK,KAAK,MAAM;wDAAE,KAAK,KAAK,IAAI;;;;;;kEAC7C,6LAAC,qIAAA,CAAA,iBAAc;kEACZ,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;kDAK5D,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,WAAU;wCAAO,OAAM;wCAAM,UAAU;;0DAC1D,6LAAC,+IAAA,CAAA,oBAAiB;gDAAC,WAAU;0DAC3B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAoC,KAAK,IAAI;;;;;;sEAC1D,6LAAC;4DAAE,WAAU;sEACV,KAAK,KAAK;;;;;;;;;;;;;;;;;0DAIjB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0DACtB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAa;;;;;;;;;;;0DAE1B,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAW;;;;;;;;;;;0DAExB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAY;;;;;;;;;;;0DAEzB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0DACtB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS;0DAAc;;;;;;;;;;;;;;;;;qDAM7C;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAQ;;;;;;;;;;;kDAI1B,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;;;;;;;;sCAShB,6LAAC;4BACC,WAAU;4BACV,SAAS;4BACT,cAAW;sCAEV,2BACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAID,6LAAC;gCAAI,WAAU;0CACZ,mBAAmB,qBAClB;;sDACE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,WAAU;;sEAChB,6LAAC,qIAAA,CAAA,cAAW;4DAAC,KAAK,KAAK,MAAM;4DAAE,KAAK,KAAK,IAAI;;;;;;sEAC7C,6LAAC,qIAAA,CAAA,iBAAc;sEACZ,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;8DAGxD,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAuB,KAAK,IAAI;;;;;;sEAC7C,6LAAC;4DAAE,WAAU;sEAAyB,KAAK,KAAK;;;;;;;;;;;;;;;;;;sDAGpD,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,SAAS,IAAM,cAAc;sDACnD,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAAuB;;;;;;;;;;;sDAI3D,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,SAAS,IAAM,cAAc;sDACjD,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAAuB;;;;;;;;;;;sDAI3D,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;sDACV;;;;;;;iEAKH;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,SAAS,IAAM,cAAc;sDACpD,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAAuB;;;;;;;;;;;sDAI3D,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAiB,SAAS,IAAM,cAAc;sDACvD,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAajD;GAjNwB;;QAEoB,kIAAA,CAAA,UAAO;;;KAF3B", "debugId": null}}, {"offset": {"line": 2045, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/app/courses/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { Search, Filter, Star, Clock, Users, BookO<PERSON>, AlertTriangle } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Breadcrumb } from \"@/components/ui/breadcrumb\";\nimport { LoadingCard } from \"@/components/ui/loading\";\nimport { ErrorCard } from \"@/components/ui/error\";\nimport Header from \"@/components/layout/Header\";\n\n\n// Interface for course data from API\ninterface Course {\n  _id: string;\n  title: string;\n  description: string;\n  instructor: {\n    _id: string;\n    name: string;\n    avatar?: string;\n  };\n  price: number;\n  thumbnail?: string;\n  averageRating: number;\n  totalStudents: number;\n  totalDuration: number; // in hours\n  level: string;\n  category: string;\n  tags?: string[];\n  isPublished: boolean;\n  createdAt: string;\n  curriculum: Array<{\n    title: string;\n    duration: number;\n  }>;\n}\n\nconst categories = [\n  { value: \"all\", label: \"Tất cả\" },\n  { value: \"comprehensive\", label: \"Tổng hợp\" },\n  { value: \"listening\", label: \"Nghe\" },\n  { value: \"speaking\", label: \"Nói\" },\n  { value: \"reading\", label: \"Đọc\" },\n  { value: \"writing\", label: \"Viết\" }\n];\n\nconst levels = [\n  { value: \"all\", label: \"Tất cả trình độ\" },\n  { value: \"beginner\", label: \"Cơ bản\" },\n  { value: \"intermediate\", label: \"Trung cấp\" },\n  { value: \"advanced\", label: \"Nâng cao\" }\n];\n\nexport default function CoursesPage() {\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [selectedCategory, setSelectedCategory] = useState(\"all\");\n  const [selectedLevel, setSelectedLevel] = useState(\"all\");\n\n  // Load courses from API\n  useEffect(() => {\n    loadCourses();\n  }, [selectedCategory, selectedLevel, searchTerm]);\n\n  const loadCourses = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const params = new URLSearchParams();\n      if (selectedCategory !== 'all') params.append('category', selectedCategory);\n      if (selectedLevel !== 'all') params.append('level', selectedLevel);\n      if (searchTerm) params.append('search', searchTerm);\n\n      const response = await fetch(`/api/courses?${params.toString()}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setCourses(data.data);\n      } else {\n        setError(data.message || 'Lỗi khi tải danh sách khóa học');\n      }\n    } catch (error) {\n      console.error('Load courses error:', error);\n      setError('Lỗi kết nối. Vui lòng thử lại.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const breadcrumbItems = [\n    { label: \"Khóa học\", current: true }\n  ];\n\n  const filteredCourses = courses.filter((course: Course) => {\n    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         course.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === \"all\" || course.category === selectedCategory;\n    const matchesLevel = selectedLevel === \"all\" || course.level === selectedLevel;\n\n    return matchesSearch && matchesCategory && matchesLevel;\n  });\n\n  const handleRetry = () => {\n    loadCourses();\n  };\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND'\n    }).format(price);\n  };\n\n  // Show loading state\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Đang tải danh sách khóa học...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error state\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center py-12\">\n            <div className=\"text-red-600 mb-4\">\n              <AlertTriangle className=\"h-12 w-12 mx-auto mb-2\" />\n              <p className=\"text-lg font-medium\">Có lỗi xảy ra</p>\n              <p className=\"text-sm\">{error}</p>\n            </div>\n            <Button onClick={handleRetry} variant=\"outline\">\n              Thử lại\n            </Button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Breadcrumb */}\n        <Breadcrumb items={breadcrumbItems} className=\"mb-6\" />\n\n        {/* Page Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Khóa Học Tiếng Anh</h1>\n          <p className=\"text-lg text-gray-600\">\n            Khám phá các khóa học tiếng Anh chất lượng cao từ các giáo viên chuyên nghiệp\n          </p>\n        </div>\n\n        {/* Search and Filter */}\n        <div className=\"bg-white rounded-lg shadow-sm p-6 mb-8\">\n          <div className=\"flex flex-col lg:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                <Input\n                  placeholder=\"Tìm kiếm khóa học...\"\n                  className=\"pl-10\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n              </div>\n            </div>\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <select\n                className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n              >\n                {categories.map(category => (\n                  <option key={category.value} value={category.value}>\n                    {category.label}\n                  </option>\n                ))}\n              </select>\n              <select\n                className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                value={selectedLevel}\n                onChange={(e) => setSelectedLevel(e.target.value)}\n              >\n                {levels.map(level => (\n                  <option key={level.value} value={level.value}>\n                    {level.label}\n                  </option>\n                ))}\n              </select>\n              <Button variant=\"outline\">\n                <Filter className=\"h-4 w-4 mr-2\" />\n                Lọc ({filteredCourses.length})\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Course Grid */}\n        {isLoading ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {Array.from({ length: 8 }).map((_, index) => (\n              <LoadingCard key={index} />\n            ))}\n          </div>\n        ) : error ? (\n          <ErrorCard\n            title=\"Không thể tải khóa học\"\n            message={error}\n            onRetry={handleRetry}\n          />\n        ) : filteredCourses.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <BookOpen className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              Không tìm thấy khóa học\n            </h3>\n            <p className=\"text-gray-600\">\n              Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm\n            </p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {filteredCourses.map(course => (\n            <Card key={course._id} className=\"hover:shadow-lg transition-shadow\">\n              <div className=\"aspect-video bg-gray-200 rounded-t-lg\"></div>\n              <CardHeader className=\"pb-2\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <span className=\"text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded\">\n                    {course.level === 'beginner' ? 'Cơ bản' : \n                     course.level === 'intermediate' ? 'Trung cấp' : 'Nâng cao'}\n                  </span>\n                  <div className=\"flex items-center\">\n                    <Star className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                    <span className=\"text-sm text-gray-600 ml-1\">{course.averageRating}</span>\n                  </div>\n                </div>\n                <CardTitle className=\"text-lg line-clamp-2\">{course.title}</CardTitle>\n                <CardDescription className=\"line-clamp-2\">\n                  {course.description}\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"pt-0\">\n                <div className=\"flex items-center text-sm text-gray-500 mb-3\">\n                  <div className=\"flex items-center mr-4\">\n                    <Clock className=\"h-4 w-4 mr-1\" />\n                    {course.totalDuration}h\n                  </div>\n                  <div className=\"flex items-center mr-4\">\n                    <BookOpen className=\"h-4 w-4 mr-1\" />\n                    {course.curriculum?.length || 0} bài\n                  </div>\n                  <div className=\"flex items-center\">\n                    <Users className=\"h-4 w-4 mr-1\" />\n                    {course.totalStudents}\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center\">\n                    <div className=\"w-8 h-8 bg-gray-300 rounded-full mr-2\"></div>\n                    <span className=\"text-sm text-gray-600\">{course.instructor.name}</span>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-2xl font-bold text-blue-600\">\n                    {formatPrice(course.price)}\n                  </div>\n                  <Link href={`/courses/${course._id}`}>\n                    <Button size=\"sm\">\n                      Xem chi tiết\n                    </Button>\n                  </Link>\n                </div>\n              </CardContent>\n            </Card>\n            ))}\n          </div>\n        )}\n\n        {/* Load More */}\n        {!isLoading && !error && filteredCourses.length > 0 && (\n          <div className=\"text-center mt-12\">\n            <Button variant=\"outline\" size=\"lg\">\n              Xem thêm khóa học\n            </Button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAwCA,MAAM,aAAa;IACjB;QAAE,OAAO;QAAO,OAAO;IAAS;IAChC;QAAE,OAAO;QAAiB,OAAO;IAAW;IAC5C;QAAE,OAAO;QAAa,OAAO;IAAO;IACpC;QAAE,OAAO;QAAY,OAAO;IAAM;IAClC;QAAE,OAAO;QAAW,OAAO;IAAM;IACjC;QAAE,OAAO;QAAW,OAAO;IAAO;CACnC;AAED,MAAM,SAAS;IACb;QAAE,OAAO;QAAO,OAAO;IAAkB;IACzC;QAAE,OAAO;QAAY,OAAO;IAAS;IACrC;QAAE,OAAO;QAAgB,OAAO;IAAY;IAC5C;QAAE,OAAO;QAAY,OAAO;IAAW;CACxC;AAEc,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG;QAAC;QAAkB;QAAe;KAAW;IAEhD,MAAM,cAAc;QAClB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,qBAAqB,OAAO,OAAO,MAAM,CAAC,YAAY;YAC1D,IAAI,kBAAkB,OAAO,OAAO,MAAM,CAAC,SAAS;YACpD,IAAI,YAAY,OAAO,MAAM,CAAC,UAAU;YAExC,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,OAAO,QAAQ,IAAI;YAChE,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW,KAAK,IAAI;YACtB,OAAO;gBACL,SAAS,KAAK,OAAO,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAY,SAAS;QAAK;KACpC;IAED,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAC;QACtC,MAAM,gBAAgB,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,OAAO,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACrF,MAAM,kBAAkB,qBAAqB,SAAS,OAAO,QAAQ,KAAK;QAC1E,MAAM,eAAe,kBAAkB,SAAS,OAAO,KAAK,KAAK;QAEjE,OAAO,iBAAiB,mBAAmB;IAC7C;IAEA,MAAM,cAAc;QAClB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,qBAAqB;IACrB,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;IAKvC;IAEA,mBAAmB;IACnB,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC;wCAAE,WAAU;kDAAW;;;;;;;;;;;;0CAE1B,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAa,SAAQ;0CAAU;;;;;;;;;;;;;;;;;;;;;;;IAO1D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,yIAAA,CAAA,aAAU;wBAAC,OAAO;wBAAiB,WAAU;;;;;;kCAG9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAMvC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,WAAU;gDACV,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;8CAInD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;sDAElD,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;oDAA4B,OAAO,SAAS,KAAK;8DAC/C,SAAS,KAAK;mDADJ,SAAS,KAAK;;;;;;;;;;sDAK/B,6LAAC;4CACC,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;sDAE/C,OAAO,GAAG,CAAC,CAAA,sBACV,6LAAC;oDAAyB,OAAO,MAAM,KAAK;8DACzC,MAAM,KAAK;mDADD,MAAM,KAAK;;;;;;;;;;sDAK5B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;;8DACd,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;gDAC7B,gBAAgB,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;oBAOpC,0BACC,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC,sIAAA,CAAA,cAAW,MAAM;;;;;;;;;+BAGpB,sBACF,6LAAC,oIAAA,CAAA,YAAS;wBACR,OAAM;wBACN,SAAS;wBACT,SAAS;;;;;+BAET,gBAAgB,MAAM,KAAK,kBAC7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;6CAK/B,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAA,uBACrB,6LAAC,mIAAA,CAAA,OAAI;gCAAkB,WAAU;;kDAC/B,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,OAAO,KAAK,KAAK,aAAa,WAC9B,OAAO,KAAK,KAAK,iBAAiB,cAAc;;;;;;kEAEnD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;gEAAK,WAAU;0EAA8B,OAAO,aAAa;;;;;;;;;;;;;;;;;;0DAGtE,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAwB,OAAO,KAAK;;;;;;0DACzD,6LAAC,mIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,OAAO,WAAW;;;;;;;;;;;;kDAGvB,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,OAAO,aAAa;4DAAC;;;;;;;kEAExB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,OAAO,UAAU,EAAE,UAAU;4DAAE;;;;;;;kEAElC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,OAAO,aAAa;;;;;;;;;;;;;0DAIzB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAK,WAAU;sEAAyB,OAAO,UAAU,CAAC,IAAI;;;;;;;;;;;;;;;;;0DAInE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,YAAY,OAAO,KAAK;;;;;;kEAE3B,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,SAAS,EAAE,OAAO,GAAG,EAAE;kEAClC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,MAAK;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;+BA9Cf,OAAO,GAAG;;;;;;;;;;oBA0DxB,CAAC,aAAa,CAAC,SAAS,gBAAgB,MAAM,GAAG,mBAChD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAQhD;GA9PwB;KAAA", "debugId": null}}]}