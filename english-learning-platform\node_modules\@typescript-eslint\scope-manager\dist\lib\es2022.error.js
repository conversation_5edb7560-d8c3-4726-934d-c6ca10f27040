"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2022_error = void 0;
const base_config_1 = require("./base-config");
const es2021_promise_1 = require("./es2021.promise");
exports.es2022_error = {
    libs: [es2021_promise_1.es2021_promise],
    variables: [
        ['ErrorOptions', base_config_1.TYPE],
        ['Error', base_config_1.TYPE],
        ['ErrorConstructor', base_config_1.TYPE],
        ['EvalErrorConstructor', base_config_1.TYPE],
        ['RangeErrorConstructor', base_config_1.TYPE],
        ['ReferenceErrorConstructor', base_config_1.TYPE],
        ['SyntaxErrorConstructor', base_config_1.TYPE],
        ['TypeErrorConstructor', base_config_1.TYPE],
        ['URIErrorConstructor', base_config_1.TYPE],
        ['AggregateErrorConstructor', base_config_1.TYPE],
    ],
};
