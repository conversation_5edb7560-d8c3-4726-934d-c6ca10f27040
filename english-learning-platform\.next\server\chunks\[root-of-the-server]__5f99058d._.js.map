{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI!;\n\nif (!MONGODB_URI) {\n  throw new Error('<PERSON>ui lòng định nghĩa biến môi trường MONGODB_URI trong .env.local');\n}\n\n/**\n * Global được sử dụng để duy trì kết nối cached trong môi trường development.\n * Điều này ngăn chặn việc tạo quá nhiều kết nối trong quá trình hot reloading.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Kết nối MongoDB thành công');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    console.error('❌ Lỗi kết nối MongoDB:', e);\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;CAGC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/types/index.ts"], "sourcesContent": ["// Enum types\nexport enum UserRole {\n  ADMIN = 'admin',\n  TEACHER = 'teacher',\n  STUDENT = 'student'\n}\n\nexport enum CourseCategory {\n  LISTENING = 'listening',\n  SPEAKING = 'speaking',\n  READING = 'reading',\n  WRITING = 'writing',\n  COMPREHENSIVE = 'comprehensive'\n}\n\nexport enum CourseLevel {\n  BEGINNER = 'beginner',\n  INTERMEDIATE = 'intermediate',\n  ADVANCED = 'advanced'\n}\n\nexport enum TestType {\n  VOCABULARY = 'vocabulary',\n  GRAMMAR = 'grammar',\n  LISTENING = 'listening',\n  SPEAKING = 'speaking',\n  READING = 'reading',\n  WRITING = 'writing',\n  COMPREHENSIVE = 'comprehensive',\n  PRACTICE = 'practice'\n}\n\nexport enum QuestionType {\n  MULTIPLE_CHOICE = 'multiple_choice',\n  FILL_IN_BLANK = 'fill_in_blank',\n  TRUE_FALSE = 'true_false',\n  ESSAY = 'essay',\n  AUDIO_RESPONSE = 'audio_response',\n  DRAG_DROP = 'drag_drop',\n  MATCHING = 'matching',\n  // Enhanced question types for language skills\n  LISTENING_MULTIPLE_CHOICE = 'listening_multiple_choice',\n  LISTENING_FILL_BLANK = 'listening_fill_blank',\n  SPEAKING_RECORD = 'speaking_record',\n  READING_COMPREHENSION = 'reading_comprehension',\n  WRITING_ESSAY = 'writing_essay',\n  WRITING_SHORT_ANSWER = 'writing_short_answer'\n}\n\nexport enum PaymentStatus {\n  PENDING = 'pending',\n  COMPLETED = 'completed',\n  FAILED = 'failed'\n}\n\n// User types\nexport interface User {\n  _id: string;\n  email: string;\n  name: string;\n  role: UserRole;\n  avatar?: string;\n  phone?: string;\n  dateOfBirth?: Date;\n  isEmailVerified: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n  profile?: UserProfile;\n}\n\nexport interface UserProfile {\n  bio?: string;\n  experience?: string; // for teachers\n  education?: string;\n  skills?: string[];\n}\n\n// Course types\nexport interface Course {\n  _id: string;\n  title: string;\n  description: string;\n  shortDescription: string;\n  teacherId: string;\n  teacher?: User;\n  category: CourseCategory;\n  level: CourseLevel;\n  price: number;\n  duration: number; // in hours\n  thumbnail?: string;\n  videoIntro?: string;\n  isPublished: boolean;\n  curriculum: Lesson[];\n  ratings: Rating[];\n  averageRating: number;\n  totalStudents: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Lesson {\n  title: string;\n  description: string;\n  videoUrl?: string;\n  materials: string[];\n  duration: number; // in minutes\n}\n\nexport interface Rating {\n  userId: string;\n  user?: User;\n  rating: number;\n  comment: string;\n  createdAt: Date;\n}\n\n// Enrollment types\nexport interface Enrollment {\n  _id: string;\n  studentId: string;\n  student?: User;\n  courseId: string;\n  course?: Course;\n  enrolledAt: Date;\n  progress: number; // percentage\n  completedLessons: number[];\n  lastAccessedAt: Date;\n  paymentStatus: PaymentStatus;\n  paymentId?: string;\n}\n\n// Test types\nexport interface Test {\n  _id: string;\n  title: string;\n  description: string;\n  type: TestType;\n  level: CourseLevel;\n  duration: number; // in minutes\n  totalQuestions: number;\n  passingScore: number;\n  questions: Question[];\n  createdBy: string;\n  creator?: User;\n  isPublished: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Question {\n  _id?: string;\n  type: QuestionType;\n  question: string;\n  options?: string[]; // for multiple choice\n  correctAnswer: string | string[]; // can be array for multiple correct answers\n  points: number;\n  order?: number;\n  explanation?: string;\n  // Enhanced fields for language skills\n  audioUrl?: string; // for listening questions\n  audioSegments?: AudioSegment[]; // multiple audio files\n  imageUrl?: string;\n  readingPassage?: string; // for reading comprehension\n  wordLimit?: number; // for writing questions\n  timeLimit?: number; // for speaking questions (in seconds)\n  keywords?: string[]; // for essay scoring\n  rubric?: GradingRubric; // for manual grading\n  autoGrade?: boolean; // whether to use automatic grading\n}\n\nexport interface AudioSegment {\n  url: string;\n  title: string;\n  duration: number; // in seconds\n  transcript?: string; // for reference\n}\n\nexport interface GradingRubric {\n  criteria: GradingCriterion[];\n  maxScore: number;\n}\n\nexport interface GradingCriterion {\n  name: string;\n  description: string;\n  maxPoints: number;\n  levels: GradingLevel[];\n}\n\nexport interface GradingLevel {\n  score: number;\n  description: string;\n}\n\nexport interface TestResult {\n  _id: string;\n  studentId: string;\n  student?: User;\n  testId: string;\n  test?: Test;\n  answers: Answer[];\n  totalScore: number;\n  percentage: number;\n  timeSpent: number; // in minutes\n  startedAt: Date;\n  completedAt: Date;\n  feedback?: string;\n}\n\nexport interface Answer {\n  questionId: string;\n  questionIndex: number;\n  answer: string | string[]; // can be array for multiple answers\n  isCorrect: boolean;\n  points: number;\n  maxPoints: number;\n  // Enhanced fields for different answer types\n  audioUrl?: string; // for speaking responses\n  audioBlob?: Blob; // for client-side audio data\n  timeSpent?: number; // time spent on question (in seconds)\n  attempts?: number; // number of attempts\n  feedback?: string; // teacher feedback\n  gradedBy?: string; // teacher who graded (for manual grading)\n  gradedAt?: Date; // when it was graded\n  rubricScores?: RubricScore[]; // detailed scoring for essays/speaking\n}\n\nexport interface RubricScore {\n  criterionId: string;\n  criterionName: string;\n  score: number;\n  maxScore: number;\n  feedback?: string;\n}\n\n// API Response types\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\n// Form types\nexport interface LoginForm {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterForm {\n  name: string;\n  email: string;\n  password: string;\n  confirmPassword: string;\n  role?: UserRole;\n}\n\nexport interface CourseForm {\n  title: string;\n  description: string;\n  shortDescription: string;\n  category: CourseCategory;\n  level: CourseLevel;\n  price: number;\n  duration: number;\n  thumbnail?: string;\n  videoIntro?: string;\n  curriculum: Lesson[];\n}\n\n// Dashboard types\nexport interface DashboardStats {\n  totalCourses: number;\n  totalStudents: number;\n  totalRevenue: number;\n  totalTests: number;\n}\n\nexport interface StudentDashboard {\n  enrolledCourses: Course[];\n  recentTests: TestResult[];\n  progress: {\n    courseId: string;\n    courseName: string;\n    progress: number;\n  }[];\n  stats: {\n    totalCourses: number;\n    completedCourses: number;\n    averageScore: number;\n    totalTestsTaken: number;\n  };\n}\n"], "names": [], "mappings": "AAAA,aAAa;;;;;;;;;AACN,IAAA,AAAK,kCAAA;;;;WAAA;;AAML,IAAA,AAAK,wCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,qCAAA;;;;WAAA;;AAML,IAAA,AAAK,kCAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,sCAAA;;;;;;;;IAQV,8CAA8C;;;;;;;WARpC;;AAiBL,IAAA,AAAK,uCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\nimport { UserRole } from '@/types';\n\nexport interface IUser extends Document {\n  email: string;\n  password: string;\n  name: string;\n  role: UserRole;\n  avatar?: string;\n  phone?: string;\n  dateOfBirth?: Date;\n  isEmailVerified: boolean;\n  profile?: {\n    bio?: string;\n    experience?: string;\n    education?: string;\n    skills?: string[];\n  };\n  createdAt: Date;\n  updatedAt: Date;\n  comparePassword(candidatePassword: string): Promise<boolean>;\n}\n\nconst UserSchema = new Schema<IUser>({\n  email: {\n    type: String,\n    required: [true, 'Email là bắt buộc'],\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: [/^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/, 'Email không hợp lệ']\n  },\n  password: {\n    type: String,\n    required: [true, '<PERSON>ật khẩu là bắt buộc'],\n    minlength: [6, 'Mật khẩu phải có ít nhất 6 ký tự']\n  },\n  name: {\n    type: String,\n    required: [true, 'Tên là bắt buộc'],\n    trim: true,\n    maxlength: [100, 'Tên không được vượt quá 100 ký tự']\n  },\n  role: {\n    type: String,\n    enum: Object.values(UserRole),\n    default: UserRole.STUDENT\n  },\n  avatar: {\n    type: String,\n    default: null\n  },\n  phone: {\n    type: String,\n    match: [/^[0-9]{10,11}$/, 'Số điện thoại không hợp lệ']\n  },\n  dateOfBirth: {\n    type: Date\n  },\n  isEmailVerified: {\n    type: Boolean,\n    default: false\n  },\n  profile: {\n    bio: {\n      type: String,\n      maxlength: [500, 'Bio không được vượt quá 500 ký tự']\n    },\n    experience: {\n      type: String,\n      maxlength: [1000, 'Kinh nghiệm không được vượt quá 1000 ký tự']\n    },\n    education: {\n      type: String,\n      maxlength: [500, 'Học vấn không được vượt quá 500 ký tự']\n    },\n    skills: [{\n      type: String,\n      maxlength: [50, 'Kỹ năng không được vượt quá 50 ký tự']\n    }]\n  }\n}, {\n  timestamps: true\n});\n\n// Index cho tìm kiếm\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\n\n// Hash password trước khi lưu\nUserSchema.pre('save', async function(next) {\n  if (!this.isModified('password')) return next();\n  \n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error as Error);\n  }\n});\n\n// Method để so sánh password\nUserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Loại bỏ password khỏi JSON response\nUserSchema.methods.toJSON = function() {\n  const userObject = this.toObject();\n  delete userObject.password;\n  return userObject;\n};\n\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAsBA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAQ;IACnC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;YAAC;YAA+C;SAAqB;IAC9E;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAAmC;IACpD;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAkB;QACnC,MAAM;QACN,WAAW;YAAC;YAAK;SAAoC;IACvD;IACA,MAAM;QACJ,MAAM;QACN,MAAM,OAAO,MAAM,CAAC,uHAAA,CAAA,WAAQ;QAC5B,SAAS,uHAAA,CAAA,WAAQ,CAAC,OAAO;IAC3B;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,OAAO;YAAC;YAAkB;SAA6B;IACzD;IACA,aAAa;QACX,MAAM;IACR;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,SAAS;QACP,KAAK;YACH,MAAM;YACN,WAAW;gBAAC;gBAAK;aAAoC;QACvD;QACA,YAAY;YACV,MAAM;YACN,WAAW;gBAAC;gBAAM;aAA6C;QACjE;QACA,WAAW;YACT,MAAM;YACN,WAAW;gBAAC;gBAAK;aAAwC;QAC3D;QACA,QAAQ;YAAC;gBACP,MAAM;gBACN,WAAW;oBAAC;oBAAI;iBAAuC;YACzD;SAAE;IACJ;AACF,GAAG;IACD,YAAY;AACd;AAEA,qBAAqB;AACrB,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAE3B,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAe,IAAI;IACxC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,6BAA6B;AAC7B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAyB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,sCAAsC;AACtC,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO;AACT;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport User from '@/models/User';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';\n\nexport interface AuthUser {\n  userId: string;\n  email: string;\n  role: string;\n}\n\nexport async function verifyToken(token: string): Promise<AuthUser | null> {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET) as AuthUser;\n    return decoded;\n  } catch (error) {\n    console.error('Token verification failed:', error);\n    return null;\n  }\n}\n\nexport async function getAuthUser(request: NextRequest): Promise<AuthUser | null> {\n  try {\n    // Try to get token from cookie first\n    let token = request.cookies.get('auth-token')?.value;\n    \n    // If no cookie, try Authorization header\n    if (!token) {\n      const authHeader = request.headers.get('authorization');\n      if (authHeader && authHeader.startsWith('Bearer ')) {\n        token = authHeader.substring(7);\n      }\n    }\n\n    if (!token) {\n      return null;\n    }\n\n    return await verifyToken(token);\n  } catch (error) {\n    console.error('Get auth user failed:', error);\n    return null;\n  }\n}\n\nexport async function requireAuth(request: NextRequest): Promise<{ user: AuthUser } | { error: Response }> {\n  const user = await getAuthUser(request);\n  \n  if (!user) {\n    return {\n      error: new Response(\n        JSON.stringify({\n          success: false,\n          message: 'Unauthorized - Token required'\n        }),\n        { \n          status: 401,\n          headers: { 'Content-Type': 'application/json' }\n        }\n      )\n    };\n  }\n\n  return { user };\n}\n\nexport async function requireRole(request: NextRequest, allowedRoles: string[]): Promise<{ user: AuthUser } | { error: Response }> {\n  const authResult = await requireAuth(request);\n  \n  if ('error' in authResult) {\n    return authResult;\n  }\n\n  if (!allowedRoles.includes(authResult.user.role)) {\n    return {\n      error: new Response(\n        JSON.stringify({\n          success: false,\n          message: 'Forbidden - Insufficient permissions'\n        }),\n        { \n          status: 403,\n          headers: { 'Content-Type': 'application/json' }\n        }\n      )\n    };\n  }\n\n  return authResult;\n}\n\nexport async function getCurrentUser(userId: string) {\n  try {\n    await connectDB();\n    const user = await User.findById(userId).select('-password');\n    return user;\n  } catch (error) {\n    console.error('Get current user failed:', error);\n    return null;\n  }\n}\n\n// Helper function to generate JWT token\nexport function generateToken(payload: { userId: string; email: string; role: string }): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });\n}\n\n// Helper function to hash password (for password reset, etc.)\nexport async function hashPassword(password: string): Promise<string> {\n  const bcrypt = require('bcryptjs');\n  const salt = await bcrypt.genSalt(12);\n  return bcrypt.hash(password, salt);\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;AACA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAQtC,eAAe,YAAY,KAAa;IAC7C,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAEO,eAAe,YAAY,OAAoB;IACpD,IAAI;QACF,qCAAqC;QACrC,IAAI,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAE/C,yCAAyC;QACzC,IAAI,CAAC,OAAO;YACV,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;YACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;gBAClD,QAAQ,WAAW,SAAS,CAAC;YAC/B;QACF;QAEA,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,OAAO,MAAM,YAAY;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF;AAEO,eAAe,YAAY,OAAoB;IACpD,MAAM,OAAO,MAAM,YAAY;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;YACL,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBACb,SAAS;gBACT,SAAS;YACX,IACA;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;IACF;IAEA,OAAO;QAAE;IAAK;AAChB;AAEO,eAAe,YAAY,OAAoB,EAAE,YAAsB;IAC5E,MAAM,aAAa,MAAM,YAAY;IAErC,IAAI,WAAW,YAAY;QACzB,OAAO;IACT;IAEA,IAAI,CAAC,aAAa,QAAQ,CAAC,WAAW,IAAI,CAAC,IAAI,GAAG;QAChD,OAAO;YACL,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBACb,SAAS;gBACT,SAAS;YACX,IACA;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;IACF;IAEA,OAAO;AACT;AAEO,eAAe,eAAe,MAAc;IACjD,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAAQ,MAAM,CAAC;QAChD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAGO,SAAS,cAAc,OAAwD;IACpF,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAGO,eAAe,aAAa,QAAgB;IACjD,MAAM;IACN,MAAM,OAAO,MAAM,OAAO,OAAO,CAAC;IAClC,OAAO,OAAO,IAAI,CAAC,UAAU;AAC/B", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/app/api/auth/me/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getAuthUser, getCurrentUser } from '@/lib/auth';\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Get authenticated user from token\n    const authUser = await getAuthUser(request);\n    \n    if (!authUser) {\n      return NextResponse.json({\n        success: false,\n        message: 'Unauthorized'\n      }, { status: 401 });\n    }\n\n    // Get full user data from database\n    const user = await getCurrentUser(authUser.userId);\n    \n    if (!user) {\n      return NextResponse.json({\n        success: false,\n        message: 'User not found'\n      }, { status: 404 });\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: user\n    });\n\n  } catch (error) {\n    console.error('Get current user error:', error);\n    \n    return NextResponse.json({\n      success: false,\n      message: 'Lỗi hệ thống, vui lòng thử lại sau',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,oCAAoC;QACpC,MAAM,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAEnC,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,mCAAmC;QACnC,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,MAAM;QAEjD,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}