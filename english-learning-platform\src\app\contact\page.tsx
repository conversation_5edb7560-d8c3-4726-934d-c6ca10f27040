"use client";

import { useState } from "react";
import { Mail, Phone, MapPin, Clock, Send, MessageSquare, HelpCircle, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Header from "@/components/layout/Header";
import { toast } from "sonner";

const contactMethods = [
  {
    icon: Mail,
    title: "Email",
    description: "Gửi email cho chúng tôi",
    contact: "<EMAIL>",
    action: "mailto:<EMAIL>"
  },
  {
    icon: Phone,
    title: "Điện thoại",
    description: "<PERSON><PERSON>i điện trực tiếp",
    contact: "0123 456 789",
    action: "tel:0123456789"
  },
  {
    icon: MessageSquare,
    title: "Live Chat",
    description: "<PERSON><PERSON> tr<PERSON><PERSON> tiế<PERSON> với tư vấn viên",
    contact: "<PERSON>r<PERSON><PERSON> tuyến 24/7",
    action: "#"
  },
  {
    icon: MapPin,
    title: "Địa chỉ",
    description: "Ghé thăm văn phòng của chúng tôi",
    contact: "123 Đường ABC, Quận 1, TP.HCM",
    action: "#"
  }
];

const faqs = [
  {
    question: "Làm thế nào để đăng ký khóa học?",
    answer: "Bạn có thể đăng ký khóa học bằng cách tạo tài khoản, chọn khóa học phù hợp và thanh toán trực tuyến. Chúng tôi hỗ trợ nhiều phương thức thanh toán khác nhau."
  },
  {
    question: "Tôi có thể học thử miễn phí không?",
    answer: "Có, chúng tôi cung cấp bài học thử miễn phí cho tất cả khóa học. Bạn cũng có thể tham gia các bài kiểm tra trình độ miễn phí."
  },
  {
    question: "Chính sách hoàn tiền như thế nào?",
    answer: "Chúng tôi có chính sách hoàn tiền 100% trong vòng 30 ngày đầu nếu bạn không hài lòng với khóa học."
  },
  {
    question: "Tôi có nhận được chứng chỉ sau khi hoàn thành khóa học không?",
    answer: "Có, bạn sẽ nhận được chứng chỉ hoàn thành có giá trị sau khi hoàn thành tất cả bài học và đạt điểm yêu cầu trong bài kiểm tra cuối khóa."
  },
  {
    question: "Làm sao để liên hệ với giáo viên?",
    answer: "Bạn có thể liên hệ trực tiếp với giáo viên thông qua hệ thống tin nhắn trong khóa học hoặc tham gia các buổi Q&A trực tuyến."
  }
];

const officeHours = [
  { day: "Thứ 2 - Thứ 6", hours: "8:00 - 18:00" },
  { day: "Thứ 7", hours: "9:00 - 17:00" },
  { day: "Chủ nhật", hours: "10:00 - 16:00" }
];

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: ""
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false);
      toast.success("Cảm ơn bạn đã liên hệ! Chúng tôi sẽ phản hồi trong vòng 24 giờ.");
      setFormData({
        name: "",
        email: "",
        phone: "",
        subject: "",
        message: ""
      });
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Liên hệ với chúng tôi</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Chúng tôi luôn sẵn sàng hỗ trợ bạn. Hãy liên hệ với chúng tôi qua bất kỳ kênh nào dưới đây.
          </p>
        </div>

        {/* Contact Methods */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {contactMethods.map((method, index) => (
            <Card key={index} className="text-center hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <method.icon className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle className="text-lg">{method.title}</CardTitle>
                <CardDescription>{method.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="font-medium text-gray-900">{method.contact}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Send className="h-5 w-5 mr-2" />
                Gửi tin nhắn
              </CardTitle>
              <CardDescription>
                Điền thông tin dưới đây và chúng tôi sẽ phản hồi sớm nhất có thể
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">
                      Họ và tên *
                    </label>
                    <Input
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="Nhập họ và tên"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">
                      Số điện thoại
                    </label>
                    <Input
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="Nhập số điện thoại"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Email *
                  </label>
                  <Input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Chủ đề *
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={formData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    required
                  >
                    <option value="">Chọn chủ đề</option>
                    <option value="course-inquiry">Tư vấn khóa học</option>
                    <option value="technical-support">Hỗ trợ kỹ thuật</option>
                    <option value="payment-issue">Vấn đề thanh toán</option>
                    <option value="partnership">Hợp tác</option>
                    <option value="other">Khác</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Tin nhắn *
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={5}
                    value={formData.message}
                    onChange={(e) => handleInputChange('message', e.target.value)}
                    placeholder="Nhập tin nhắn của bạn..."
                    required
                  />
                </div>

                <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? (
                    "Đang gửi..."
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Gửi tin nhắn
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Office Info & FAQ */}
          <div className="space-y-6">
            {/* Office Hours */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Giờ làm việc
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {officeHours.map((schedule, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-gray-700">{schedule.day}</span>
                      <Badge variant="outline">{schedule.hours}</Badge>
                    </div>
                  ))}
                </div>
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Lưu ý:</strong> Hỗ trợ trực tuyến 24/7 qua chat và email
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* FAQ */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <HelpCircle className="h-5 w-5 mr-2" />
                  Câu hỏi thường gặp
                </CardTitle>
                <CardDescription>
                  Một số câu hỏi phổ biến từ học viên
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {faqs.slice(0, 3).map((faq, index) => (
                    <div key={index} className="border-b border-gray-200 pb-4 last:border-b-0">
                      <h3 className="font-medium text-gray-900 mb-2">{faq.question}</h3>
                      <p className="text-sm text-gray-600">{faq.answer}</p>
                    </div>
                  ))}
                </div>
                <div className="mt-4">
                  <Button variant="outline" className="w-full">
                    Xem tất cả FAQ
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Quick Support */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  Hỗ trợ nhanh
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Chat với tư vấn viên
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Phone className="h-4 w-4 mr-2" />
                  Gọi hotline: 0123 456 789
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Mail className="h-4 w-4 mr-2" />
                  Email: <EMAIL>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Map Section */}
        <Card className="mt-12">
          <CardHeader>
            <CardTitle>Vị trí văn phòng</CardTitle>
            <CardDescription>
              Ghé thăm văn phòng của chúng tôi tại trung tâm thành phố
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-200 rounded-lg h-64 flex items-center justify-center">
              <p className="text-gray-500">Google Maps sẽ được tích hợp ở đây</p>
            </div>
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                <span>123 Đường ABC, Quận 1, TP.HCM</span>
              </div>
              <div className="flex items-center">
                <Phone className="h-4 w-4 mr-2 text-gray-400" />
                <span>0123 456 789</span>
              </div>
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2 text-gray-400" />
                <span><EMAIL></span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
