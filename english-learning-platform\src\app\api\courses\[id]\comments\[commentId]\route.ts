import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Comment from '@/models/Comment';
import { getAuthUser } from '@/lib/auth';

interface RouteParams {
  params: {
    id: string;
    commentId: string;
  };
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { commentId } = params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find comment
    const comment = await Comment.findById(commentId);
    if (!comment || comment.isDeleted) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy bình luận'
      }, { status: 404 });
    }

    // Check permission to edit
    if (!comment.canEdit(user.userId, user.role)) {
      return NextResponse.json({
        success: false,
        message: '<PERSON>ạ<PERSON> không có quyền chỉnh sửa bình luận này'
      }, { status: 403 });
    }

    const body = await request.json();
    const { content, isResolved } = body;

    // Update content if provided
    if (content !== undefined) {
      if (!content || content.trim().length < 3) {
        return NextResponse.json({
          success: false,
          message: 'Nội dung bình luận phải có ít nhất 3 ký tự'
        }, { status: 400 });
      }

      if (content.length > 2000) {
        return NextResponse.json({
          success: false,
          message: 'Nội dung bình luận không được vượt quá 2000 ký tự'
        }, { status: 400 });
      }

      comment.content = content.trim();
    }

    // Update resolved status for questions (instructor only)
    if (isResolved !== undefined && comment.type === 'question') {
      if (comment.isInstructor || user.role === 'admin') {
        comment.isResolved = isResolved;
      }
    }

    await comment.save();

    // Populate user info
    await comment.populate('userId', 'name avatar role');

    return NextResponse.json({
      success: true,
      message: 'Đã cập nhật bình luận',
      data: comment
    });

  } catch (error) {
    console.error('Update comment error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi cập nhật bình luận',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { commentId } = params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find comment
    const comment = await Comment.findById(commentId);
    if (!comment || comment.isDeleted) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy bình luận'
      }, { status: 404 });
    }

    // Check permission to delete
    if (!comment.canDelete(user.userId, user.role)) {
      return NextResponse.json({
        success: false,
        message: 'Bạn không có quyền xóa bình luận này'
      }, { status: 403 });
    }

    // Soft delete comment
    await comment.softDelete();

    return NextResponse.json({
      success: true,
      message: 'Đã xóa bình luận'
    });

  } catch (error) {
    console.error('Delete comment error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi xóa bình luận',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Like/Unlike comment
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { commentId } = params;
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action'); // 'like' or 'dislike'
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find comment
    const comment = await Comment.findById(commentId);
    if (!comment || comment.isDeleted) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy bình luận'
      }, { status: 404 });
    }

    let result;
    if (action === 'like') {
      result = comment.toggleLike(user.userId);
    } else if (action === 'dislike') {
      result = comment.toggleDislike(user.userId);
    } else {
      return NextResponse.json({
        success: false,
        message: 'Action không hợp lệ'
      }, { status: 400 });
    }

    await comment.save();

    return NextResponse.json({
      success: true,
      message: `Đã ${result.action}`,
      data: {
        action: result.action,
        likeCount: comment.likes.length,
        dislikeCount: comment.dislikes.length
      }
    });

  } catch (error) {
    console.error('Like/dislike comment error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi thực hiện action',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
