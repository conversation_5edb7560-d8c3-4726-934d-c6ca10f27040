"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Search, Filter, Star, Clock, Users, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { LoadingCard } from "@/components/ui/loading";
import { ErrorCard } from "@/components/ui/error";
import Header from "@/components/layout/Header";
import { formatPrice } from "@/lib/utils";

// Mock data for courses
const mockCourses = [
  {
    id: "1",
    title: "Tiếng Anh Cơ <PERSON>ản cho Người <PERSON>ới <PERSON>",
    description: "<PERSON>h<PERSON><PERSON> học dành cho những người chưa có kiến thức về tiếng <PERSON>, bắ<PERSON> đầu từ những kiến thức cơ bản nhất.",
    shortDescription: "<PERSON>ọ<PERSON> tiếng <PERSON> từ con số 0 với phương pháp dễ hiểu và thực tế.",
    category: "comprehensive",
    level: "beginner",
    price: 299000,
    duration: 40,
    thumbnail: "/api/placeholder/300/200",
    teacher: {
      name: "Cô Minh Anh",
      avatar: "/api/placeholder/40/40"
    },
    averageRating: 4.8,
    totalStudents: 1250,
    totalLessons: 25
  },
  {
    id: "2",
    title: "Luyện Nghe IELTS 6.5+",
    description: "Khóa học tập trung vào kỹ năng nghe IELTS với các bài tập thực hành từ cơ bản đến nâng cao.",
    shortDescription: "Nâng cao kỹ năng nghe IELTS với các chiến lược hiệu quả.",
    category: "listening",
    level: "intermediate",
    price: 499000,
    duration: 30,
    thumbnail: "/api/placeholder/300/200",
    teacher: {
      name: "Thầy Đức Minh",
      avatar: "/api/placeholder/40/40"
    },
    averageRating: 4.9,
    totalStudents: 890,
    totalLessons: 20
  },
  {
    id: "3",
    title: "Tiếng Anh Giao Tiếp Thực Tế",
    description: "Học cách giao tiếp tiếng Anh tự nhiên trong các tình huống hàng ngày và công việc.",
    shortDescription: "Giao tiếp tiếng Anh tự tin trong mọi tình huống.",
    category: "speaking",
    level: "intermediate",
    price: 399000,
    duration: 35,
    thumbnail: "/api/placeholder/300/200",
    teacher: {
      name: "Cô Thu Hà",
      avatar: "/api/placeholder/40/40"
    },
    averageRating: 4.7,
    totalStudents: 2100,
    totalLessons: 28
  },
  {
    id: "4",
    title: "Luyện Viết IELTS Writing Task 1 & 2",
    description: "Khóa học chuyên sâu về kỹ năng viết IELTS với các mẫu bài và chiến lược làm bài hiệu quả.",
    shortDescription: "Chinh phục IELTS Writing với phương pháp khoa học.",
    category: "writing",
    level: "advanced",
    price: 599000,
    duration: 25,
    thumbnail: "/api/placeholder/300/200",
    teacher: {
      name: "Thầy Quang Huy",
      avatar: "/api/placeholder/40/40"
    },
    averageRating: 4.6,
    totalStudents: 650,
    totalLessons: 18
  }
];

const categories = [
  { value: "all", label: "Tất cả" },
  { value: "comprehensive", label: "Tổng hợp" },
  { value: "listening", label: "Nghe" },
  { value: "speaking", label: "Nói" },
  { value: "reading", label: "Đọc" },
  { value: "writing", label: "Viết" }
];

const levels = [
  { value: "all", label: "Tất cả trình độ" },
  { value: "beginner", label: "Cơ bản" },
  { value: "intermediate", label: "Trung cấp" },
  { value: "advanced", label: "Nâng cao" }
];

export default function CoursesPage() {
  const [courses, setCourses] = useState(mockCourses);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedLevel, setSelectedLevel] = useState("all");

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const breadcrumbItems = [
    { label: "Khóa học", current: true }
  ];

  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || course.category === selectedCategory;
    const matchesLevel = selectedLevel === "all" || course.level === selectedLevel;

    return matchesSearch && matchesCategory && matchesLevel;
  });

  const handleRetry = () => {
    setIsLoading(true);
    setError(null);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <Breadcrumb items={breadcrumbItems} className="mb-6" />

        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Khóa Học Tiếng Anh</h1>
          <p className="text-lg text-gray-600">
            Khám phá các khóa học tiếng Anh chất lượng cao từ các giáo viên chuyên nghiệp
          </p>
        </div>

        {/* Search and Filter */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Tìm kiếm khóa học..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <select
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                {categories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
              <select
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
              >
                {levels.map(level => (
                  <option key={level.value} value={level.value}>
                    {level.label}
                  </option>
                ))}
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Lọc ({filteredCourses.length})
              </Button>
            </div>
          </div>
        </div>

        {/* Course Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, index) => (
              <LoadingCard key={index} />
            ))}
          </div>
        ) : error ? (
          <ErrorCard
            title="Không thể tải khóa học"
            message={error}
            onRetry={handleRetry}
          />
        ) : filteredCourses.length === 0 ? (
          <div className="text-center py-12">
            <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Không tìm thấy khóa học
            </h3>
            <p className="text-gray-600">
              Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredCourses.map(course => (
            <Card key={course.id} className="hover:shadow-lg transition-shadow">
              <div className="aspect-video bg-gray-200 rounded-t-lg"></div>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                    {course.level === 'beginner' ? 'Cơ bản' : 
                     course.level === 'intermediate' ? 'Trung cấp' : 'Nâng cao'}
                  </span>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span className="text-sm text-gray-600 ml-1">{course.averageRating}</span>
                  </div>
                </div>
                <CardTitle className="text-lg line-clamp-2">{course.title}</CardTitle>
                <CardDescription className="line-clamp-2">
                  {course.shortDescription}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center text-sm text-gray-500 mb-3">
                  <div className="flex items-center mr-4">
                    <Clock className="h-4 w-4 mr-1" />
                    {course.duration}h
                  </div>
                  <div className="flex items-center mr-4">
                    <BookOpen className="h-4 w-4 mr-1" />
                    {course.totalLessons} bài
                  </div>
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-1" />
                    {course.totalStudents}
                  </div>
                </div>
                
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gray-300 rounded-full mr-2"></div>
                    <span className="text-sm text-gray-600">{course.teacher.name}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatPrice(course.price)}
                  </div>
                  <Link href={`/courses/${course.id}`}>
                    <Button size="sm">
                      Xem chi tiết
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
            ))}
          </div>
        )}

        {/* Load More */}
        {!isLoading && !error && filteredCourses.length > 0 && (
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Xem thêm khóa học
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
