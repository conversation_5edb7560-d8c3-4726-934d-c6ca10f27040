"use client";

import { useState, useEffect } from "react";
import { Search, Filter, Play, Pause, FileText, Mic, Clock, User, Star } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Slider } from "@/components/ui/slider";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import Header from "@/components/layout/Header";
import { toast } from "sonner";

interface PendingGrading {
  _id: string;
  studentName: string;
  testTitle: string;
  questionType: string;
  submittedAt: Date;
  answer: string | string[];
  audioUrl?: string;
  question: {
    question: string;
    points: number;
    rubric?: any;
  };
  priority: 'high' | 'medium' | 'low';
}

export default function TeacherGradingPage() {
  const [pendingItems, setPendingItems] = useState<PendingGrading[]>([]);
  const [selectedItem, setSelectedItem] = useState<PendingGrading | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [score, setScore] = useState<number[]>([0]);
  const [feedback, setFeedback] = useState("");
  const [isGrading, setIsGrading] = useState(false);
  const [audioPlaying, setAudioPlaying] = useState(false);

  useEffect(() => {
    loadPendingGrading();
  }, []);

  const loadPendingGrading = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/grading/pending');
      const data = await response.json();
      
      if (data.success) {
        setPendingItems(data.data);
      } else {
        toast.error(data.message || 'Lỗi khi tải danh sách chấm điểm');
      }
    } catch (error) {
      console.error('Load pending grading error:', error);
      toast.error('Lỗi kết nối. Vui lòng thử lại.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGradeSubmit = async () => {
    if (!selectedItem) return;

    setIsGrading(true);
    try {
      const response = await fetch('/api/grading/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          gradingId: selectedItem._id,
          score: score[0],
          maxScore: selectedItem.question.points,
          feedback: feedback.trim(),
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Chấm điểm thành công!');
        // Remove graded item from list
        setPendingItems(prev => prev.filter(item => item._id !== selectedItem._id));
        setSelectedItem(null);
        setScore([0]);
        setFeedback("");
      } else {
        toast.error(data.message || 'Lỗi khi nộp điểm');
      }
    } catch (error) {
      console.error('Submit grading error:', error);
      toast.error('Lỗi kết nối. Vui lòng thử lại.');
    } finally {
      setIsGrading(false);
    }
  };

  const filteredItems = pendingItems.filter(item => {
    const matchesSearch = item.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.testTitle.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === "all" || item.questionType === filterType;
    return matchesSearch && matchesFilter;
  });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Chấm Điểm Bài Thi</h1>
          <p className="text-lg text-gray-600">
            Chấm điểm các câu trả lời cần đánh giá thủ công
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Chờ chấm</p>
                  <p className="text-2xl font-bold text-gray-900">{pendingItems.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Mic className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Bài nói</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {pendingItems.filter(item => item.questionType === 'speaking_record').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Bài viết</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {pendingItems.filter(item => item.questionType === 'writing_essay').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-amber-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Ưu tiên cao</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {pendingItems.filter(item => item.priority === 'high').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Pending Items List */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Danh sách chờ chấm</CardTitle>
                
                {/* Search and Filter */}
                <div className="space-y-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Tìm kiếm học sinh hoặc bài thi..."
                      className="pl-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Lọc theo loại câu hỏi" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tất cả</SelectItem>
                      <SelectItem value="speaking_record">Bài nói</SelectItem>
                      <SelectItem value="writing_essay">Bài viết</SelectItem>
                      <SelectItem value="writing_short_answer">Câu trả lời ngắn</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              
              <CardContent className="p-0">
                <div className="max-h-96 overflow-y-auto">
                  {filteredItems.map((item) => (
                    <div
                      key={item._id}
                      onClick={() => setSelectedItem(item)}
                      className={`p-4 border-b cursor-pointer hover:bg-gray-50 transition-colors ${
                        selectedItem?._id === item._id ? 'bg-blue-50 border-blue-200' : ''
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-gray-500" />
                          <span className="font-medium text-gray-900">{item.studentName}</span>
                        </div>
                        <Badge className={getPriorityColor(item.priority)}>
                          {item.priority}
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-1">{item.testTitle}</p>
                      <p className="text-xs text-gray-500">{formatDate(item.submittedAt)}</p>
                      
                      <div className="flex items-center gap-2 mt-2">
                        {item.questionType === 'speaking_record' && (
                          <Badge variant="outline" className="text-xs">
                            <Mic className="h-3 w-3 mr-1" />
                            Bài nói
                          </Badge>
                        )}
                        {item.questionType === 'writing_essay' && (
                          <Badge variant="outline" className="text-xs">
                            <FileText className="h-3 w-3 mr-1" />
                            Bài viết
                          </Badge>
                        )}
                        <span className="text-xs text-gray-500">
                          {item.question.points} điểm
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Grading Interface */}
          <div className="lg:col-span-2">
            {selectedItem ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Chấm điểm: {selectedItem.studentName}</span>
                    <Badge className={getPriorityColor(selectedItem.priority)}>
                      {selectedItem.priority}
                    </Badge>
                  </CardTitle>
                  <p className="text-gray-600">{selectedItem.testTitle}</p>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  {/* Question */}
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-gray-700 mb-2">Câu hỏi:</h4>
                    <p className="text-gray-800">{selectedItem.question.question}</p>
                    <p className="text-sm text-gray-500 mt-2">
                      Điểm tối đa: {selectedItem.question.points}
                    </p>
                  </div>

                  {/* Student Answer */}
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-700">Câu trả lời của học sinh:</h4>
                    
                    {selectedItem.questionType === 'speaking_record' && selectedItem.audioUrl && (
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-4 mb-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setAudioPlaying(!audioPlaying)}
                          >
                            {audioPlaying ? (
                              <Pause className="h-4 w-4 mr-2" />
                            ) : (
                              <Play className="h-4 w-4 mr-2" />
                            )}
                            {audioPlaying ? 'Tạm dừng' : 'Phát'}
                          </Button>
                          <span className="text-sm text-gray-600">Bản ghi âm</span>
                        </div>
                        <audio
                          src={selectedItem.audioUrl}
                          controls
                          className="w-full"
                          onPlay={() => setAudioPlaying(true)}
                          onPause={() => setAudioPlaying(false)}
                        />
                      </div>
                    )}
                    
                    {selectedItem.questionType === 'writing_essay' && (
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <div className="whitespace-pre-wrap text-gray-800">
                          {selectedItem.answer as string}
                        </div>
                        <div className="mt-4 text-sm text-gray-500">
                          Số từ: {(selectedItem.answer as string).split(/\s+/).length}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Scoring */}
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-700">Chấm điểm:</h4>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Điểm số:</span>
                        <span className="text-lg font-bold text-blue-600">
                          {score[0]} / {selectedItem.question.points}
                        </span>
                      </div>
                      <Slider
                        value={score}
                        onValueChange={setScore}
                        max={selectedItem.question.points}
                        step={0.5}
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">
                        Nhận xét và góp ý:
                      </label>
                      <Textarea
                        value={feedback}
                        onChange={(e) => setFeedback(e.target.value)}
                        placeholder="Nhập nhận xét chi tiết về bài làm của học sinh..."
                        className="min-h-[100px]"
                      />
                    </div>

                    <div className="flex gap-2">
                      <Button
                        onClick={handleGradeSubmit}
                        disabled={isGrading}
                        className="flex-1"
                      >
                        {isGrading ? 'Đang lưu...' : 'Lưu điểm'}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSelectedItem(null);
                          setScore([0]);
                          setFeedback("");
                        }}
                      >
                        Hủy
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-12 text-center">
                  <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Chọn bài cần chấm điểm
                  </h3>
                  <p className="text-gray-600">
                    Chọn một bài từ danh sách bên trái để bắt đầu chấm điểm
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
