import mongoose, { Document, Schema } from 'mongoose';

export interface IComment extends Document {
  userId: mongoose.Types.ObjectId;
  courseId: mongoose.Types.ObjectId;
  lessonId?: string;
  content: string;
  type: 'comment' | 'question' | 'answer';
  parentId?: mongoose.Types.ObjectId; // For replies
  isInstructor: boolean;
  isResolved?: boolean; // For questions
  likes: mongoose.Types.ObjectId[];
  dislikes: mongoose.Types.ObjectId[];
  isModerated: boolean;
  moderatedBy?: mongoose.Types.ObjectId;
  moderatedAt?: Date;
  moderationReason?: string;
  isDeleted: boolean;
  deletedAt?: Date;
  editedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const CommentSchema = new Schema<IComment>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'ID người dùng là bắt buộc']
  },
  courseId: {
    type: Schema.Types.ObjectId,
    ref: 'Course',
    required: [true, 'ID khóa học là bắt buộc']
  },
  lessonId: {
    type: String,
    index: true
  },
  content: {
    type: String,
    required: [true, 'Nội dung bình luận là bắt buộc'],
    maxlength: [2000, 'Nội dung không được vượt quá 2000 ký tự'],
    validate: {
      validator: function(v: string) {
        return v.trim().length >= 3;
      },
      message: 'Nội dung phải có ít nhất 3 ký tự'
    }
  },
  type: {
    type: String,
    enum: ['comment', 'question', 'answer'],
    default: 'comment',
    required: [true, 'Loại bình luận là bắt buộc']
  },
  parentId: {
    type: Schema.Types.ObjectId,
    ref: 'Comment',
    index: true
  },
  isInstructor: {
    type: Boolean,
    default: false
  },
  isResolved: {
    type: Boolean,
    default: false
  },
  likes: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  dislikes: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  isModerated: {
    type: Boolean,
    default: false
  },
  moderatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  moderatedAt: {
    type: Date
  },
  moderationReason: {
    type: String,
    maxlength: [500, 'Lý do kiểm duyệt không được vượt quá 500 ký tự']
  },
  isDeleted: {
    type: Boolean,
    default: false
  },
  deletedAt: {
    type: Date
  },
  editedAt: {
    type: Date
  }
}, {
  timestamps: true
});

// Indexes for performance
CommentSchema.index({ courseId: 1, lessonId: 1, createdAt: -1 });
CommentSchema.index({ userId: 1, createdAt: -1 });
CommentSchema.index({ parentId: 1, createdAt: 1 });
CommentSchema.index({ type: 1, isResolved: 1 });
CommentSchema.index({ isModerated: 1, isDeleted: 1 });

// Virtual for reply count
CommentSchema.virtual('replyCount', {
  ref: 'Comment',
  localField: '_id',
  foreignField: 'parentId',
  count: true,
  match: { isDeleted: false }
});

// Virtual for like count
CommentSchema.virtual('likeCount').get(function() {
  return this.likes ? this.likes.length : 0;
});

// Virtual for dislike count
CommentSchema.virtual('dislikeCount').get(function() {
  return this.dislikes ? this.dislikes.length : 0;
});

// Middleware to update editedAt when content is modified
CommentSchema.pre('save', function(next) {
  if (this.isModified('content') && !this.isNew) {
    this.editedAt = new Date();
  }
  next();
});

// Method to check if user can edit comment
CommentSchema.methods.canEdit = function(userId: string, userRole: string) {
  // User can edit their own comment within 15 minutes
  const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
  const isOwner = this.userId.toString() === userId;
  const isRecent = this.createdAt > fifteenMinutesAgo;
  const isAdmin = userRole === 'admin';
  
  return (isOwner && isRecent) || isAdmin;
};

// Method to check if user can delete comment
CommentSchema.methods.canDelete = function(userId: string, userRole: string) {
  const isOwner = this.userId.toString() === userId;
  const isAdmin = userRole === 'admin';
  const isModerator = userRole === 'teacher'; // Teachers can moderate in their courses
  
  return isOwner || isAdmin || isModerator;
};

// Method to soft delete comment
CommentSchema.methods.softDelete = function(deletedBy?: string) {
  this.isDeleted = true;
  this.deletedAt = new Date();
  this.content = '[Bình luận đã bị xóa]';
  return this.save();
};

// Method to like/unlike comment
CommentSchema.methods.toggleLike = function(userId: string) {
  const userObjectId = new mongoose.Types.ObjectId(userId);
  const likeIndex = this.likes.findIndex((id: mongoose.Types.ObjectId) => 
    id.toString() === userId
  );
  const dislikeIndex = this.dislikes.findIndex((id: mongoose.Types.ObjectId) => 
    id.toString() === userId
  );

  // Remove from dislikes if present
  if (dislikeIndex > -1) {
    this.dislikes.splice(dislikeIndex, 1);
  }

  // Toggle like
  if (likeIndex > -1) {
    this.likes.splice(likeIndex, 1);
    return { action: 'unliked', likeCount: this.likes.length };
  } else {
    this.likes.push(userObjectId);
    return { action: 'liked', likeCount: this.likes.length };
  }
};

// Method to dislike/undislike comment
CommentSchema.methods.toggleDislike = function(userId: string) {
  const userObjectId = new mongoose.Types.ObjectId(userId);
  const likeIndex = this.likes.findIndex((id: mongoose.Types.ObjectId) => 
    id.toString() === userId
  );
  const dislikeIndex = this.dislikes.findIndex((id: mongoose.Types.ObjectId) => 
    id.toString() === userId
  );

  // Remove from likes if present
  if (likeIndex > -1) {
    this.likes.splice(likeIndex, 1);
  }

  // Toggle dislike
  if (dislikeIndex > -1) {
    this.dislikes.splice(dislikeIndex, 1);
    return { action: 'undisliked', dislikeCount: this.dislikes.length };
  } else {
    this.dislikes.push(userObjectId);
    return { action: 'disliked', dislikeCount: this.dislikes.length };
  }
};

export default mongoose.models.Comment || mongoose.model<IComment>('Comment', CommentSchema);
