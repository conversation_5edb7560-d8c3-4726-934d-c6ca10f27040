import User from '@/models/User';
import bcrypt from 'bcryptjs';

const sampleUsers = [
  // Admin users
  {
    name: 'Admin System',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    profile: {
      bio: 'Quản trị viên hệ thống',
      skills: ['System Management', 'User Support']
    }
  },
  
  // Teacher users
  {
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    password: 'teacher123',
    role: 'teacher',
    phone: '0901234567',
    profile: {
      bio: 'Giáo viên tiếng Anh với 10 năm kinh nghiệm giảng dạy. Chuyên về IELTS và giao tiếp.',
      skills: ['IELTS Teaching', 'Business English', 'Conversation']
    }
  },
  {
    name: 'Th<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    password: 'teacher123',
    role: 'teacher',
    phone: '0901234568',
    profile: {
      bio: 'Chuyên gia luyện thi TOEIC và tiếng Anh thương mại. Có bằng TESOL quốc tế.',
      skills: ['TOEIC Preparation', 'Business English', 'Grammar']
    }
  },
  {
    name: 'Cô Thu Hà',
    email: '<EMAIL>',
    password: 'teacher123',
    role: 'teacher',
    phone: '0901234569',
    profile: {
      bio: 'Giáo viên tiếng Anh giao tiếp và phát âm. Từng sống và làm việc tại Mỹ 5 năm.',
      skills: ['Pronunciation', 'Conversation', 'American English']
    }
  },

  // Student users
  {
    name: 'Nguyễn Văn A',
    email: '<EMAIL>',
    password: 'student123',
    role: 'student',
    phone: '0987654321',
    dateOfBirth: '1995-05-15',
    profile: {
      bio: 'Sinh viên năm 3 ngành Kinh tế. Mục tiêu đạt IELTS 7.0 để du học.',
      skills: ['Basic English', 'Reading']
    }
  },
  {
    name: 'Trần Thị B',
    email: '<EMAIL>',
    password: 'student123',
    role: 'student',
    phone: '0987654322',
    dateOfBirth: '1992-08-20',
    profile: {
      bio: 'Nhân viên văn phòng, muốn cải thiện tiếng Anh để thăng tiến trong công việc.',
      skills: ['Business English', 'Email Writing']
    }
  },
  {
    name: 'Lê Văn C',
    email: '<EMAIL>',
    password: 'student123',
    role: 'student',
    phone: '0987654323',
    dateOfBirth: '1998-12-10',
    profile: {
      bio: 'Học sinh lớp 12, chuẩn bị thi đại học và muốn học tiếng Anh cơ bản.',
      skills: ['Grammar', 'Vocabulary']
    }
  },
  {
    name: 'Phạm Thị D',
    email: '<EMAIL>',
    password: 'student123',
    role: 'student',
    phone: '0987654324',
    dateOfBirth: '1990-03-25',
    profile: {
      bio: 'Mẹ bỉm sữa muốn học tiếng Anh để dạy con và phát triển bản thân.',
      skills: ['Listening', 'Basic Conversation']
    }
  },
  {
    name: 'Hoàng Văn E',
    email: '<EMAIL>',
    password: 'student123',
    role: 'student',
    phone: '0987654325',
    dateOfBirth: '1985-11-30',
    profile: {
      bio: 'Kỹ sư IT muốn cải thiện tiếng Anh để đọc tài liệu kỹ thuật và giao tiếp quốc tế.',
      skills: ['Technical English', 'Reading']
    }
  }
];

export async function seedUsers() {
  try {
    // Clear existing users (optional - comment out if you want to keep existing data)
    // await User.deleteMany({});
    
    const users = [];
    
    for (const userData of sampleUsers) {
      // Check if user already exists
      const existingUser = await User.findOne({ email: userData.email });
      if (existingUser) {
        console.log(`User ${userData.email} already exists, skipping...`);
        users.push(existingUser);
        continue;
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 12);
      
      // Create user
      const user = new User({
        ...userData,
        password: hashedPassword,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      await user.save();
      users.push(user);
      console.log(`✅ Created user: ${userData.name} (${userData.role})`);
    }
    
    return users;
  } catch (error) {
    console.error('Error seeding users:', error);
    throw error;
  }
}
