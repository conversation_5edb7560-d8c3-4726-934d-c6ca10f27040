"use client";

import { useState, useRef, useEffect } from "react";
import { Mic, Square, Play, Pause, RotateCcw, Upload } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface AudioRecorderProps {
  questionId: string;
  timeLimit?: number; // in seconds
  onRecordingComplete?: (audioBlob: Blob, duration: number) => void;
  onUploadComplete?: (audioUrl: string) => void;
  maxAttempts?: number;
  allowReRecord?: boolean;
  prompt?: string;
}

export default function AudioRecorder({
  questionId,
  timeLimit = 120, // 2 minutes default
  onRecordingComplete,
  onUploadComplete,
  maxAttempts = 3,
  allowReRecord = true,
  prompt
}: AudioRecorderProps) {
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const chunksRef = useRef<Blob[]>([]);
  
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [attempts, setAttempts] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [permissionGranted, setPermissionGranted] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);

  useEffect(() => {
    // Request microphone permission on component mount
    requestMicrophonePermission();
    
    return () => {
      // Cleanup
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingTime(prev => {
          if (prev >= timeLimit) {
            stopRecording();
            return prev;
          }
          return prev + 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecording, timeLimit]);

  const requestMicrophonePermission = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        }
      });
      setStream(mediaStream);
      setPermissionGranted(true);
      setError(null);
    } catch (err) {
      setError("Không thể truy cập microphone. Vui lòng cho phép quyền truy cập.");
      setPermissionGranted(false);
    }
  };

  const startRecording = () => {
    if (!stream || !permissionGranted) {
      requestMicrophonePermission();
      return;
    }

    if (attempts >= maxAttempts) {
      setError(`Bạn đã sử dụng hết ${maxAttempts} lần ghi âm.`);
      return;
    }

    try {
      chunksRef.current = [];
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: 'audio/webm;codecs=opus' });
        setAudioBlob(blob);
        
        // Create URL for playback
        if (audioUrl) {
          URL.revokeObjectURL(audioUrl);
        }
        const url = URL.createObjectURL(blob);
        setAudioUrl(url);
        
        onRecordingComplete?.(blob, recordingTime);
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start(100); // Collect data every 100ms
      
      setIsRecording(true);
      setRecordingTime(0);
      setError(null);
    } catch (err) {
      setError("Lỗi khi bắt đầu ghi âm. Vui lòng thử lại.");
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setAttempts(prev => prev + 1);
    }
  };

  const playRecording = () => {
    if (!audioRef.current || !audioUrl) return;

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const reRecord = () => {
    if (!allowReRecord) return;
    
    setAudioBlob(null);
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
      setAudioUrl(null);
    }
    setRecordingTime(0);
    setIsPlaying(false);
    setError(null);
  };

  const uploadRecording = async () => {
    if (!audioBlob) return;

    setIsUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('audio', audioBlob, `recording-${questionId}-${Date.now()}.webm`);
      formData.append('questionId', questionId);
      formData.append('duration', recordingTime.toString());

      const response = await fetch('/api/tests/upload-audio', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (data.success) {
        onUploadComplete?.(data.audioUrl);
      } else {
        setError(data.message || 'Lỗi khi tải lên file ghi âm');
      }
    } catch (err) {
      setError('Lỗi kết nối. Vui lòng thử lại.');
    } finally {
      setIsUploading(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const progressPercentage = (recordingTime / timeLimit) * 100;

  if (!permissionGranted) {
    return (
      <Card className="w-full">
        <CardContent className="p-6 text-center">
          <Mic className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium mb-2">Cần quyền truy cập Microphone</h3>
          <p className="text-gray-600 mb-4">
            Để ghi âm câu trả lời, vui lòng cho phép truy cập microphone.
          </p>
          <Button onClick={requestMicrophonePermission}>
            Cho phép truy cập Microphone
          </Button>
          {error && (
            <p className="text-red-600 text-sm mt-2">{error}</p>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg">Ghi âm câu trả lời</CardTitle>
        {prompt && (
          <p className="text-sm text-gray-600">{prompt}</p>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Recording Status */}
        <div className="text-center">
          <div className="text-2xl font-mono font-bold text-blue-600 mb-2">
            {formatTime(recordingTime)} / {formatTime(timeLimit)}
          </div>
          <Progress value={progressPercentage} className="w-full" />
          <p className="text-sm text-gray-500 mt-1">
            Lần thử: {attempts}/{maxAttempts}
          </p>
        </div>

        {/* Recording Controls */}
        <div className="flex justify-center gap-2">
          {!isRecording && !audioBlob && (
            <Button
              onClick={startRecording}
              disabled={attempts >= maxAttempts}
              className="bg-red-600 hover:bg-red-700"
            >
              <Mic className="h-4 w-4 mr-2" />
              Bắt đầu ghi âm
            </Button>
          )}

          {isRecording && (
            <Button
              onClick={stopRecording}
              variant="outline"
              className="border-red-600 text-red-600 hover:bg-red-50"
            >
              <Square className="h-4 w-4 mr-2" />
              Dừng ghi âm
            </Button>
          )}

          {audioBlob && !isRecording && (
            <>
              <Button
                onClick={playRecording}
                variant="outline"
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4 mr-2" />
                ) : (
                  <Play className="h-4 w-4 mr-2" />
                )}
                {isPlaying ? 'Tạm dừng' : 'Nghe lại'}
              </Button>

              {allowReRecord && attempts < maxAttempts && (
                <Button
                  onClick={reRecord}
                  variant="outline"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Ghi lại
                </Button>
              )}

              <Button
                onClick={uploadRecording}
                disabled={isUploading}
                className="bg-green-600 hover:bg-green-700"
              >
                {isUploading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                ) : (
                  <Upload className="h-4 w-4 mr-2" />
                )}
                {isUploading ? 'Đang tải lên...' : 'Nộp bài'}
              </Button>
            </>
          )}
        </div>

        {/* Audio Element for Playback */}
        {audioUrl && (
          <audio
            ref={audioRef}
            src={audioUrl}
            onEnded={() => setIsPlaying(false)}
            className="hidden"
          />
        )}

        {/* Error Message */}
        {error && (
          <div className="text-center text-red-600 text-sm">
            {error}
          </div>
        )}

        {/* Instructions */}
        <div className="text-xs text-gray-500 text-center">
          <p>• Nói rõ ràng và gần microphone</p>
          <p>• Tối đa {maxAttempts} lần ghi âm</p>
          <p>• Thời gian tối đa: {formatTime(timeLimit)}</p>
        </div>
      </CardContent>
    </Card>
  );
}
