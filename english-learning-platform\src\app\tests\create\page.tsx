"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Plus, Trash2, Save, Eye } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import Header from "@/components/layout/Header";
import { TestType, CourseLevel, QuestionType } from "@/types";
import { toast } from "sonner";

interface Question {
  id: string;
  type: QuestionType;
  question: string;
  options?: string[];
  correctAnswer: string | string[];
  points: number;
  explanation?: string;
  order: number;
}

export default function CreateTestPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("basic");
  const [isLoading, setIsLoading] = useState(false);
  
  const [testData, setTestData] = useState({
    title: "",
    description: "",
    type: TestType.VOCABULARY,
    level: CourseLevel.BEGINNER,
    duration: 30,
    passingScore: 70,
    instructions: "",
    tags: [] as string[],
    difficulty: "medium" as "easy" | "medium" | "hard"
  });

  const [questions, setQuestions] = useState<Question[]>([]);
  const [currentQuestion, setCurrentQuestion] = useState<Partial<Question>>({
    type: QuestionType.MULTIPLE_CHOICE,
    question: "",
    options: ["", "", "", ""],
    correctAnswer: "",
    points: 5,
    explanation: ""
  });

  const breadcrumbItems = [
    { label: "Luyện thi", href: "/tests" },
    { label: "Tạo bài kiểm tra", current: true }
  ];

  const handleTestDataChange = (field: string, value: any) => {
    setTestData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleQuestionChange = (field: string, value: any) => {
    setCurrentQuestion(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...(currentQuestion.options || [])];
    newOptions[index] = value;
    setCurrentQuestion(prev => ({
      ...prev,
      options: newOptions
    }));
  };

  const addQuestion = () => {
    if (!currentQuestion.question || !currentQuestion.correctAnswer) {
      toast.error("Vui lòng nhập đầy đủ thông tin câu hỏi");
      return;
    }

    const newQuestion: Question = {
      id: `q-${Date.now()}`,
      type: currentQuestion.type!,
      question: currentQuestion.question,
      options: currentQuestion.options,
      correctAnswer: currentQuestion.correctAnswer,
      points: currentQuestion.points || 5,
      explanation: currentQuestion.explanation,
      order: questions.length + 1
    };

    setQuestions(prev => [...prev, newQuestion]);
    
    // Reset current question
    setCurrentQuestion({
      type: QuestionType.MULTIPLE_CHOICE,
      question: "",
      options: ["", "", "", ""],
      correctAnswer: "",
      points: 5,
      explanation: ""
    });

    toast.success("Đã thêm câu hỏi");
  };

  const removeQuestion = (id: string) => {
    setQuestions(prev => prev.filter(q => q.id !== id));
    toast.success("Đã xóa câu hỏi");
  };

  const handleSave = async (isDraft = true) => {
    if (!testData.title || !testData.description || questions.length === 0) {
      toast.error("Vui lòng nhập đầy đủ thông tin bài kiểm tra");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/tests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...testData,
          questions: questions.map(q => ({
            type: q.type,
            question: q.question,
            options: q.options,
            correctAnswer: q.correctAnswer,
            points: q.points,
            explanation: q.explanation,
            order: q.order
          })),
          isPublished: !isDraft
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success(isDraft ? "Lưu nháp thành công!" : "Xuất bản bài kiểm tra thành công!");
        router.push('/dashboard/teacher');
      } else {
        toast.error(data.message || "Lỗi khi tạo bài kiểm tra");
      }
    } catch (error) {
      console.error('Create test error:', error);
      toast.error("Lỗi kết nối. Vui lòng thử lại.");
    } finally {
      setIsLoading(false);
    }
  };

  const renderQuestionForm = () => {
    switch (currentQuestion.type) {
      case QuestionType.MULTIPLE_CHOICE:
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Các lựa chọn
              </label>
              {currentQuestion.options?.map((option, index) => (
                <div key={index} className="flex items-center gap-2 mb-2">
                  <span className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium">
                    {String.fromCharCode(65 + index)}
                  </span>
                  <Input
                    value={option}
                    onChange={(e) => handleOptionChange(index, e.target.value)}
                    placeholder={`Lựa chọn ${String.fromCharCode(65 + index)}`}
                  />
                </div>
              ))}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Đáp án đúng
              </label>
              <select
                value={currentQuestion.correctAnswer}
                onChange={(e) => handleQuestionChange('correctAnswer', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Chọn đáp án đúng</option>
                {currentQuestion.options?.map((option, index) => (
                  <option key={index} value={option}>
                    {String.fromCharCode(65 + index)}: {option}
                  </option>
                ))}
              </select>
            </div>
          </div>
        );

      case QuestionType.FILL_IN_BLANK:
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Đáp án đúng
            </label>
            <Input
              value={currentQuestion.correctAnswer as string}
              onChange={(e) => handleQuestionChange('correctAnswer', e.target.value)}
              placeholder="Nhập đáp án đúng"
            />
          </div>
        );

      case QuestionType.TRUE_FALSE:
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Đáp án đúng
            </label>
            <select
              value={currentQuestion.correctAnswer}
              onChange={(e) => handleQuestionChange('correctAnswer', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Chọn đáp án</option>
              <option value="Đúng">Đúng</option>
              <option value="Sai">Sai</option>
            </select>
          </div>
        );

      case QuestionType.ESSAY:
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Gợi ý đáp án (không bắt buộc)
            </label>
            <textarea
              value={currentQuestion.correctAnswer as string}
              onChange={(e) => handleQuestionChange('correctAnswer', e.target.value)}
              placeholder="Nhập gợi ý đáp án hoặc tiêu chí chấm điểm..."
              rows={4}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Breadcrumb items={breadcrumbItems} className="mb-6" />
        
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Tạo bài kiểm tra mới</h1>
            <p className="text-gray-600">Tạo bài kiểm tra cho học viên của bạn</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" onClick={() => handleSave(true)} disabled={isLoading}>
              <Save className="h-4 w-4 mr-2" />
              Lưu nháp
            </Button>
            <Button onClick={() => handleSave(false)} disabled={isLoading}>
              <Eye className="h-4 w-4 mr-2" />
              Xuất bản
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">Thông tin cơ bản</TabsTrigger>
            <TabsTrigger value="questions">Câu hỏi ({questions.length})</TabsTrigger>
            <TabsTrigger value="preview">Xem trước</TabsTrigger>
          </TabsList>

          {/* Basic Info Tab */}
          <TabsContent value="basic" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Thông tin bài kiểm tra</CardTitle>
                <CardDescription>
                  Nhập thông tin cơ bản về bài kiểm tra
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tiêu đề *
                    </label>
                    <Input
                      value={testData.title}
                      onChange={(e) => handleTestDataChange('title', e.target.value)}
                      placeholder="Nhập tiêu đề bài kiểm tra"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Loại bài kiểm tra *
                    </label>
                    <select
                      value={testData.type}
                      onChange={(e) => handleTestDataChange('type', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value={TestType.VOCABULARY}>Từ vựng</option>
                      <option value={TestType.GRAMMAR}>Ngữ pháp</option>
                      <option value={TestType.LISTENING}>Nghe</option>
                      <option value={TestType.SPEAKING}>Nói</option>
                      <option value={TestType.READING}>Đọc</option>
                      <option value={TestType.WRITING}>Viết</option>
                      <option value={TestType.COMPREHENSIVE}>Tổng hợp</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Trình độ *
                    </label>
                    <select
                      value={testData.level}
                      onChange={(e) => handleTestDataChange('level', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value={CourseLevel.BEGINNER}>Cơ bản</option>
                      <option value={CourseLevel.INTERMEDIATE}>Trung cấp</option>
                      <option value={CourseLevel.ADVANCED}>Nâng cao</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Độ khó
                    </label>
                    <select
                      value={testData.difficulty}
                      onChange={(e) => handleTestDataChange('difficulty', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="easy">Dễ</option>
                      <option value="medium">Trung bình</option>
                      <option value="hard">Khó</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Thời lượng (phút) *
                    </label>
                    <Input
                      type="number"
                      value={testData.duration}
                      onChange={(e) => handleTestDataChange('duration', parseInt(e.target.value))}
                      min="5"
                      max="300"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Điểm đạt (%) *
                    </label>
                    <Input
                      type="number"
                      value={testData.passingScore}
                      onChange={(e) => handleTestDataChange('passingScore', parseInt(e.target.value))}
                      min="0"
                      max="100"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mô tả *
                  </label>
                  <textarea
                    value={testData.description}
                    onChange={(e) => handleTestDataChange('description', e.target.value)}
                    placeholder="Mô tả về bài kiểm tra này..."
                    rows={3}
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hướng dẫn làm bài *
                  </label>
                  <textarea
                    value={testData.instructions}
                    onChange={(e) => handleTestDataChange('instructions', e.target.value)}
                    placeholder="Hướng dẫn chi tiết cho học viên..."
                    rows={4}
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Questions Tab */}
          <TabsContent value="questions" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Add Question Form */}
              <Card>
                <CardHeader>
                  <CardTitle>Thêm câu hỏi mới</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Loại câu hỏi
                    </label>
                    <select
                      value={currentQuestion.type}
                      onChange={(e) => handleQuestionChange('type', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value={QuestionType.MULTIPLE_CHOICE}>Trắc nghiệm</option>
                      <option value={QuestionType.FILL_IN_BLANK}>Điền từ</option>
                      <option value={QuestionType.TRUE_FALSE}>Đúng/Sai</option>
                      <option value={QuestionType.ESSAY}>Tự luận</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Câu hỏi *
                    </label>
                    <textarea
                      value={currentQuestion.question}
                      onChange={(e) => handleQuestionChange('question', e.target.value)}
                      placeholder="Nhập nội dung câu hỏi..."
                      rows={3}
                      className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  {renderQuestionForm()}

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Điểm số
                    </label>
                    <Input
                      type="number"
                      value={currentQuestion.points}
                      onChange={(e) => handleQuestionChange('points', parseInt(e.target.value))}
                      min="1"
                      max="100"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Giải thích (không bắt buộc)
                    </label>
                    <textarea
                      value={currentQuestion.explanation}
                      onChange={(e) => handleQuestionChange('explanation', e.target.value)}
                      placeholder="Giải thích đáp án..."
                      rows={2}
                      className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <Button onClick={addQuestion} className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Thêm câu hỏi
                  </Button>
                </CardContent>
              </Card>

              {/* Questions List */}
              <Card>
                <CardHeader>
                  <CardTitle>Danh sách câu hỏi ({questions.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  {questions.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      Chưa có câu hỏi nào. Hãy thêm câu hỏi đầu tiên!
                    </div>
                  ) : (
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {questions.map((question, index) => (
                        <div key={question.id} className="p-3 border rounded-lg">
                          <div className="flex justify-between items-start mb-2">
                            <span className="font-medium">Câu {index + 1}</span>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">{question.points} điểm</Badge>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeQuestion(question.id)}
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>
                          </div>
                          <p className="text-sm text-gray-700 line-clamp-2">
                            {question.question}
                          </p>
                          <div className="mt-2">
                            <Badge variant="secondary" className="text-xs">
                              {question.type === QuestionType.MULTIPLE_CHOICE ? 'Trắc nghiệm' :
                               question.type === QuestionType.FILL_IN_BLANK ? 'Điền từ' :
                               question.type === QuestionType.TRUE_FALSE ? 'Đúng/Sai' :
                               question.type === QuestionType.ESSAY ? 'Tự luận' : question.type}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Preview Tab */}
          <TabsContent value="preview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Xem trước bài kiểm tra</CardTitle>
                <CardDescription>
                  Kiểm tra lại thông tin trước khi lưu
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-xl font-bold">{testData.title || "Chưa có tiêu đề"}</h3>
                  <p className="text-gray-600 mt-2">{testData.description || "Chưa có mô tả"}</p>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="font-bold text-blue-600">{testData.duration}</div>
                    <div className="text-sm text-gray-600">phút</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="font-bold text-green-600">{questions.length}</div>
                    <div className="text-sm text-gray-600">câu hỏi</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="font-bold text-purple-600">{testData.passingScore}%</div>
                    <div className="text-sm text-gray-600">điểm đạt</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="font-bold text-orange-600">
                      {questions.reduce((sum, q) => sum + q.points, 0)}
                    </div>
                    <div className="text-sm text-gray-600">tổng điểm</div>
                  </div>
                </div>

                {testData.instructions && (
                  <div>
                    <h4 className="font-medium mb-2">Hướng dẫn làm bài:</h4>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <p className="whitespace-pre-line">{testData.instructions}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
