import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Course from '@/models/Course';
import Enrollment from '@/models/Enrollment';
import User from '@/models/User';
import { getAuthUser } from '@/lib/auth';
import { PaymentStatus } from '@/types';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Only students can enroll
    if (user.role !== 'student') {
      return NextResponse.json({
        success: false,
        message: 'Chỉ học viên mới có thể đăng ký khóa học'
      }, { status: 403 });
    }

    // Find course
    const course = await Course.findById(id);
    if (!course) {
      return NextResponse.json({
        success: false,
        message: '<PERSON>h<PERSON><PERSON> tìm thấy khóa học'
      }, { status: 404 });
    }

    if (!course.isPublished) {
      return NextResponse.json({
        success: false,
        message: 'Khóa học chưa được xuất bản'
      }, { status: 403 });
    }

    // Check if already enrolled
    const existingEnrollment = await Enrollment.findOne({
      studentId: user.userId,
      courseId: id
    });

    if (existingEnrollment) {
      return NextResponse.json({
        success: false,
        message: 'Bạn đã đăng ký khóa học này rồi'
      }, { status: 409 });
    }

    const body = await request.json();
    const { paymentMethod, paymentId } = body;

    // For free courses, enroll immediately
    if (course.price === 0) {
      const enrollment = new Enrollment({
        studentId: user.userId,
        courseId: id,
        paymentStatus: PaymentStatus.COMPLETED,
        paymentAmount: 0,
        lessonProgress: course.curriculum.map((lesson: any) => ({
          lessonId: lesson._id.toString(),
          timeSpent: 0,
          videoProgress: 0,
          isCompleted: false,
          bookmarks: []
        }))
      });

      await enrollment.save();

      // Update course total students
      await Course.findByIdAndUpdate(id, {
        $inc: { totalStudents: 1 }
      });

      // Populate enrollment data
      await enrollment.populate([
        { path: 'studentId', select: 'name email' },
        { path: 'courseId', select: 'title description instructor' }
      ]);

      return NextResponse.json({
        success: true,
        message: 'Đăng ký khóa học miễn phí thành công!',
        data: enrollment
      }, { status: 201 });
    }

    // For paid courses, create pending enrollment
    if (!paymentMethod) {
      return NextResponse.json({
        success: false,
        message: 'Phương thức thanh toán là bắt buộc cho khóa học có phí'
      }, { status: 400 });
    }

    const enrollment = new Enrollment({
      studentId: user.userId,
      courseId: id,
      paymentStatus: PaymentStatus.PENDING,
      paymentAmount: course.price,
      paymentId: paymentId,
      lessonProgress: course.curriculum.map((lesson: any) => ({
        lessonId: lesson._id.toString(),
        timeSpent: 0,
        videoProgress: 0,
        isCompleted: false,
        bookmarks: []
      }))
    });

    await enrollment.save();

    // TODO: Integrate with payment gateway (Stripe/VNPay)
    // For now, we'll simulate payment processing
    
    // Populate enrollment data
    await enrollment.populate([
      { path: 'studentId', select: 'name email' },
      { path: 'courseId', select: 'title description instructor price' }
    ]);

    return NextResponse.json({
      success: true,
      message: 'Đăng ký khóa học thành công! Vui lòng hoàn tất thanh toán.',
      data: enrollment,
      paymentRequired: true,
      paymentAmount: course.price
    }, { status: 201 });

  } catch (error) {
    console.error('Enroll course error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi đăng ký khóa học',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Get enrollment info
    const enrollment = await Enrollment.findOne({
      studentId: user.userId,
      courseId: id
    }).populate([
      { path: 'studentId', select: 'name email' },
      { path: 'courseId', select: 'title description instructor curriculum' }
    ]);

    if (!enrollment) {
      return NextResponse.json({
        success: false,
        message: 'Bạn chưa đăng ký khóa học này'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: enrollment
    });

  } catch (error) {
    console.error('Get enrollment error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi tải thông tin đăng ký',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find enrollment
    const enrollment = await Enrollment.findOne({
      studentId: user.userId,
      courseId: id
    });

    if (!enrollment) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy thông tin đăng ký'
      }, { status: 404 });
    }

    // Check if can unenroll (e.g., within refund period)
    const enrolledDaysAgo = Math.floor(
      (Date.now() - enrollment.enrolledAt.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (enrolledDaysAgo > 7) {
      return NextResponse.json({
        success: false,
        message: 'Không thể hủy đăng ký sau 7 ngày'
      }, { status: 403 });
    }

    if (enrollment.progress > 20) {
      return NextResponse.json({
        success: false,
        message: 'Không thể hủy đăng ký khi đã học quá 20% khóa học'
      }, { status: 403 });
    }

    // Delete enrollment
    await Enrollment.findByIdAndDelete(enrollment._id);

    // Update course total students
    await Course.findByIdAndUpdate(id, {
      $inc: { totalStudents: -1 }
    });

    // TODO: Process refund if applicable

    return NextResponse.json({
      success: true,
      message: 'Hủy đăng ký khóa học thành công'
    });

  } catch (error) {
    console.error('Unenroll course error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi hủy đăng ký khóa học',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
