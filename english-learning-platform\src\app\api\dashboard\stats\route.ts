import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import Test from '@/models/Test';
import Course from '@/models/Course';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return authResult.error;
    }

    const { user } = authResult;
    await connectDB();

    // Get user data
    const userDoc = await User.findById(user.userId);

    // For now, simulate course data - in a real app you'd have enrollment tracking
    const totalCourses = 3; // Simulated
    const completedCourses = 1; // Simulated

    // Get user's test results
    const testResults = await Test.aggregate([
      {
        $lookup: {
          from: 'testresults',
          localField: '_id',
          foreignField: 'testId',
          as: 'results'
        }
      },
      {
        $unwind: {
          path: '$results',
          preserveNullAndEmptyArrays: false
        }
      },
      {
        $match: {
          'results.userId': userDoc?._id
        }
      },
      {
        $group: {
          _id: null,
          totalTests: { $sum: 1 },
          averageScore: { $avg: '$results.score' },
          totalTimeSpent: { $sum: '$results.timeSpent' }
        }
      }
    ]);

    const testStats = testResults[0] || {
      totalTests: 0,
      averageScore: 0,
      totalTimeSpent: 0
    };

    // Calculate total hours (from test time + estimated course time)
    const totalHours = Math.round((testStats.totalTimeSpent / 3600) + (totalCourses * 10)); // Estimate 10 hours per course

    const stats = {
      totalCourses,
      completedCourses,
      totalHours,
      averageScore: Math.round(testStats.averageScore || 0),
      totalTests: testStats.totalTests,
      completedTests: testStats.totalTests // For now, all taken tests are considered completed
    };

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Get dashboard stats error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi hệ thống, vui lòng thử lại sau',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
