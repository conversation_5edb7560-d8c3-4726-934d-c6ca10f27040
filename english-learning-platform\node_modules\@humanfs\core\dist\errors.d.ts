/**
 * @fileoverview Common error classes
 * <AUTHOR>
 */
/**
 * Error thrown when a file or directory is not found.
 */
export class NotFoundError extends Error {
    /**
     * Creates a new instance.
     * @param {string} message The error message.
     */
    constructor(message: string);
    /**
     * Error code.
     * @type {string}
     */
    code: string;
}
/**
 * Error thrown when an operation is not permitted.
 */
export class PermissionError extends Error {
    /**
     * Creates a new instance.
     * @param {string} message The error message.
     */
    constructor(message: string);
    /**
     * Error code.
     * @type {string}
     */
    code: string;
}
/**
 * Error thrown when an operation is not allowed on a directory.
 */
export class DirectoryError extends Error {
    /**
     * Creates a new instance.
     * @param {string} message The error message.
     */
    constructor(message: string);
    /**
     * Error code.
     * @type {string}
     */
    code: string;
}
/**
 * Error thrown when a directory is not empty.
 */
export class NotEmptyError extends Error {
    /**
     * Creates a new instance.
     * @param {string} message The error message.
     */
    constructor(message: string);
    /**
     * Error code.
     * @type {string}
     */
    code: string;
}
