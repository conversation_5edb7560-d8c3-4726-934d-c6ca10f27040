import Link from "next/link";
import { ChevronRight, Home } from "lucide-react";
import { cn } from "@/lib/utils";

export interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
}

export function Breadcrumb({ items, className, showHome = true }: BreadcrumbProps) {
  const allItems = showHome 
    ? [{ label: "Trang chủ", href: "/" }, ...items]
    : items;

  return (
    <nav className={cn("flex", className)} aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        {allItems.map((item, index) => (
          <li key={index} className="inline-flex items-center">
            {index > 0 && (
              <ChevronRight className="w-4 h-4 text-gray-400 mx-1" />
            )}
            
            {item.current || !item.href ? (
              <span className="text-sm font-medium text-gray-500 flex items-center">
                {index === 0 && showHome && (
                  <Home className="w-4 h-4 mr-1" />
                )}
                {item.label}
              </span>
            ) : (
              <Link
                href={item.href}
                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
              >
                {index === 0 && showHome && (
                  <Home className="w-4 h-4 mr-1" />
                )}
                {item.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}

// Hook để tự động tạo breadcrumb từ URL
export function useBreadcrumb() {
  if (typeof window === 'undefined') return [];
  
  const pathname = window.location.pathname;
  const segments = pathname.split('/').filter(Boolean);
  
  const breadcrumbMap: Record<string, string> = {
    'courses': 'Khóa học',
    'tests': 'Luyện thi',
    'dashboard': 'Dashboard',
    'profile': 'Hồ sơ',
    'about': 'Giới thiệu',
    'contact': 'Liên hệ',
    'auth': 'Xác thực',
    'login': 'Đăng nhập',
    'register': 'Đăng ký',
    'teacher': 'Giáo viên',
    'admin': 'Quản trị',
    'student': 'Học viên'
  };

  const items: BreadcrumbItem[] = [];
  let currentPath = '';

  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    const isLast = index === segments.length - 1;
    
    items.push({
      label: breadcrumbMap[segment] || segment,
      href: isLast ? undefined : currentPath,
      current: isLast
    });
  });

  return items;
}
