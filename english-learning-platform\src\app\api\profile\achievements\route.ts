import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return authResult.error;
    }

    const { user } = authResult;
    await connectDB();

    // For now, return mock achievements - in a real app you'd track these
    const achievements = [
      {
        title: "Hoàn thành khóa học Tiếng Anh <PERSON>ơ Bản",
        date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
        type: "course"
      },
      {
        title: "Đạt 85% bài kiểm tra từ vựng",
        date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
        type: "test"
      },
      {
        title: "<PERSON>ọ<PERSON> viên xuất sắc tháng 1",
        date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days ago
        type: "award"
      }
    ];

    return NextResponse.json({
      success: true,
      data: achievements
    });

  } catch (error) {
    console.error('Get profile achievements error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi hệ thống, vui lòng thử lại sau',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
