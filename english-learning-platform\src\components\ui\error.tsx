import { Alert<PERSON><PERSON>gle, <PERSON>fresh<PERSON><PERSON>, Home, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface ErrorMessageProps {
  title?: string;
  message?: string;
  className?: string;
}

export function ErrorMessage({ 
  title = "Đã xảy ra lỗi", 
  message = "Vui lòng thử lại sau.", 
  className 
}: ErrorMessageProps) {
  return (
    <div className={cn("flex items-center space-x-2 text-red-600", className)}>
      <AlertTriangle className="h-4 w-4" />
      <div>
        <p className="font-medium text-sm">{title}</p>
        {message && <p className="text-xs text-red-500">{message}</p>}
      </div>
    </div>
  );
}

interface ErrorCardProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  className?: string;
}

export function ErrorCard({ 
  title = "Không thể tải dữ liệu", 
  message = "Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại.", 
  onRetry,
  className 
}: ErrorCardProps) {
  return (
    <Card className={cn("border-red-200", className)}>
      <CardContent className="pt-6">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
          <p className="text-gray-600 mb-4">{message}</p>
          {onRetry && (
            <Button onClick={onRetry} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Thử lại
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface ErrorPageProps {
  title?: string;
  message?: string;
  showHomeButton?: boolean;
  showBackButton?: boolean;
  onRetry?: () => void;
}

export function ErrorPage({ 
  title = "Oops! Đã xảy ra lỗi", 
  message = "Trang bạn đang tìm kiếm không tồn tại hoặc đã xảy ra lỗi hệ thống.",
  showHomeButton = true,
  showBackButton = true,
  onRetry
}: ErrorPageProps) {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <AlertTriangle className="h-24 w-24 text-red-500 mx-auto mb-6" />
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{title}</h1>
          <p className="text-gray-600 mb-8">{message}</p>
        </div>
        
        <div className="space-y-3">
          {onRetry && (
            <Button onClick={onRetry} className="w-full">
              <RefreshCw className="h-4 w-4 mr-2" />
              Thử lại
            </Button>
          )}
          
          {showHomeButton && (
            <Button variant="outline" className="w-full" onClick={() => window.location.href = '/'}>
              <Home className="h-4 w-4 mr-2" />
              Về trang chủ
            </Button>
          )}
          
          {showBackButton && (
            <Button variant="ghost" className="w-full" onClick={() => window.history.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

interface FormErrorProps {
  errors: Record<string, string>;
  className?: string;
}

export function FormError({ errors, className }: FormErrorProps) {
  const errorMessages = Object.values(errors).filter(Boolean);
  
  if (errorMessages.length === 0) return null;

  return (
    <div className={cn("bg-red-50 border border-red-200 rounded-md p-3", className)}>
      <div className="flex">
        <AlertTriangle className="h-4 w-4 text-red-400 mt-0.5 mr-2 flex-shrink-0" />
        <div className="text-sm">
          {errorMessages.length === 1 ? (
            <p className="text-red-800">{errorMessages[0]}</p>
          ) : (
            <div>
              <p className="text-red-800 font-medium mb-1">Vui lòng kiểm tra lại:</p>
              <ul className="list-disc list-inside text-red-700 space-y-1">
                {errorMessages.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

interface NetworkErrorProps {
  onRetry?: () => void;
  className?: string;
}

export function NetworkError({ onRetry, className }: NetworkErrorProps) {
  return (
    <div className={cn("text-center py-8", className)}>
      <div className="mb-4">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <AlertTriangle className="h-8 w-8 text-red-600" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Lỗi kết nối mạng
        </h3>
        <p className="text-gray-600 mb-4">
          Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối internet và thử lại.
        </p>
      </div>
      
      {onRetry && (
        <Button onClick={onRetry} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Thử lại
        </Button>
      )}
    </div>
  );
}

interface NotFoundProps {
  title?: string;
  message?: string;
  showHomeButton?: boolean;
}

export function NotFound({ 
  title = "Không tìm thấy trang", 
  message = "Trang bạn đang tìm kiếm không tồn tại hoặc đã được di chuyển.",
  showHomeButton = true
}: NotFoundProps) {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <div className="text-6xl font-bold text-gray-400 mb-4">404</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">{title}</h1>
          <p className="text-gray-600 mb-8">{message}</p>
        </div>
        
        {showHomeButton && (
          <Button onClick={() => window.location.href = '/'}>
            <Home className="h-4 w-4 mr-2" />
            Về trang chủ
          </Button>
        )}
      </div>
    </div>
  );
}
