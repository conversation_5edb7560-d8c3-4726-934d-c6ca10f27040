"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Clock, Al<PERSON>Triangle, Check<PERSON><PERSON>cle, Play, Pause, SkipForward } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { LoadingPage } from "@/components/ui/loading";
import { ErrorPage } from "@/components/ui/error";
import Header from "@/components/layout/Header";
import { toast } from "sonner";

// Interface for test data from API
interface TestData {
  _id: string;
  title: string;
  description: string;
  type: string;
  level: string;
  duration: number;
  totalQuestions: number;
  passingScore: number;
  instructions: string;
  questions: Array<{
    _id: string;
    type: string;
    question: string;
    options?: string[];
    correctAnswer: string | string[];
    points: number;
    order: number;
    explanation?: string;
  }>;
  isPublished: boolean;
  createdBy?: {
    name: string;
  };
}

interface TestDetailPageProps {
  params: {
    id: string;
  };
}

export default function TestDetailPage({ params }: TestDetailPageProps) {
  const router = useRouter();
  const [test, setTest] = useState<TestData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasStarted, setHasStarted] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [timeLeft, setTimeLeft] = useState(0); // in seconds
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmSubmit, setShowConfirmSubmit] = useState(false);

  // Load test data from API
  useEffect(() => {
    loadTest();
  }, []);

  const loadTest = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const { id } = await params;
      const response = await fetch(`/api/tests/${id}`);
      const data = await response.json();

      if (data.success) {
        setTest(data.data);
        setTimeLeft(data.data.duration * 60); // Convert minutes to seconds
      } else {
        setError(data.message || 'Không thể tải bài kiểm tra');
      }
    } catch (error) {
      console.error('Load test error:', error);
      setError('Lỗi kết nối. Vui lòng thử lại.');
    } finally {
      setIsLoading(false);
    }
  };

  // Timer countdown
  useEffect(() => {
    if (!hasStarted || timeLeft <= 0) return;

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          handleAutoSubmit();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [hasStarted, timeLeft]);

  const breadcrumbItems = [
    { label: "Luyện thi", href: "/tests" },
    { label: test?.title || "Bài kiểm tra", current: true }
  ];

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getTimeColor = () => {
    const percentage = (timeLeft / (test.duration * 60)) * 100;
    if (percentage > 50) return "text-green-600";
    if (percentage > 25) return "text-yellow-600";
    return "text-red-600";
  };

  const handleStartTest = () => {
    setHasStarted(true);
    toast.success("Bài kiểm tra đã bắt đầu!");
  };

  const handleAnswerChange = (questionId: string, answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleNextQuestion = () => {
    if (currentQuestion < test.questions.length - 1) {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const handleQuestionJump = (index: number) => {
    setCurrentQuestion(index);
  };

  const handleAutoSubmit = () => {
    toast.warning("Hết thời gian! Bài kiểm tra sẽ được nộp tự động.");
    submitTest();
  };

  const handleSubmitClick = () => {
    const unansweredCount = test.questions.length - Object.keys(answers).length;
    if (unansweredCount > 0) {
      setShowConfirmSubmit(true);
    } else {
      submitTest();
    }
  };

  const submitTest = async () => {
    if (!test) return;

    setIsSubmitting(true);

    try {
      const { id } = await params;
      const response = await fetch(`/api/tests/${id}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          answers,
          timeSpent: (test.duration * 60) - timeLeft,
          startedAt: new Date(Date.now() - ((test.duration * 60) - timeLeft) * 1000)
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success("Nộp bài thành công!");
        router.push(`/tests/${id}/result`);
      } else {
        toast.error(data.message || "Lỗi khi nộp bài");
      }
    } catch (error) {
      console.error("Submit error:", error);
      toast.error("Lỗi kết nối. Vui lòng thử lại.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <LoadingPage message="Đang tải bài kiểm tra..." />;
  }

  if (error) {
    return <ErrorPage title="Không thể tải bài kiểm tra" message={error} />;
  }

  if (!test) {
    return <ErrorPage title="Không tìm thấy bài kiểm tra" message="Bài kiểm tra không tồn tại hoặc đã bị xóa" />;
  }

  if (!hasStarted) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Breadcrumb items={breadcrumbItems} className="mb-6" />
          
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">{test.title}</CardTitle>
              <CardDescription>{test.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Test Info */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-blue-600">{test.duration}</div>
                  <div className="text-sm text-gray-600">phút</div>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-green-600">{test.totalQuestions}</div>
                  <div className="text-sm text-gray-600">câu hỏi</div>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                  <AlertTriangle className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-purple-600">{test.passingScore}%</div>
                  <div className="text-sm text-gray-600">điểm đạt</div>
                </div>
              </div>

              {/* Instructions */}
              <div className="prose max-w-none">
                <div dangerouslySetInnerHTML={{ __html: test.instructions }} />
              </div>

              {/* Start Button */}
              <div className="text-center">
                <Button size="lg" onClick={handleStartTest}>
                  <Play className="h-5 w-5 mr-2" />
                  Bắt đầu làm bài
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const currentQ = test.questions[currentQuestion];
  const progress = ((currentQuestion + 1) / test.questions.length) * 100;
  const answeredCount = Object.keys(answers).length;

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Test Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{test.title}</h1>
              <p className="text-gray-600">
                Câu {currentQuestion + 1} / {test.questions.length}
              </p>
            </div>
            
            <div className="flex items-center gap-6">
              {/* Progress */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Tiến độ:</span>
                <div className="w-32">
                  <Progress value={progress} />
                </div>
                <span className="text-sm font-medium">{Math.round(progress)}%</span>
              </div>
              
              {/* Timer */}
              <div className={`flex items-center gap-2 ${getTimeColor()}`}>
                <Clock className="h-5 w-5" />
                <span className="text-lg font-mono font-bold">
                  {formatTime(timeLeft)}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Question Navigation */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Danh sách câu hỏi</CardTitle>
                <CardDescription>
                  Đã trả lời: {answeredCount}/{test.questions.length}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-5 lg:grid-cols-4 gap-2">
                  {test.questions.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => handleQuestionJump(index)}
                      className={`
                        w-10 h-10 rounded-lg text-sm font-medium transition-colors
                        ${index === currentQuestion 
                          ? 'bg-blue-600 text-white' 
                          : answers[test.questions[index].id]
                            ? 'bg-green-100 text-green-700 hover:bg-green-200'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }
                      `}
                    >
                      {index + 1}
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Question Content */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Câu {currentQuestion + 1}</CardTitle>
                  <Badge variant="outline">{currentQ.points} điểm</Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Question */}
                <div className="prose max-w-none">
                  <h3 className="text-lg font-medium text-gray-900">
                    {currentQ.question}
                  </h3>
                </div>

                {/* Answer Options */}
                {currentQ.type === 'multiple_choice' && (
                  <div className="space-y-3">
                    {currentQ.options?.map((option, index) => (
                      <label
                        key={index}
                        className={`
                          flex items-center p-4 border rounded-lg cursor-pointer transition-colors
                          ${answers[currentQ.id] === option
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                          }
                        `}
                      >
                        <input
                          type="radio"
                          name={`question-${currentQ.id}`}
                          value={option}
                          checked={answers[currentQ.id] === option}
                          onChange={(e) => handleAnswerChange(currentQ.id, e.target.value)}
                          className="mr-3"
                        />
                        <span className="text-gray-900">{option}</span>
                      </label>
                    ))}
                  </div>
                )}

                {/* Navigation */}
                <div className="flex items-center justify-between pt-6 border-t">
                  <Button
                    variant="outline"
                    onClick={handlePreviousQuestion}
                    disabled={currentQuestion === 0}
                  >
                    Câu trước
                  </Button>
                  
                  <div className="flex gap-2">
                    {currentQuestion === test.questions.length - 1 ? (
                      <Button onClick={handleSubmitClick} disabled={isSubmitting}>
                        {isSubmitting ? "Đang nộp bài..." : "Nộp bài"}
                      </Button>
                    ) : (
                      <Button onClick={handleNextQuestion}>
                        Câu tiếp theo
                        <SkipForward className="h-4 w-4 ml-2" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Confirm Submit Dialog */}
        {showConfirmSubmit && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <Card className="w-full max-w-md mx-4">
              <CardHeader>
                <CardTitle>Xác nhận nộp bài</CardTitle>
                <CardDescription>
                  Bạn còn {test.questions.length - answeredCount} câu chưa trả lời. 
                  Bạn có chắc chắn muốn nộp bài?
                </CardDescription>
              </CardHeader>
              <CardContent className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => setShowConfirmSubmit(false)}
                  className="flex-1"
                >
                  Tiếp tục làm bài
                </Button>
                <Button
                  onClick={() => {
                    setShowConfirmSubmit(false);
                    submitTest();
                  }}
                  className="flex-1"
                >
                  Nộp bài
                </Button>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
