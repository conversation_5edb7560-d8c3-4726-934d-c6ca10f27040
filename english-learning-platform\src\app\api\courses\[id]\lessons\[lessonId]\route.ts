import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Course from '@/models/Course';
import Enrollment from '@/models/Enrollment';
import { getAuthUser } from '@/lib/auth';

interface RouteParams {
  params: {
    id: string;
    lessonId: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id, lessonId } = params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find course
    const course = await Course.findById(id);
    if (!course) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy khóa học'
      }, { status: 404 });
    }

    // Find lesson
    const lesson = course.curriculum.find((l: any) => l._id.toString() === lessonId);
    if (!lesson) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy bài học'
      }, { status: 404 });
    }

    // Check enrollment and access permissions
    let hasAccess = false;
    let enrollment = null;

    if (user.role === 'admin' || user.userId === course.instructor.toString()) {
      hasAccess = true;
    } else if (user.role === 'student') {
      enrollment = await Enrollment.findOne({
        studentId: user.userId,
        courseId: id
      });

      // Check if enrolled or lesson is preview
      hasAccess = !!enrollment || lesson.isPreview;
    }

    if (!hasAccess) {
      return NextResponse.json({
        success: false,
        message: 'Bạn cần đăng ký khóa học để xem bài học này'
      }, { status: 403 });
    }

    // Get lesson progress if enrolled
    let lessonProgress = null;
    if (enrollment) {
      lessonProgress = enrollment.lessonProgress.find(
        (lp: any) => lp.lessonId === lessonId
      );
    }

    // Prepare lesson data
    let lessonData = lesson.toJSON();

    // For non-enrolled users, hide premium content
    if (!enrollment && lesson.isPreview) {
      lessonData = {
        ...lessonData,
        materials: [],
        transcript: undefined,
        quiz: undefined
      };
    }

    // Add progress info
    const response = {
      ...lessonData,
      progress: lessonProgress ? {
        timeSpent: lessonProgress.timeSpent,
        videoProgress: lessonProgress.videoProgress,
        isCompleted: lessonProgress.isCompleted,
        quizScore: lessonProgress.quizScore,
        notes: lessonProgress.notes,
        bookmarks: lessonProgress.bookmarks || []
      } : null,
      courseInfo: {
        id: course._id,
        title: course.title,
        instructor: course.instructor
      }
    };

    return NextResponse.json({
      success: true,
      data: response
    });

  } catch (error) {
    console.error('Get lesson error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi tải bài học',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id, lessonId } = params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user || user.role !== 'student') {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Chỉ học viên mới có thể cập nhật tiến độ'
      }, { status: 401 });
    }

    // Find enrollment
    const enrollment = await Enrollment.findOne({
      studentId: user.userId,
      courseId: id
    });

    if (!enrollment) {
      return NextResponse.json({
        success: false,
        message: 'Bạn chưa đăng ký khóa học này'
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      timeSpent,
      videoProgress,
      isCompleted,
      quizScore,
      notes,
      bookmarks
    } = body;

    // Find or create lesson progress
    let lessonProgressIndex = enrollment.lessonProgress.findIndex(
      (lp: any) => lp.lessonId === lessonId
    );

    if (lessonProgressIndex === -1) {
      // Create new lesson progress
      enrollment.lessonProgress.push({
        lessonId,
        timeSpent: 0,
        videoProgress: 0,
        isCompleted: false,
        bookmarks: []
      });
      lessonProgressIndex = enrollment.lessonProgress.length - 1;
    }

    // Update lesson progress
    const lessonProgress = enrollment.lessonProgress[lessonProgressIndex];
    
    if (timeSpent !== undefined) {
      lessonProgress.timeSpent = Math.max(lessonProgress.timeSpent, timeSpent);
      enrollment.totalTimeSpent += Math.max(0, timeSpent - lessonProgress.timeSpent);
    }
    
    if (videoProgress !== undefined) {
      lessonProgress.videoProgress = Math.max(lessonProgress.videoProgress, videoProgress);
    }
    
    if (isCompleted !== undefined && isCompleted && !lessonProgress.isCompleted) {
      lessonProgress.isCompleted = true;
      lessonProgress.completedAt = new Date();
      
      // Add to completed lessons if not already there
      if (!enrollment.completedLessons.includes(lessonId)) {
        enrollment.completedLessons.push(lessonId);
      }
    }
    
    if (quizScore !== undefined) {
      lessonProgress.quizScore = quizScore;
    }
    
    if (notes !== undefined) {
      lessonProgress.notes = notes;
    }
    
    if (bookmarks !== undefined) {
      lessonProgress.bookmarks = bookmarks;
    }

    // Update current lesson
    enrollment.currentLesson = lessonId;
    enrollment.lastAccessedAt = new Date();

    // Recalculate overall progress
    const course = await Course.findById(id);
    if (course) {
      const totalLessons = course.curriculum.length;
      const completedCount = enrollment.completedLessons.length;
      enrollment.progress = Math.round((completedCount / totalLessons) * 100);

      // Check if course is completed
      if (enrollment.progress === 100 && !enrollment.completedAt) {
        enrollment.completedAt = new Date();
        // TODO: Generate certificate
      }
    }

    await enrollment.save();

    return NextResponse.json({
      success: true,
      message: 'Cập nhật tiến độ thành công',
      data: {
        progress: enrollment.progress,
        lessonProgress: lessonProgress,
        isCompleted: enrollment.progress === 100
      }
    });

  } catch (error) {
    console.error('Update lesson progress error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi cập nhật tiến độ bài học',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id, lessonId } = params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user || user.role !== 'student') {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Chỉ học viên mới có thể thêm bookmark'
      }, { status: 401 });
    }

    // Find enrollment
    const enrollment = await Enrollment.findOne({
      studentId: user.userId,
      courseId: id
    });

    if (!enrollment) {
      return NextResponse.json({
        success: false,
        message: 'Bạn chưa đăng ký khóa học này'
      }, { status: 403 });
    }

    const body = await request.json();
    const { timestamp, note } = body;

    if (!timestamp || !note) {
      return NextResponse.json({
        success: false,
        message: 'Timestamp và ghi chú là bắt buộc'
      }, { status: 400 });
    }

    // Find lesson progress
    let lessonProgress = enrollment.lessonProgress.find(
      (lp: any) => lp.lessonId === lessonId
    );

    if (!lessonProgress) {
      // Create new lesson progress
      lessonProgress = {
        lessonId,
        timeSpent: 0,
        videoProgress: 0,
        isCompleted: false,
        bookmarks: []
      };
      enrollment.lessonProgress.push(lessonProgress);
    }

    // Add bookmark
    const bookmark = {
      timestamp,
      note,
      createdAt: new Date()
    };

    lessonProgress.bookmarks = lessonProgress.bookmarks || [];
    lessonProgress.bookmarks.push(bookmark);

    await enrollment.save();

    return NextResponse.json({
      success: true,
      message: 'Thêm bookmark thành công',
      data: bookmark
    });

  } catch (error) {
    console.error('Add bookmark error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi thêm bookmark',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
