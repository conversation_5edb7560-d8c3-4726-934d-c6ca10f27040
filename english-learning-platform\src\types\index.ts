// Enum types
export enum UserRole {
  ADMIN = 'admin',
  TEACHER = 'teacher',
  STUDENT = 'student'
}

export enum CourseCategory {
  LISTENING = 'listening',
  SPEAKING = 'speaking',
  READING = 'reading',
  WRITING = 'writing',
  COMPREHENSIVE = 'comprehensive'
}

export enum CourseLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced'
}

export enum TestType {
  VOCABULARY = 'vocabulary',
  GRAMMAR = 'grammar',
  LISTENING = 'listening',
  SPEAKING = 'speaking',
  READING = 'reading',
  WRITING = 'writing',
  COMPREHENSIVE = 'comprehensive',
  PRACTICE = 'practice'
}

export enum QuestionType {
  MULTIPLE_CHOICE = 'multiple_choice',
  FILL_IN_BLANK = 'fill_in_blank',
  TRUE_FALSE = 'true_false',
  ESSAY = 'essay',
  AUDIO_RESPONSE = 'audio_response',
  DRAG_DROP = 'drag_drop',
  MATCHING = 'matching',
  // Enhanced question types for language skills
  LISTENING_MULTIPLE_CHOICE = 'listening_multiple_choice',
  LISTENING_FILL_BLANK = 'listening_fill_blank',
  SPEAKING_RECORD = 'speaking_record',
  READING_COMPREHENSION = 'reading_comprehension',
  WRITING_ESSAY = 'writing_essay',
  WRITING_SHORT_ANSWER = 'writing_short_answer'
}

export enum PaymentStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// User types
export interface User {
  _id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  phone?: string;
  dateOfBirth?: Date;
  isEmailVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
  profile?: UserProfile;
}

export interface UserProfile {
  bio?: string;
  experience?: string; // for teachers
  education?: string;
  skills?: string[];
}

// Course types
export interface Course {
  _id: string;
  title: string;
  description: string;
  shortDescription: string;
  teacherId: string;
  teacher?: User;
  category: CourseCategory;
  level: CourseLevel;
  price: number;
  duration: number; // in hours
  thumbnail?: string;
  videoIntro?: string;
  isPublished: boolean;
  curriculum: Lesson[];
  ratings: Rating[];
  averageRating: number;
  totalStudents: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Lesson {
  title: string;
  description: string;
  videoUrl?: string;
  materials: string[];
  duration: number; // in minutes
}

export interface Rating {
  userId: string;
  user?: User;
  rating: number;
  comment: string;
  createdAt: Date;
}

// Enrollment types
export interface Enrollment {
  _id: string;
  studentId: string;
  student?: User;
  courseId: string;
  course?: Course;
  enrolledAt: Date;
  progress: number; // percentage
  completedLessons: number[];
  lastAccessedAt: Date;
  paymentStatus: PaymentStatus;
  paymentId?: string;
}

// Test types
export interface Test {
  _id: string;
  title: string;
  description: string;
  type: TestType;
  level: CourseLevel;
  duration: number; // in minutes
  totalQuestions: number;
  passingScore: number;
  questions: Question[];
  createdBy: string;
  creator?: User;
  isPublished: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Question {
  _id?: string;
  type: QuestionType;
  question: string;
  options?: string[]; // for multiple choice
  correctAnswer: string | string[]; // can be array for multiple correct answers
  points: number;
  order?: number;
  explanation?: string;
  // Enhanced fields for language skills
  audioUrl?: string; // for listening questions
  audioSegments?: AudioSegment[]; // multiple audio files
  imageUrl?: string;
  readingPassage?: string; // for reading comprehension
  wordLimit?: number; // for writing questions
  timeLimit?: number; // for speaking questions (in seconds)
  keywords?: string[]; // for essay scoring
  rubric?: GradingRubric; // for manual grading
  autoGrade?: boolean; // whether to use automatic grading
}

export interface AudioSegment {
  url: string;
  title: string;
  duration: number; // in seconds
  transcript?: string; // for reference
}

export interface GradingRubric {
  criteria: GradingCriterion[];
  maxScore: number;
}

export interface GradingCriterion {
  name: string;
  description: string;
  maxPoints: number;
  levels: GradingLevel[];
}

export interface GradingLevel {
  score: number;
  description: string;
}

export interface TestResult {
  _id: string;
  studentId: string;
  student?: User;
  testId: string;
  test?: Test;
  answers: Answer[];
  totalScore: number;
  percentage: number;
  timeSpent: number; // in minutes
  startedAt: Date;
  completedAt: Date;
  feedback?: string;
}

export interface Answer {
  questionId: string;
  questionIndex: number;
  answer: string | string[]; // can be array for multiple answers
  isCorrect: boolean;
  points: number;
  maxPoints: number;
  // Enhanced fields for different answer types
  audioUrl?: string; // for speaking responses
  audioBlob?: Blob; // for client-side audio data
  timeSpent?: number; // time spent on question (in seconds)
  attempts?: number; // number of attempts
  feedback?: string; // teacher feedback
  gradedBy?: string; // teacher who graded (for manual grading)
  gradedAt?: Date; // when it was graded
  rubricScores?: RubricScore[]; // detailed scoring for essays/speaking
}

export interface RubricScore {
  criterionId: string;
  criterionName: string;
  score: number;
  maxScore: number;
  feedback?: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  role?: UserRole;
}

export interface CourseForm {
  title: string;
  description: string;
  shortDescription: string;
  category: CourseCategory;
  level: CourseLevel;
  price: number;
  duration: number;
  thumbnail?: string;
  videoIntro?: string;
  curriculum: Lesson[];
}

// Dashboard types
export interface DashboardStats {
  totalCourses: number;
  totalStudents: number;
  totalRevenue: number;
  totalTests: number;
}

export interface StudentDashboard {
  enrolledCourses: Course[];
  recentTests: TestResult[];
  progress: {
    courseId: string;
    courseName: string;
    progress: number;
  }[];
  stats: {
    totalCourses: number;
    completedCourses: number;
    averageScore: number;
    totalTestsTaken: number;
  };
}
