{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('vi-VN', {\n    style: 'currency',\n    currency: 'VND'\n  }).format(price);\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('vi-VN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  }).format(dateObj);\n}\n\nexport function formatDuration(minutes: number): string {\n  const hours = Math.floor(minutes / 60);\n  const remainingMinutes = minutes % 60;\n\n  if (hours === 0) {\n    return `${remainingMinutes} phút`;\n  } else if (remainingMinutes === 0) {\n    return `${hours} giờ`;\n  } else {\n    return `${hours} giờ ${remainingMinutes} phút`;\n  }\n}\n\nexport function calculateProgress(completedLessons: number[], totalLessons: number): number {\n  if (totalLessons === 0) return 0;\n  return Math.round((completedLessons.length / totalLessons) * 100);\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2);\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '') // Remove diacritics\n    .replace(/[^a-z0-9 -]/g, '') // Remove special characters\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen\n    .trim();\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n\n  if (password.length < 6) {\n    errors.push('Mật khẩu phải có ít nhất 6 ký tự');\n  }\n\n  if (!/[A-Z]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 chữ hoa');\n  }\n\n  if (!/[a-z]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 chữ thường');\n  }\n\n  if (!/[0-9]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 số');\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\nexport function generateRandomString(length: number): string {\n  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += characters.charAt(Math.floor(Math.random() * characters.length));\n  }\n  return result;\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,mBAAmB,UAAU;IAEnC,IAAI,UAAU,GAAG;QACf,OAAO,GAAG,iBAAiB,KAAK,CAAC;IACnC,OAAO,IAAI,qBAAqB,GAAG;QACjC,OAAO,GAAG,MAAM,IAAI,CAAC;IACvB,OAAO;QACL,OAAO,GAAG,MAAM,KAAK,EAAE,iBAAiB,KAAK,CAAC;IAChD;AACF;AAEO,SAAS,kBAAkB,gBAA0B,EAAE,YAAoB;IAChF,IAAI,iBAAiB,GAAG,OAAO;IAC/B,OAAO,KAAK,KAAK,CAAC,AAAC,iBAAiB,MAAM,GAAG,eAAgB;AAC/D;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAAI,oBAAoB;KACpD,OAAO,CAAC,gBAAgB,IAAI,4BAA4B;KACxD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,OAAO,KAAK,8CAA8C;KAClE,IAAI;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,qBAAqB,MAAc;IACjD,MAAM,aAAa;IACnB,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,WAAW,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM;IAC1E;IACA,OAAO;AACT;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { ChevronRight, Home } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface BreadcrumbItem {\n  label: string;\n  href?: string;\n  current?: boolean;\n}\n\ninterface BreadcrumbProps {\n  items: BreadcrumbItem[];\n  className?: string;\n  showHome?: boolean;\n}\n\nexport function Breadcrumb({ items, className, showHome = true }: BreadcrumbProps) {\n  const allItems = showHome \n    ? [{ label: \"Trang chủ\", href: \"/\" }, ...items]\n    : items;\n\n  return (\n    <nav className={cn(\"flex\", className)} aria-label=\"Breadcrumb\">\n      <ol className=\"inline-flex items-center space-x-1 md:space-x-3\">\n        {allItems.map((item, index) => (\n          <li key={index} className=\"inline-flex items-center\">\n            {index > 0 && (\n              <ChevronRight className=\"w-4 h-4 text-gray-400 mx-1\" />\n            )}\n            \n            {item.current || !item.href ? (\n              <span className=\"text-sm font-medium text-gray-500 flex items-center\">\n                {index === 0 && showHome && (\n                  <Home className=\"w-4 h-4 mr-1\" />\n                )}\n                {item.label}\n              </span>\n            ) : (\n              <Link\n                href={item.href}\n                className=\"inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors\"\n              >\n                {index === 0 && showHome && (\n                  <Home className=\"w-4 h-4 mr-1\" />\n                )}\n                {item.label}\n              </Link>\n            )}\n          </li>\n        ))}\n      </ol>\n    </nav>\n  );\n}\n\n// Hook để tự động tạo breadcrumb từ URL\nexport function useBreadcrumb() {\n  if (typeof window === 'undefined') return [];\n  \n  const pathname = window.location.pathname;\n  const segments = pathname.split('/').filter(Boolean);\n  \n  const breadcrumbMap: Record<string, string> = {\n    'courses': 'Khóa học',\n    'tests': 'Luyện thi',\n    'dashboard': 'Dashboard',\n    'profile': 'Hồ sơ',\n    'about': 'Giới thiệu',\n    'contact': 'Liên hệ',\n    'auth': 'Xác thực',\n    'login': 'Đăng nhập',\n    'register': 'Đăng ký',\n    'teacher': 'Giáo viên',\n    'admin': 'Quản trị',\n    'student': 'Học viên'\n  };\n\n  const items: BreadcrumbItem[] = [];\n  let currentPath = '';\n\n  segments.forEach((segment, index) => {\n    currentPath += `/${segment}`;\n    const isLast = index === segments.length - 1;\n    \n    items.push({\n      label: breadcrumbMap[segment] || segment,\n      href: isLast ? undefined : currentPath,\n      current: isLast\n    });\n  });\n\n  return items;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;;;;;AAcO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,IAAI,EAAmB;IAC/E,MAAM,WAAW,WACb;QAAC;YAAE,OAAO;YAAa,MAAM;QAAI;WAAM;KAAM,GAC7C;IAEJ,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAY,cAAW;kBAChD,cAAA,8OAAC;YAAG,WAAU;sBACX,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC;oBAAe,WAAU;;wBACvB,QAAQ,mBACP,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAGzB,KAAK,OAAO,IAAI,CAAC,KAAK,IAAI,iBACzB,8OAAC;4BAAK,WAAU;;gCACb,UAAU,KAAK,0BACd,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAEjB,KAAK,KAAK;;;;;;iDAGb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAU;;gCAET,UAAU,KAAK,0BACd,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAEjB,KAAK,KAAK;;;;;;;;mBApBR;;;;;;;;;;;;;;;AA4BnB;AAGO,SAAS;IACd,wCAAmC,OAAO,EAAE;;IAE5C,MAAM;IACN,MAAM;IAEN,MAAM;IAeN,MAAM;IACN,IAAI;AAcN", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/loading.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\n\ninterface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\";\n  className?: string;\n}\n\nexport function LoadingSpinner({ size = \"md\", className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: \"h-4 w-4\",\n    md: \"h-6 w-6\", \n    lg: \"h-8 w-8\"\n  };\n\n  return (\n    <div\n      className={cn(\n        \"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600\",\n        sizeClasses[size],\n        className\n      )}\n    />\n  );\n}\n\ninterface LoadingCardProps {\n  className?: string;\n}\n\nexport function LoadingCard({ className }: LoadingCardProps) {\n  return (\n    <div className={cn(\"animate-pulse\", className)}>\n      <div className=\"bg-gray-200 rounded-lg h-48 mb-4\"></div>\n      <div className=\"space-y-3\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-5/6\"></div>\n      </div>\n    </div>\n  );\n}\n\ninterface LoadingPageProps {\n  message?: string;\n}\n\nexport function LoadingPage({ message = \"Đang tải...\" }: LoadingPageProps) {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <LoadingSpinner size=\"lg\" className=\"mx-auto mb-4\" />\n        <p className=\"text-gray-600\">{message}</p>\n      </div>\n    </div>\n  );\n}\n\ninterface LoadingButtonProps {\n  children: React.ReactNode;\n  isLoading?: boolean;\n  className?: string;\n  disabled?: boolean;\n  onClick?: () => void;\n  type?: \"button\" | \"submit\" | \"reset\";\n}\n\nexport function LoadingButton({ \n  children, \n  isLoading = false, \n  className,\n  disabled,\n  onClick,\n  type = \"button\"\n}: LoadingButtonProps) {\n  return (\n    <button\n      type={type}\n      onClick={onClick}\n      disabled={disabled || isLoading}\n      className={cn(\n        \"inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n        className\n      )}\n    >\n      {isLoading && <LoadingSpinner size=\"sm\" className=\"mr-2\" />}\n      {children}\n    </button>\n  );\n}\n\nexport function LoadingOverlay() {\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 flex items-center space-x-3\">\n        <LoadingSpinner size=\"md\" />\n        <span className=\"text-gray-700\">Đang xử lý...</span>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR;AAMO,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;;0BAClC,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;AAMO,SAAS,YAAY,EAAE,UAAU,aAAa,EAAoB;IACvE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAe,MAAK;oBAAK,WAAU;;;;;;8BACpC,8OAAC;oBAAE,WAAU;8BAAiB;;;;;;;;;;;;;;;;;AAItC;AAWO,SAAS,cAAc,EAC5B,QAAQ,EACR,YAAY,KAAK,EACjB,SAAS,EACT,QAAQ,EACR,OAAO,EACP,OAAO,QAAQ,EACI;IACnB,qBACE,8OAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU,YAAY;QACtB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+QACA;;YAGD,2BAAa,8OAAC;gBAAe,MAAK;gBAAK,WAAU;;;;;;YACjD;;;;;;;AAGP;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAe,MAAK;;;;;;8BACrB,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAIxC", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/error.tsx"], "sourcesContent": ["import { Alert<PERSON><PERSON>gle, <PERSON>fresh<PERSON><PERSON>, Home, ArrowLeft } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { cn } from \"@/lib/utils\";\n\ninterface ErrorMessageProps {\n  title?: string;\n  message?: string;\n  className?: string;\n}\n\nexport function ErrorMessage({ \n  title = \"Đã xảy ra lỗi\", \n  message = \"Vui lòng thử lại sau.\", \n  className \n}: ErrorMessageProps) {\n  return (\n    <div className={cn(\"flex items-center space-x-2 text-red-600\", className)}>\n      <AlertTriangle className=\"h-4 w-4\" />\n      <div>\n        <p className=\"font-medium text-sm\">{title}</p>\n        {message && <p className=\"text-xs text-red-500\">{message}</p>}\n      </div>\n    </div>\n  );\n}\n\ninterface ErrorCardProps {\n  title?: string;\n  message?: string;\n  onRetry?: () => void;\n  className?: string;\n}\n\nexport function ErrorCard({ \n  title = \"Không thể tải dữ liệu\", \n  message = \"Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại.\", \n  onRetry,\n  className \n}: ErrorCardProps) {\n  return (\n    <Card className={cn(\"border-red-200\", className)}>\n      <CardContent className=\"pt-6\">\n        <div className=\"text-center\">\n          <AlertTriangle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">{title}</h3>\n          <p className=\"text-gray-600 mb-4\">{message}</p>\n          {onRetry && (\n            <Button onClick={onRetry} variant=\"outline\">\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\n              Thử lại\n            </Button>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n\ninterface ErrorPageProps {\n  title?: string;\n  message?: string;\n  showHomeButton?: boolean;\n  showBackButton?: boolean;\n  onRetry?: () => void;\n}\n\nexport function ErrorPage({ \n  title = \"Oops! Đã xảy ra lỗi\", \n  message = \"Trang bạn đang tìm kiếm không tồn tại hoặc đã xảy ra lỗi hệ thống.\",\n  showHomeButton = true,\n  showBackButton = true,\n  onRetry\n}: ErrorPageProps) {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center px-4\">\n      <div className=\"max-w-md w-full text-center\">\n        <div className=\"mb-8\">\n          <AlertTriangle className=\"h-24 w-24 text-red-500 mx-auto mb-6\" />\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">{title}</h1>\n          <p className=\"text-gray-600 mb-8\">{message}</p>\n        </div>\n        \n        <div className=\"space-y-3\">\n          {onRetry && (\n            <Button onClick={onRetry} className=\"w-full\">\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\n              Thử lại\n            </Button>\n          )}\n          \n          {showHomeButton && (\n            <Button variant=\"outline\" className=\"w-full\" onClick={() => window.location.href = '/'}>\n              <Home className=\"h-4 w-4 mr-2\" />\n              Về trang chủ\n            </Button>\n          )}\n          \n          {showBackButton && (\n            <Button variant=\"ghost\" className=\"w-full\" onClick={() => window.history.back()}>\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Quay lại\n            </Button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface FormErrorProps {\n  errors: Record<string, string>;\n  className?: string;\n}\n\nexport function FormError({ errors, className }: FormErrorProps) {\n  const errorMessages = Object.values(errors).filter(Boolean);\n  \n  if (errorMessages.length === 0) return null;\n\n  return (\n    <div className={cn(\"bg-red-50 border border-red-200 rounded-md p-3\", className)}>\n      <div className=\"flex\">\n        <AlertTriangle className=\"h-4 w-4 text-red-400 mt-0.5 mr-2 flex-shrink-0\" />\n        <div className=\"text-sm\">\n          {errorMessages.length === 1 ? (\n            <p className=\"text-red-800\">{errorMessages[0]}</p>\n          ) : (\n            <div>\n              <p className=\"text-red-800 font-medium mb-1\">Vui lòng kiểm tra lại:</p>\n              <ul className=\"list-disc list-inside text-red-700 space-y-1\">\n                {errorMessages.map((error, index) => (\n                  <li key={index}>{error}</li>\n                ))}\n              </ul>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface NetworkErrorProps {\n  onRetry?: () => void;\n  className?: string;\n}\n\nexport function NetworkError({ onRetry, className }: NetworkErrorProps) {\n  return (\n    <div className={cn(\"text-center py-8\", className)}>\n      <div className=\"mb-4\">\n        <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n          <AlertTriangle className=\"h-8 w-8 text-red-600\" />\n        </div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n          Lỗi kết nối mạng\n        </h3>\n        <p className=\"text-gray-600 mb-4\">\n          Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối internet và thử lại.\n        </p>\n      </div>\n      \n      {onRetry && (\n        <Button onClick={onRetry} variant=\"outline\">\n          <RefreshCw className=\"h-4 w-4 mr-2\" />\n          Thử lại\n        </Button>\n      )}\n    </div>\n  );\n}\n\ninterface NotFoundProps {\n  title?: string;\n  message?: string;\n  showHomeButton?: boolean;\n}\n\nexport function NotFound({ \n  title = \"Không tìm thấy trang\", \n  message = \"Trang bạn đang tìm kiếm không tồn tại hoặc đã được di chuyển.\",\n  showHomeButton = true\n}: NotFoundProps) {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center px-4\">\n      <div className=\"max-w-md w-full text-center\">\n        <div className=\"mb-8\">\n          <div className=\"text-6xl font-bold text-gray-400 mb-4\">404</div>\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">{title}</h1>\n          <p className=\"text-gray-600 mb-8\">{message}</p>\n        </div>\n        \n        {showHomeButton && (\n          <Button onClick={() => window.location.href = '/'}>\n            <Home className=\"h-4 w-4 mr-2\" />\n            Về trang chủ\n          </Button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAQO,SAAS,aAAa,EAC3B,QAAQ,eAAe,EACvB,UAAU,uBAAuB,EACjC,SAAS,EACS;IAClB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;;0BAC7D,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,8OAAC;;kCACC,8OAAC;wBAAE,WAAU;kCAAuB;;;;;;oBACnC,yBAAW,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAIzD;AASO,SAAS,UAAU,EACxB,QAAQ,uBAAuB,EAC/B,UAAU,kDAAkD,EAC5D,OAAO,EACP,SAAS,EACM;IACf,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBACpC,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;oBAClC,yBACC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAS,SAAQ;;0CAChC,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;;;;;AAQpD;AAUO,SAAS,UAAU,EACxB,QAAQ,qBAAqB,EAC7B,UAAU,oEAAoE,EAC9E,iBAAiB,IAAI,EACrB,iBAAiB,IAAI,EACrB,OAAO,EACQ;IACf,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAGrC,8OAAC;oBAAI,WAAU;;wBACZ,yBACC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAS,WAAU;;8CAClC,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;wBAKzC,gCACC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,WAAU;4BAAS,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8CACjF,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;wBAKpC,gCACC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,WAAU;4BAAS,SAAS,IAAM,OAAO,OAAO,CAAC,IAAI;;8CAC3E,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAQpD;AAOO,SAAS,UAAU,EAAE,MAAM,EAAE,SAAS,EAAkB;IAC7D,MAAM,gBAAgB,OAAO,MAAM,CAAC,QAAQ,MAAM,CAAC;IAEnD,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;kBACnE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;8BACzB,8OAAC;oBAAI,WAAU;8BACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;wBAAE,WAAU;kCAAgB,aAAa,CAAC,EAAE;;;;;6CAE7C,8OAAC;;0CACC,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;0CAC7C,8OAAC;gCAAG,WAAU;0CACX,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC;kDAAgB;uCAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3B;AAOO,SAAS,aAAa,EAAE,OAAO,EAAE,SAAS,EAAqB;IACpE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;;0BACrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;kCAE3B,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;YAKnC,yBACC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,SAAS;gBAAS,SAAQ;;kCAChC,8OAAC,gNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;;;;;;AAMhD;AAQO,SAAS,SAAS,EACvB,QAAQ,sBAAsB,EAC9B,UAAU,+DAA+D,EACzE,iBAAiB,IAAI,EACP;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAwC;;;;;;sCACvD,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;gBAGpC,gCACC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;sCAC5C,8OAAC,mMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;;;;;AAO7C", "debugId": null}}, {"offset": {"line": 1049, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1363, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { useState } from \"react\";\nimport { Book<PERSON>pen, Menu, X } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { useAuth } from \"@/contexts/AuthContext\";\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const { user, isAuthenticated, logout } = useAuth();\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  const handleLogout = async () => {\n    await logout();\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <BookOpen className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-xl font-bold text-gray-900\">\n              English Learning Platform\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <Link\n              href=\"/courses\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Khóa Học\n            </Link>\n            <Link\n              href=\"/tests\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Luyện Thi\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Giới Thiệu\n            </Link>\n            <Link\n              href=\"/contact\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Liên Hệ\n            </Link>\n          </nav>\n\n          {/* Desktop Auth/User Menu */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {isAuthenticated && user ? (\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                    <Avatar className=\"h-8 w-8\">\n                      <AvatarImage src={user.avatar} alt={user.name} />\n                      <AvatarFallback>\n                        {user.name.split(' ').map((n: string) => n[0]).join('')}\n                      </AvatarFallback>\n                    </Avatar>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                  <DropdownMenuLabel className=\"font-normal\">\n                    <div className=\"flex flex-col space-y-1\">\n                      <p className=\"text-sm font-medium leading-none\">{user.name}</p>\n                      <p className=\"text-xs leading-none text-muted-foreground\">\n                        {user.email}\n                      </p>\n                    </div>\n                  </DropdownMenuLabel>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard\">Dashboard</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/profile\">Hồ sơ cá nhân</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/settings\">Cài đặt</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem onClick={handleLogout}>\n                    Đăng xuất\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            ) : (\n              <>\n                <Link href=\"/auth/login\">\n                  <Button variant=\"ghost\">\n                    Đăng Nhập\n                  </Button>\n                </Link>\n                <Link href=\"/auth/register\">\n                  <Button>\n                    Đăng Ký\n                  </Button>\n                </Link>\n              </>\n            )}\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            className=\"md:hidden p-2\"\n            onClick={toggleMenu}\n            aria-label=\"Toggle menu\"\n          >\n            {isMenuOpen ? (\n              <X className=\"h-6 w-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"h-6 w-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <Link\n                href=\"/courses\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Khóa Học\n              </Link>\n              <Link\n                href=\"/tests\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Luyện Thi\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Giới Thiệu\n              </Link>\n              <Link\n                href=\"/contact\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Liên Hệ\n              </Link>\n\n              <div className=\"flex flex-col space-y-2 pt-4 border-t border-gray-200\">\n                {isAuthenticated && user ? (\n                  <>\n                    <div className=\"flex items-center space-x-3 px-3 py-2\">\n                      <Avatar className=\"h-8 w-8\">\n                        <AvatarImage src={user.avatar} alt={user.name} />\n                        <AvatarFallback>\n                          {user.name.split(' ').map((n: string) => n[0]).join('')}\n                        </AvatarFallback>\n                      </Avatar>\n                      <div>\n                        <p className=\"text-sm font-medium\">{user.name}</p>\n                        <p className=\"text-xs text-gray-500\">{user.email}</p>\n                      </div>\n                    </div>\n                    <Link href=\"/dashboard\" onClick={() => setIsMenuOpen(false)}>\n                      <Button variant=\"ghost\" className=\"w-full justify-start\">\n                        Dashboard\n                      </Button>\n                    </Link>\n                    <Link href=\"/profile\" onClick={() => setIsMenuOpen(false)}>\n                      <Button variant=\"ghost\" className=\"w-full justify-start\">\n                        Hồ sơ cá nhân\n                      </Button>\n                    </Link>\n                    <Button\n                      variant=\"ghost\"\n                      className=\"w-full justify-start\"\n                      onClick={handleLogout}\n                    >\n                      Đăng xuất\n                    </Button>\n                  </>\n                ) : (\n                  <>\n                    <Link href=\"/auth/login\" onClick={() => setIsMenuOpen(false)}>\n                      <Button variant=\"ghost\" className=\"w-full justify-start\">\n                        Đăng Nhập\n                      </Button>\n                    </Link>\n                    <Link href=\"/auth/register\" onClick={() => setIsMenuOpen(false)}>\n                      <Button className=\"w-full\">\n                        Đăng Ký\n                      </Button>\n                    </Link>\n                  </>\n                )}\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAQA;AACA;AAfA;;;;;;;;;AAiBe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhD,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,MAAM,eAAe;QACnB,MAAM;QACN,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAMzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;sCACZ,mBAAmB,qBAClB,8OAAC,4IAAA,CAAA,eAAY;;kDACX,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,WAAU;sDAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kIAAA,CAAA,cAAW;wDAAC,KAAK,KAAK,MAAM;wDAAE,KAAK,KAAK,IAAI;;;;;;kEAC7C,8OAAC,kIAAA,CAAA,iBAAc;kEACZ,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;kDAK5D,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,WAAU;wCAAO,OAAM;wCAAM,UAAU;;0DAC1D,8OAAC,4IAAA,CAAA,oBAAiB;gDAAC,WAAU;0DAC3B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAoC,KAAK,IAAI;;;;;;sEAC1D,8OAAC;4DAAE,WAAU;sEACV,KAAK,KAAK;;;;;;;;;;;;;;;;;0DAIjB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0DACtB,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAa;;;;;;;;;;;0DAE1B,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAW;;;;;;;;;;;0DAExB,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAY;;;;;;;;;;;0DAEzB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0DACtB,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS;0DAAc;;;;;;;;;;;;;;;;;qDAM7C;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAQ;;;;;;;;;;;kDAI1B,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;;;;;;;;sCAShB,8OAAC;4BACC,WAAU;4BACV,SAAS;4BACT,cAAW;sCAEV,2BACC,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAID,8OAAC;gCAAI,WAAU;0CACZ,mBAAmB,qBAClB;;sDACE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;;sEAChB,8OAAC,kIAAA,CAAA,cAAW;4DAAC,KAAK,KAAK,MAAM;4DAAE,KAAK,KAAK,IAAI;;;;;;sEAC7C,8OAAC,kIAAA,CAAA,iBAAc;sEACZ,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;8DAGxD,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAuB,KAAK,IAAI;;;;;;sEAC7C,8OAAC;4DAAE,WAAU;sEAAyB,KAAK,KAAK;;;;;;;;;;;;;;;;;;sDAGpD,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,SAAS,IAAM,cAAc;sDACnD,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAAuB;;;;;;;;;;;sDAI3D,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,SAAS,IAAM,cAAc;sDACjD,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAAuB;;;;;;;;;;;sDAI3D,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;sDACV;;;;;;;iEAKH;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,SAAS,IAAM,cAAc;sDACpD,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAAuB;;;;;;;;;;;sDAI3D,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAiB,SAAS,IAAM,cAAc;sDACvD,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAajD", "debugId": null}}, {"offset": {"line": 1915, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/types/index.ts"], "sourcesContent": ["// Enum types\nexport enum UserRole {\n  ADMIN = 'admin',\n  TEACHER = 'teacher',\n  STUDENT = 'student'\n}\n\nexport enum CourseCategory {\n  LISTENING = 'listening',\n  SPEAKING = 'speaking',\n  READING = 'reading',\n  WRITING = 'writing',\n  COMPREHENSIVE = 'comprehensive'\n}\n\nexport enum CourseLevel {\n  BEGINNER = 'beginner',\n  INTERMEDIATE = 'intermediate',\n  ADVANCED = 'advanced'\n}\n\nexport enum TestType {\n  VOCABULARY = 'vocabulary',\n  GRAMMAR = 'grammar',\n  LISTENING = 'listening',\n  SPEAKING = 'speaking',\n  READING = 'reading',\n  WRITING = 'writing',\n  COMPREHENSIVE = 'comprehensive',\n  PRACTICE = 'practice'\n}\n\nexport enum QuestionType {\n  MULTIPLE_CHOICE = 'multiple_choice',\n  FILL_IN_BLANK = 'fill_in_blank',\n  TRUE_FALSE = 'true_false',\n  ESSAY = 'essay',\n  AUDIO_RESPONSE = 'audio_response',\n  DRAG_DROP = 'drag_drop',\n  MATCHING = 'matching',\n  // Enhanced question types for language skills\n  LISTENING_MULTIPLE_CHOICE = 'listening_multiple_choice',\n  LISTENING_FILL_BLANK = 'listening_fill_blank',\n  SPEAKING_RECORD = 'speaking_record',\n  READING_COMPREHENSION = 'reading_comprehension',\n  WRITING_ESSAY = 'writing_essay',\n  WRITING_SHORT_ANSWER = 'writing_short_answer'\n}\n\nexport enum PaymentStatus {\n  PENDING = 'pending',\n  COMPLETED = 'completed',\n  FAILED = 'failed'\n}\n\n// User types\nexport interface User {\n  _id: string;\n  email: string;\n  name: string;\n  role: UserRole;\n  avatar?: string;\n  phone?: string;\n  dateOfBirth?: Date;\n  isEmailVerified: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n  profile?: UserProfile;\n}\n\nexport interface UserProfile {\n  bio?: string;\n  experience?: string; // for teachers\n  education?: string;\n  skills?: string[];\n}\n\n// Course types\nexport interface Course {\n  _id: string;\n  title: string;\n  description: string;\n  shortDescription: string;\n  teacherId: string;\n  teacher?: User;\n  category: CourseCategory;\n  level: CourseLevel;\n  price: number;\n  duration: number; // in hours\n  thumbnail?: string;\n  videoIntro?: string;\n  isPublished: boolean;\n  curriculum: Lesson[];\n  ratings: Rating[];\n  averageRating: number;\n  totalStudents: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Lesson {\n  title: string;\n  description: string;\n  videoUrl?: string;\n  materials: string[];\n  duration: number; // in minutes\n}\n\nexport interface Rating {\n  userId: string;\n  user?: User;\n  rating: number;\n  comment: string;\n  createdAt: Date;\n}\n\n// Enrollment types\nexport interface Enrollment {\n  _id: string;\n  studentId: string;\n  student?: User;\n  courseId: string;\n  course?: Course;\n  enrolledAt: Date;\n  progress: number; // percentage\n  completedLessons: number[];\n  lastAccessedAt: Date;\n  paymentStatus: PaymentStatus;\n  paymentId?: string;\n}\n\n// Test types\nexport interface Test {\n  _id: string;\n  title: string;\n  description: string;\n  type: TestType;\n  level: CourseLevel;\n  duration: number; // in minutes\n  totalQuestions: number;\n  passingScore: number;\n  questions: Question[];\n  createdBy: string;\n  creator?: User;\n  isPublished: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Question {\n  _id?: string;\n  type: QuestionType;\n  question: string;\n  options?: string[]; // for multiple choice\n  correctAnswer: string | string[]; // can be array for multiple correct answers\n  points: number;\n  order?: number;\n  explanation?: string;\n  // Enhanced fields for language skills\n  audioUrl?: string; // for listening questions\n  audioSegments?: AudioSegment[]; // multiple audio files\n  imageUrl?: string;\n  readingPassage?: string; // for reading comprehension\n  wordLimit?: number; // for writing questions\n  timeLimit?: number; // for speaking questions (in seconds)\n  keywords?: string[]; // for essay scoring\n  rubric?: GradingRubric; // for manual grading\n  autoGrade?: boolean; // whether to use automatic grading\n}\n\nexport interface AudioSegment {\n  url: string;\n  title: string;\n  duration: number; // in seconds\n  transcript?: string; // for reference\n}\n\nexport interface GradingRubric {\n  criteria: GradingCriterion[];\n  maxScore: number;\n}\n\nexport interface GradingCriterion {\n  name: string;\n  description: string;\n  maxPoints: number;\n  levels: GradingLevel[];\n}\n\nexport interface GradingLevel {\n  score: number;\n  description: string;\n}\n\nexport interface TestResult {\n  _id: string;\n  studentId: string;\n  student?: User;\n  testId: string;\n  test?: Test;\n  answers: Answer[];\n  totalScore: number;\n  percentage: number;\n  timeSpent: number; // in minutes\n  startedAt: Date;\n  completedAt: Date;\n  feedback?: string;\n}\n\nexport interface Answer {\n  questionId: string;\n  questionIndex: number;\n  answer: string | string[]; // can be array for multiple answers\n  isCorrect: boolean;\n  points: number;\n  maxPoints: number;\n  // Enhanced fields for different answer types\n  audioUrl?: string; // for speaking responses\n  audioBlob?: Blob; // for client-side audio data\n  timeSpent?: number; // time spent on question (in seconds)\n  attempts?: number; // number of attempts\n  feedback?: string; // teacher feedback\n  gradedBy?: string; // teacher who graded (for manual grading)\n  gradedAt?: Date; // when it was graded\n  rubricScores?: RubricScore[]; // detailed scoring for essays/speaking\n}\n\nexport interface RubricScore {\n  criterionId: string;\n  criterionName: string;\n  score: number;\n  maxScore: number;\n  feedback?: string;\n}\n\n// API Response types\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\n// Form types\nexport interface LoginForm {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterForm {\n  name: string;\n  email: string;\n  password: string;\n  confirmPassword: string;\n  role?: UserRole;\n}\n\nexport interface CourseForm {\n  title: string;\n  description: string;\n  shortDescription: string;\n  category: CourseCategory;\n  level: CourseLevel;\n  price: number;\n  duration: number;\n  thumbnail?: string;\n  videoIntro?: string;\n  curriculum: Lesson[];\n}\n\n// Dashboard types\nexport interface DashboardStats {\n  totalCourses: number;\n  totalStudents: number;\n  totalRevenue: number;\n  totalTests: number;\n}\n\nexport interface StudentDashboard {\n  enrolledCourses: Course[];\n  recentTests: TestResult[];\n  progress: {\n    courseId: string;\n    courseName: string;\n    progress: number;\n  }[];\n  stats: {\n    totalCourses: number;\n    completedCourses: number;\n    averageScore: number;\n    totalTestsTaken: number;\n  };\n}\n"], "names": [], "mappings": "AAAA,aAAa;;;;;;;;;AACN,IAAA,AAAK,kCAAA;;;;WAAA;;AAML,IAAA,AAAK,wCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,qCAAA;;;;WAAA;;AAML,IAAA,AAAK,kCAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,sCAAA;;;;;;;;IAQV,8CAA8C;;;;;;;WARpC;;AAiBL,IAAA,AAAK,uCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 2010, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/test/QuestionRenderer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Play, Pause, Volume2, Mic, MicOff } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { QuestionType } from \"@/types\";\n\ninterface Question {\n  id: string;\n  type: QuestionType;\n  question: string;\n  options?: string[];\n  correctAnswer: string | string[];\n  points: number;\n  audioUrl?: string;\n  imageUrl?: string;\n  timeLimit?: number;\n  order: number;\n}\n\ninterface QuestionRendererProps {\n  question: Question;\n  userAnswer?: string | string[];\n  onAnswerChange: (answer: string | string[]) => void;\n  isReadOnly?: boolean;\n  showCorrectAnswer?: boolean;\n}\n\nexport default function QuestionRenderer({\n  question,\n  userAnswer,\n  onAnswerChange,\n  isReadOnly = false,\n  showCorrectAnswer = false\n}: QuestionRendererProps) {\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isRecording, setIsRecording] = useState(false);\n  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);\n\n  const handleMultipleChoice = (selectedOption: string) => {\n    if (isReadOnly) return;\n    onAnswerChange(selectedOption);\n  };\n\n  const handleFillInBlank = (value: string) => {\n    if (isReadOnly) return;\n    onAnswerChange(value);\n  };\n\n  const handleTrueFalse = (value: string) => {\n    if (isReadOnly) return;\n    onAnswerChange(value);\n  };\n\n  const handleEssay = (value: string) => {\n    if (isReadOnly) return;\n    onAnswerChange(value);\n  };\n\n  const handleAudioPlay = () => {\n    if (question.audioUrl) {\n      const audio = new Audio(question.audioUrl);\n      setIsPlaying(true);\n      audio.play();\n      audio.onended = () => setIsPlaying(false);\n    }\n  };\n\n  const handleStartRecording = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      const mediaRecorder = new MediaRecorder(stream);\n      const chunks: BlobPart[] = [];\n\n      mediaRecorder.ondataavailable = (event) => {\n        chunks.push(event.data);\n      };\n\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunks, { type: 'audio/wav' });\n        setAudioBlob(blob);\n        onAnswerChange(URL.createObjectURL(blob));\n      };\n\n      mediaRecorder.start();\n      setIsRecording(true);\n\n      // Stop recording after 2 minutes max\n      setTimeout(() => {\n        if (mediaRecorder.state === 'recording') {\n          mediaRecorder.stop();\n          setIsRecording(false);\n        }\n      }, 120000);\n\n    } catch (error) {\n      console.error('Error accessing microphone:', error);\n    }\n  };\n\n  const handleStopRecording = () => {\n    setIsRecording(false);\n    // MediaRecorder will handle the stop event\n  };\n\n  const renderQuestionContent = () => {\n    switch (question.type) {\n      case QuestionType.MULTIPLE_CHOICE:\n        return (\n          <div className=\"space-y-3\">\n            {question.options?.map((option, index) => (\n              <label\n                key={index}\n                className={`\n                  flex items-center p-4 border rounded-lg cursor-pointer transition-colors\n                  ${isReadOnly ? 'cursor-default' : 'cursor-pointer'}\n                  ${userAnswer === option\n                    ? showCorrectAnswer\n                      ? option === question.correctAnswer\n                        ? 'border-green-500 bg-green-50'\n                        : 'border-red-500 bg-red-50'\n                      : 'border-blue-500 bg-blue-50'\n                    : showCorrectAnswer && option === question.correctAnswer\n                      ? 'border-green-500 bg-green-50'\n                      : 'border-gray-200 hover:border-gray-300'\n                  }\n                `}\n              >\n                <input\n                  type=\"radio\"\n                  name={`question-${question.id}`}\n                  value={option}\n                  checked={userAnswer === option}\n                  onChange={(e) => handleMultipleChoice(e.target.value)}\n                  disabled={isReadOnly}\n                  className=\"mr-3\"\n                />\n                <span className=\"text-gray-900\">{option}</span>\n                {showCorrectAnswer && option === question.correctAnswer && (\n                  <Badge variant=\"default\" className=\"ml-auto\">Đáp án đúng</Badge>\n                )}\n              </label>\n            ))}\n          </div>\n        );\n\n      case QuestionType.FILL_IN_BLANK:\n        return (\n          <div className=\"space-y-4\">\n            <Input\n              value={userAnswer as string || ''}\n              onChange={(e) => handleFillInBlank(e.target.value)}\n              placeholder=\"Nhập câu trả lời của bạn...\"\n              disabled={isReadOnly}\n              className={showCorrectAnswer \n                ? userAnswer === question.correctAnswer\n                  ? 'border-green-500 bg-green-50'\n                  : 'border-red-500 bg-red-50'\n                : ''\n              }\n            />\n            {showCorrectAnswer && (\n              <div className=\"p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n                <span className=\"font-medium\">Đáp án đúng: </span>\n                <span className=\"text-blue-800\">{question.correctAnswer}</span>\n              </div>\n            )}\n          </div>\n        );\n\n      case QuestionType.TRUE_FALSE:\n        return (\n          <div className=\"space-y-3\">\n            {['Đúng', 'Sai'].map((option) => (\n              <label\n                key={option}\n                className={`\n                  flex items-center p-4 border rounded-lg cursor-pointer transition-colors\n                  ${isReadOnly ? 'cursor-default' : 'cursor-pointer'}\n                  ${userAnswer === option\n                    ? showCorrectAnswer\n                      ? option === question.correctAnswer\n                        ? 'border-green-500 bg-green-50'\n                        : 'border-red-500 bg-red-50'\n                      : 'border-blue-500 bg-blue-50'\n                    : showCorrectAnswer && option === question.correctAnswer\n                      ? 'border-green-500 bg-green-50'\n                      : 'border-gray-200 hover:border-gray-300'\n                  }\n                `}\n              >\n                <input\n                  type=\"radio\"\n                  name={`question-${question.id}`}\n                  value={option}\n                  checked={userAnswer === option}\n                  onChange={(e) => handleTrueFalse(e.target.value)}\n                  disabled={isReadOnly}\n                  className=\"mr-3\"\n                />\n                <span className=\"text-gray-900\">{option}</span>\n                {showCorrectAnswer && option === question.correctAnswer && (\n                  <Badge variant=\"default\" className=\"ml-auto\">Đáp án đúng</Badge>\n                )}\n              </label>\n            ))}\n          </div>\n        );\n\n      case QuestionType.ESSAY:\n        return (\n          <div className=\"space-y-4\">\n            <textarea\n              value={userAnswer as string || ''}\n              onChange={(e) => handleEssay(e.target.value)}\n              placeholder=\"Viết câu trả lời của bạn (tối thiểu 50 từ)...\"\n              disabled={isReadOnly}\n              rows={8}\n              className={`\n                w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\n                ${isReadOnly ? 'bg-gray-50' : ''}\n              `}\n            />\n            <div className=\"text-sm text-gray-500\">\n              Số từ: {(userAnswer as string || '').split(' ').filter(word => word.length > 0).length}\n            </div>\n            {showCorrectAnswer && (\n              <div className=\"p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n                <span className=\"font-medium\">Gợi ý đáp án: </span>\n                <p className=\"text-blue-800 mt-2\">{question.correctAnswer}</p>\n              </div>\n            )}\n          </div>\n        );\n\n      case QuestionType.AUDIO_RESPONSE:\n        return (\n          <div className=\"space-y-4\">\n            {question.audioUrl && (\n              <div className=\"p-4 bg-gray-50 rounded-lg\">\n                <div className=\"flex items-center gap-3\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={handleAudioPlay}\n                    disabled={isPlaying}\n                  >\n                    {isPlaying ? <Pause className=\"h-4 w-4\" /> : <Play className=\"h-4 w-4\" />}\n                    {isPlaying ? 'Đang phát' : 'Nghe audio'}\n                  </Button>\n                  <Volume2 className=\"h-4 w-4 text-gray-500\" />\n                  <span className=\"text-sm text-gray-600\">Nghe và ghi âm câu trả lời</span>\n                </div>\n              </div>\n            )}\n            \n            <div className=\"p-4 border-2 border-dashed border-gray-300 rounded-lg text-center\">\n              {!isRecording && !audioBlob && (\n                <div>\n                  <Mic className=\"h-8 w-8 text-gray-400 mx-auto mb-2\" />\n                  <p className=\"text-gray-600 mb-3\">Nhấn để bắt đầu ghi âm câu trả lời</p>\n                  <Button onClick={handleStartRecording} disabled={isReadOnly}>\n                    <Mic className=\"h-4 w-4 mr-2\" />\n                    Bắt đầu ghi âm\n                  </Button>\n                </div>\n              )}\n              \n              {isRecording && (\n                <div>\n                  <div className=\"animate-pulse\">\n                    <MicOff className=\"h-8 w-8 text-red-500 mx-auto mb-2\" />\n                  </div>\n                  <p className=\"text-red-600 mb-3\">Đang ghi âm...</p>\n                  <Button variant=\"destructive\" onClick={handleStopRecording}>\n                    Dừng ghi âm\n                  </Button>\n                </div>\n              )}\n              \n              {audioBlob && (\n                <div>\n                  <Volume2 className=\"h-8 w-8 text-green-500 mx-auto mb-2\" />\n                  <p className=\"text-green-600 mb-3\">Đã ghi âm thành công!</p>\n                  <audio controls src={userAnswer as string} className=\"mx-auto\" />\n                  {!isReadOnly && (\n                    <div className=\"mt-3\">\n                      <Button variant=\"outline\" onClick={() => {\n                        setAudioBlob(null);\n                        onAnswerChange('');\n                      }}>\n                        Ghi âm lại\n                      </Button>\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n        );\n\n      default:\n        return (\n          <div className=\"p-4 bg-gray-50 rounded-lg text-center text-gray-500\">\n            Loại câu hỏi không được hỗ trợ\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Question Header */}\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex-1\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n            {question.question}\n          </h3>\n          {question.imageUrl && (\n            <div className=\"mb-4\">\n              <img\n                src={question.imageUrl}\n                alt=\"Question illustration\"\n                className=\"max-w-full h-auto rounded-lg\"\n              />\n            </div>\n          )}\n        </div>\n        <Badge variant=\"outline\" className=\"ml-4\">\n          {question.points} điểm\n        </Badge>\n      </div>\n\n      {/* Question Content */}\n      {renderQuestionContent()}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AA8Be,SAAS,iBAAiB,EACvC,QAAQ,EACR,UAAU,EACV,cAAc,EACd,aAAa,KAAK,EAClB,oBAAoB,KAAK,EACH;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAExD,MAAM,uBAAuB,CAAC;QAC5B,IAAI,YAAY;QAChB,eAAe;IACjB;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,YAAY;QAChB,eAAe;IACjB;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,YAAY;QAChB,eAAe;IACjB;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,YAAY;QAChB,eAAe;IACjB;IAEA,MAAM,kBAAkB;QACtB,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,IAAI,MAAM,SAAS,QAAQ;YACzC,aAAa;YACb,MAAM,IAAI;YACV,MAAM,OAAO,GAAG,IAAM,aAAa;QACrC;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAK;YACvE,MAAM,gBAAgB,IAAI,cAAc;YACxC,MAAM,SAAqB,EAAE;YAE7B,cAAc,eAAe,GAAG,CAAC;gBAC/B,OAAO,IAAI,CAAC,MAAM,IAAI;YACxB;YAEA,cAAc,MAAM,GAAG;gBACrB,MAAM,OAAO,IAAI,KAAK,QAAQ;oBAAE,MAAM;gBAAY;gBAClD,aAAa;gBACb,eAAe,IAAI,eAAe,CAAC;YACrC;YAEA,cAAc,KAAK;YACnB,eAAe;YAEf,qCAAqC;YACrC,WAAW;gBACT,IAAI,cAAc,KAAK,KAAK,aAAa;oBACvC,cAAc,IAAI;oBAClB,eAAe;gBACjB;YACF,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,sBAAsB;QAC1B,eAAe;IACf,2CAA2C;IAC7C;IAEA,MAAM,wBAAwB;QAC5B,OAAQ,SAAS,IAAI;YACnB,KAAK,qHAAA,CAAA,eAAY,CAAC,eAAe;gBAC/B,qBACE,8OAAC;oBAAI,WAAU;8BACZ,SAAS,OAAO,EAAE,IAAI,CAAC,QAAQ,sBAC9B,8OAAC;4BAEC,WAAW,CAAC;;kBAEV,EAAE,aAAa,mBAAmB,iBAAiB;kBACnD,EAAE,eAAe,SACb,oBACE,WAAW,SAAS,aAAa,GAC/B,iCACA,6BACF,+BACF,qBAAqB,WAAW,SAAS,aAAa,GACpD,iCACA,wCACL;gBACH,CAAC;;8CAED,8OAAC;oCACC,MAAK;oCACL,MAAM,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;oCAC/B,OAAO;oCACP,SAAS,eAAe;oCACxB,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;oCACpD,UAAU;oCACV,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAChC,qBAAqB,WAAW,SAAS,aAAa,kBACrD,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAAU;;;;;;;2BA3B1C;;;;;;;;;;YAkCf,KAAK,qHAAA,CAAA,eAAY,CAAC,aAAa;gBAC7B,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,OAAO,cAAwB;4BAC/B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4BACjD,aAAY;4BACZ,UAAU;4BACV,WAAW,oBACP,eAAe,SAAS,aAAa,GACnC,iCACA,6BACF;;;;;;wBAGL,mCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAc;;;;;;8CAC9B,8OAAC;oCAAK,WAAU;8CAAiB,SAAS,aAAa;;;;;;;;;;;;;;;;;;YAMjE,KAAK,qHAAA,CAAA,eAAY,CAAC,UAAU;gBAC1B,qBACE,8OAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAQ;qBAAM,CAAC,GAAG,CAAC,CAAC,uBACpB,8OAAC;4BAEC,WAAW,CAAC;;kBAEV,EAAE,aAAa,mBAAmB,iBAAiB;kBACnD,EAAE,eAAe,SACb,oBACE,WAAW,SAAS,aAAa,GAC/B,iCACA,6BACF,+BACF,qBAAqB,WAAW,SAAS,aAAa,GACpD,iCACA,wCACL;gBACH,CAAC;;8CAED,8OAAC;oCACC,MAAK;oCACL,MAAM,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;oCAC/B,OAAO;oCACP,SAAS,eAAe;oCACxB,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,UAAU;oCACV,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAChC,qBAAqB,WAAW,SAAS,aAAa,kBACrD,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAAU;;;;;;;2BA3B1C;;;;;;;;;;YAkCf,KAAK,qHAAA,CAAA,eAAY,CAAC,KAAK;gBACrB,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,OAAO,cAAwB;4BAC/B,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4BAC3C,aAAY;4BACZ,UAAU;4BACV,MAAM;4BACN,WAAW,CAAC;;gBAEV,EAAE,aAAa,eAAe,GAAG;cACnC,CAAC;;;;;;sCAEH,8OAAC;4BAAI,WAAU;;gCAAwB;gCAC7B,CAAC,cAAwB,EAAE,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;;;;;;;wBAEvF,mCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAc;;;;;;8CAC9B,8OAAC;oCAAE,WAAU;8CAAsB,SAAS,aAAa;;;;;;;;;;;;;;;;;;YAMnE,KAAK,qHAAA,CAAA,eAAY,CAAC,cAAc;gBAC9B,qBACE,8OAAC;oBAAI,WAAU;;wBACZ,SAAS,QAAQ,kBAChB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;;4CAET,0BAAY,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;qEAAe,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAC5D,YAAY,cAAc;;;;;;;kDAE7B,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAK9C,8OAAC;4BAAI,WAAU;;gCACZ,CAAC,eAAe,CAAC,2BAChB,8OAAC;;sDACC,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAsB,UAAU;;8DAC/C,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;gCAMrC,6BACC,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAE,WAAU;sDAAoB;;;;;;sDACjC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAc,SAAS;sDAAqB;;;;;;;;;;;;gCAM/D,2BACC,8OAAC;;sDACC,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,8OAAC;4CAAM,QAAQ;4CAAC,KAAK;4CAAsB,WAAU;;;;;;wCACpD,CAAC,4BACA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS;oDACjC,aAAa;oDACb,eAAe;gDACjB;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWnB;gBACE,qBACE,8OAAC;oBAAI,WAAU;8BAAsD;;;;;;QAI3E;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,SAAS,QAAQ;;;;;;4BAEnB,SAAS,QAAQ,kBAChB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,KAAK,SAAS,QAAQ;oCACtB,KAAI;oCACJ,WAAU;;;;;;;;;;;;;;;;;kCAKlB,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAU,WAAU;;4BAChC,SAAS,MAAM;4BAAC;;;;;;;;;;;;;YAKpB;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 2597, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/app/tests/%5Bid%5D/preview/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { usePara<PERSON>, useRouter } from \"next/navigation\";\nimport { <PERSON><PERSON><PERSON>t, Clock, Users, Target, Play, Eye } from \"lucide-react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Breadcrumb } from \"@/components/ui/breadcrumb\";\nimport { LoadingPage } from \"@/components/ui/loading\";\nimport { ErrorPage } from \"@/components/ui/error\";\nimport Header from \"@/components/layout/Header\";\nimport QuestionRenderer from \"@/components/test/QuestionRenderer\";\n\ninterface TestData {\n  _id: string;\n  title: string;\n  description: string;\n  type: string;\n  level: string;\n  duration: number;\n  totalQuestions: number;\n  passingScore: number;\n  instructions: string;\n  questions: Array<{\n    _id: string;\n    type: string;\n    question: string;\n    options?: string[];\n    correctAnswer: string | string[];\n    points: number;\n    order: number;\n    explanation?: string;\n    audioUrl?: string;\n    passage?: string;\n  }>;\n  difficulty: string;\n  tags: string[];\n  isPublished: boolean;\n  createdBy?: {\n    name: string;\n  };\n}\n\ninterface TestPreviewPageProps {\n  params: {\n    id: string;\n  };\n}\n\nexport default function TestPreviewPage({ params }: TestPreviewPageProps) {\n  const router = useRouter();\n  const [test, setTest] = useState<TestData | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [currentPreviewQuestion, setCurrentPreviewQuestion] = useState(0);\n  const [previewAnswers, setPreviewAnswers] = useState<Record<string, any>>({});\n  const [testId, setTestId] = useState<string>('');\n\n  useEffect(() => {\n    const getParams = async () => {\n      const resolvedParams = await params;\n      setTestId(resolvedParams.id);\n    };\n    getParams();\n  }, [params]);\n\n  useEffect(() => {\n    if (testId) {\n      loadTest();\n    }\n  }, [testId]);\n\n  const loadTest = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch(`/api/tests/${testId}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setTest(data.data);\n      } else {\n        setError(data.message || 'Không thể tải bài kiểm tra');\n      }\n    } catch (error) {\n      console.error('Load test error:', error);\n      setError('Lỗi kết nối. Vui lòng thử lại.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handlePreviewAnswerChange = (questionId: string, answer: any) => {\n    setPreviewAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  const getTypeLabel = (type: string) => {\n    const typeLabels: Record<string, string> = {\n      'vocabulary': 'Từ vựng',\n      'grammar': 'Ngữ pháp',\n      'listening': 'Nghe',\n      'reading': 'Đọc',\n      'writing': 'Viết',\n      'speaking': 'Nói',\n      'comprehensive': 'Tổng hợp'\n    };\n    return typeLabels[type] || type;\n  };\n\n  const getLevelLabel = (level: string) => {\n    const levelLabels: Record<string, string> = {\n      'beginner': 'Cơ bản',\n      'intermediate': 'Trung cấp',\n      'advanced': 'Nâng cao'\n    };\n    return levelLabels[level] || level;\n  };\n\n  const getDifficultyColor = (difficulty: string) => {\n    switch (difficulty) {\n      case 'easy': return 'bg-green-100 text-green-800';\n      case 'medium': return 'bg-yellow-100 text-yellow-800';\n      case 'hard': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getDifficultyLabel = (difficulty: string) => {\n    const difficultyLabels: Record<string, string> = {\n      'easy': 'Dễ',\n      'medium': 'Trung bình',\n      'hard': 'Khó'\n    };\n    return difficultyLabels[difficulty] || difficulty;\n  };\n\n  const breadcrumbItems = [\n    { label: \"Luyện thi\", href: \"/tests\" },\n    { label: test?.title || \"Bài kiểm tra\", href: `/tests/${testId}` },\n    { label: \"Xem trước\", current: true }\n  ];\n\n  if (isLoading) {\n    return <LoadingPage message=\"Đang tải bài kiểm tra...\" />;\n  }\n\n  if (error || !test) {\n    return <ErrorPage title=\"Không thể tải bài kiểm tra\" message={error || \"Bài kiểm tra không tồn tại\"} />;\n  }\n\n  // Get first 2-3 questions for preview\n  const previewQuestions = test.questions.slice(0, Math.min(3, test.questions.length));\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <Breadcrumb items={breadcrumbItems} className=\"mb-6\" />\n\n        {/* Test Header */}\n        <div className=\"bg-white rounded-lg shadow-sm p-6 mb-6\">\n          <div className=\"flex items-start justify-between mb-4\">\n            <div className=\"flex-1\">\n              <div className=\"flex items-center gap-3 mb-2\">\n                <h1 className=\"text-2xl font-bold text-gray-900\">{test.title}</h1>\n                <Badge className={getDifficultyColor(test.difficulty)}>\n                  {getDifficultyLabel(test.difficulty)}\n                </Badge>\n              </div>\n              <p className=\"text-gray-600 mb-4\">{test.description}</p>\n              \n              <div className=\"flex flex-wrap gap-2\">\n                <Badge variant=\"outline\">{getTypeLabel(test.type)}</Badge>\n                <Badge variant=\"outline\">{getLevelLabel(test.level)}</Badge>\n                {test.tags.map((tag, index) => (\n                  <Badge key={index} variant=\"secondary\">{tag}</Badge>\n                ))}\n              </div>\n            </div>\n\n            <Button\n              variant=\"outline\"\n              onClick={() => router.back()}\n              className=\"ml-4\"\n            >\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Quay lại\n            </Button>\n          </div>\n\n          {/* Test Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div className=\"flex items-center gap-3 p-3 bg-blue-50 rounded-lg\">\n              <Clock className=\"h-5 w-5 text-blue-600\" />\n              <div>\n                <div className=\"text-sm text-blue-600\">Thời gian</div>\n                <div className=\"font-semibold text-blue-900\">{test.duration} phút</div>\n              </div>\n            </div>\n            <div className=\"flex items-center gap-3 p-3 bg-green-50 rounded-lg\">\n              <Target className=\"h-5 w-5 text-green-600\" />\n              <div>\n                <div className=\"text-sm text-green-600\">Số câu hỏi</div>\n                <div className=\"font-semibold text-green-900\">{test.totalQuestions}</div>\n              </div>\n            </div>\n            <div className=\"flex items-center gap-3 p-3 bg-purple-50 rounded-lg\">\n              <Users className=\"h-5 w-5 text-purple-600\" />\n              <div>\n                <div className=\"text-sm text-purple-600\">Điểm đạt</div>\n                <div className=\"font-semibold text-purple-900\">{test.passingScore}%</div>\n              </div>\n            </div>\n            <div className=\"flex items-center gap-3 p-3 bg-orange-50 rounded-lg\">\n              <Eye className=\"h-5 w-5 text-orange-600\" />\n              <div>\n                <div className=\"text-sm text-orange-600\">Xem trước</div>\n                <div className=\"font-semibold text-orange-900\">{previewQuestions.length} câu</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Instructions */}\n        <Card className=\"mb-6\">\n          <CardHeader>\n            <CardTitle>Hướng dẫn làm bài</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"prose max-w-none\">\n              <div dangerouslySetInnerHTML={{ __html: test.instructions }} />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Preview Questions */}\n        <Card className=\"mb-6\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Eye className=\"h-5 w-5\" />\n              Câu hỏi mẫu ({previewQuestions.length} câu đầu)\n            </CardTitle>\n            <CardDescription>\n              Đây là một số câu hỏi mẫu để bạn làm quen với định dạng bài thi\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-8\">\n              {previewQuestions.map((question, index) => (\n                <div key={question._id} className=\"border-b border-gray-200 pb-6 last:border-b-0\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h3 className=\"text-lg font-medium\">Câu {index + 1}</h3>\n                    <Badge variant=\"outline\">{question.points} điểm</Badge>\n                  </div>\n                  \n                  <QuestionRenderer\n                    question={question}\n                    userAnswer={previewAnswers[question._id]}\n                    onAnswerChange={(answer) => handlePreviewAnswerChange(question._id, answer)}\n                    isReadOnly={false}\n                    showCorrectAnswer={false}\n                  />\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Call to Action */}\n        <Card className=\"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200\">\n          <CardContent className=\"p-6\">\n            <div className=\"text-center\">\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                Sẵn sàng làm bài kiểm tra?\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                Bạn đã xem qua các câu hỏi mẫu. Hãy bắt đầu làm bài kiểm tra chính thức để đánh giá trình độ của mình.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Button\n                  size=\"lg\"\n                  onClick={() => router.push(`/tests/${testId}`)}\n                  className=\"bg-blue-600 hover:bg-blue-700\"\n                >\n                  <Play className=\"h-5 w-5 mr-2\" />\n                  Bắt đầu làm bài\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  size=\"lg\"\n                  onClick={() => router.push('/tests')}\n                >\n                  Xem thêm bài kiểm tra khác\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAkDe,SAAS,gBAAgB,EAAE,MAAM,EAAwB;IACtE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC3E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,MAAM,iBAAiB,MAAM;YAC7B,UAAU,eAAe,EAAE;QAC7B;QACA;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,WAAW;QACf,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ;YACnD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,QAAQ,KAAK,IAAI;YACnB,OAAO;gBACL,SAAS,KAAK,OAAO,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,4BAA4B,CAAC,YAAoB;QACrD,kBAAkB,CAAA,OAAQ,CAAC;gBACzB,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,aAAqC;YACzC,cAAc;YACd,WAAW;YACX,aAAa;YACb,WAAW;YACX,WAAW;YACX,YAAY;YACZ,iBAAiB;QACnB;QACA,OAAO,UAAU,CAAC,KAAK,IAAI;IAC7B;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAsC;YAC1C,YAAY;YACZ,gBAAgB;YAChB,YAAY;QACd;QACA,OAAO,WAAW,CAAC,MAAM,IAAI;IAC/B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,mBAA2C;YAC/C,QAAQ;YACR,UAAU;YACV,QAAQ;QACV;QACA,OAAO,gBAAgB,CAAC,WAAW,IAAI;IACzC;IAEA,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAa,MAAM;QAAS;QACrC;YAAE,OAAO,MAAM,SAAS;YAAgB,MAAM,CAAC,OAAO,EAAE,QAAQ;QAAC;QACjE;YAAE,OAAO;YAAa,SAAS;QAAK;KACrC;IAED,IAAI,WAAW;QACb,qBAAO,8OAAC,mIAAA,CAAA,cAAW;YAAC,SAAQ;;;;;;IAC9B;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBAAO,8OAAC,iIAAA,CAAA,YAAS;YAAC,OAAM;YAA6B,SAAS,SAAS;;;;;;IACzE;IAEA,sCAAsC;IACtC,MAAM,mBAAmB,KAAK,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,SAAS,CAAC,MAAM;IAElF,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,aAAU;wBAAC,OAAO;wBAAiB,WAAU;;;;;;kCAG9C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoC,KAAK,KAAK;;;;;;kEAC5D,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAW,mBAAmB,KAAK,UAAU;kEACjD,mBAAmB,KAAK,UAAU;;;;;;;;;;;;0DAGvC,8OAAC;gDAAE,WAAU;0DAAsB,KAAK,WAAW;;;;;;0DAEnD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW,aAAa,KAAK,IAAI;;;;;;kEAChD,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW,cAAc,KAAK,KAAK;;;;;;oDACjD,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC,iIAAA,CAAA,QAAK;4DAAa,SAAQ;sEAAa;2DAA5B;;;;;;;;;;;;;;;;;kDAKlB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAM1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,8OAAC;wDAAI,WAAU;;4DAA+B,KAAK,QAAQ;4DAAC;;;;;;;;;;;;;;;;;;;kDAGhE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAyB;;;;;;kEACxC,8OAAC;wDAAI,WAAU;kEAAgC,KAAK,cAAc;;;;;;;;;;;;;;;;;;kDAGtE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;kEACzC,8OAAC;wDAAI,WAAU;;4DAAiC,KAAK,YAAY;4DAAC;;;;;;;;;;;;;;;;;;;kDAGtE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;kEACzC,8OAAC;wDAAI,WAAU;;4DAAiC,iBAAiB,MAAM;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhF,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,yBAAyB;4CAAE,QAAQ,KAAK,YAAY;wCAAC;;;;;;;;;;;;;;;;;;;;;;kCAMhE,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;4CACb,iBAAiB,MAAM;4CAAC;;;;;;;kDAExC,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,UAAU,sBAC/B,8OAAC;4CAAuB,WAAU;;8DAChC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;;gEAAsB;gEAAK,QAAQ;;;;;;;sEACjD,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;;gEAAW,SAAS,MAAM;gEAAC;;;;;;;;;;;;;8DAG5C,8OAAC,8IAAA,CAAA,UAAgB;oDACf,UAAU;oDACV,YAAY,cAAc,CAAC,SAAS,GAAG,CAAC;oDACxC,gBAAgB,CAAC,SAAW,0BAA0B,SAAS,GAAG,EAAE;oDACpE,YAAY;oDACZ,mBAAmB;;;;;;;2CAXb,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;kCAoB9B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ;gDAC7C,WAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC;0DAC5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}