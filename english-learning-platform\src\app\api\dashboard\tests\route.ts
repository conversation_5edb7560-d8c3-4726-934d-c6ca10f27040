import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import Test from '@/models/Test';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return authResult.error;
    }

    const { user } = authResult;
    await connectDB();

    // Get user's recent test results
    const userDoc = await User.findById(user.userId);
    
    // For now, we'll create some sample test results
    // In a real app, you'd have a TestResult model and query actual results
    const recentTests = [
      {
        _id: '1',
        title: 'Kiể<PERSON> tra từ vựng cơ bản',
        type: 'vocabulary',
        score: 85,
        maxScore: 100,
        completedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
        duration: 15
      },
      {
        _id: '2',
        title: '<PERSON><PERSON><PERSON><PERSON> nghe IELTS Part 1',
        type: 'listening',
        score: 78,
        maxScore: 100,
        completedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(), // 4 days ago
        duration: 30
      },
      {
        _id: '3',
        title: 'Ngữ pháp thì hiện tại',
        type: 'grammar',
        score: 92,
        maxScore: 100,
        completedAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(), // 6 days ago
        duration: 20
      }
    ];

    return NextResponse.json({
      success: true,
      data: recentTests
    });

  } catch (error) {
    console.error('Get dashboard tests error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi hệ thống, vui lòng thử lại sau',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
