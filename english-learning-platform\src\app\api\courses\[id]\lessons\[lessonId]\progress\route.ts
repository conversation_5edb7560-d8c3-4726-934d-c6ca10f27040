import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Course from '@/models/Course';
import Enrollment from '@/models/Enrollment';
import { getAuthUser } from '@/lib/auth';

interface RouteParams {
  params: {
    id: string;
    lessonId: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id: courseId, lessonId } = await params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find enrollment
    const enrollment = await Enrollment.findOne({
      studentId: user.userId,
      courseId: courseId
    });

    if (!enrollment) {
      return NextResponse.json({
        success: false,
        message: '<PERSON><PERSON>n chư<PERSON> đăng ký khóa học này'
      }, { status: 403 });
    }

    // Find lesson progress
    const lessonProgress = enrollment.lessonProgress.find(
      (progress: any) => progress.lessonId === lessonId
    );

    if (!lessonProgress) {
      // Return default progress if not found
      return NextResponse.json({
        success: true,
        data: {
          lessonId,
          completedAt: null,
          timeSpent: 0,
          videoProgress: 0,
          isCompleted: false,
          bookmarks: [],
          notes: ''
        }
      });
    }

    return NextResponse.json({
      success: true,
      data: lessonProgress
    });

  } catch (error) {
    console.error('Get lesson progress error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi tải tiến độ bài học',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id: courseId, lessonId } = await params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    const body = await request.json();
    const { videoProgress, timeSpent, notes, bookmarks, isCompleted } = body;

    // Find enrollment
    const enrollment = await Enrollment.findOne({
      studentId: user.userId,
      courseId: courseId
    });

    if (!enrollment) {
      return NextResponse.json({
        success: false,
        message: 'Bạn chưa đăng ký khóa học này'
      }, { status: 403 });
    }

    // Find existing lesson progress
    const existingProgressIndex = enrollment.lessonProgress.findIndex(
      (progress: any) => progress.lessonId === lessonId
    );

    const progressData = {
      lessonId,
      timeSpent: timeSpent || 0,
      videoProgress: videoProgress || 0,
      notes: notes || '',
      bookmarks: bookmarks || [],
      isCompleted: isCompleted || false,
      completedAt: isCompleted ? new Date() : null
    };

    if (existingProgressIndex >= 0) {
      // Update existing progress
      enrollment.lessonProgress[existingProgressIndex] = {
        ...enrollment.lessonProgress[existingProgressIndex],
        ...progressData
      };
    } else {
      // Add new progress
      enrollment.lessonProgress.push(progressData);
    }

    // Update completed lessons array
    if (isCompleted && !enrollment.completedLessons.includes(lessonId)) {
      enrollment.completedLessons.push(lessonId);
    } else if (!isCompleted && enrollment.completedLessons.includes(lessonId)) {
      enrollment.completedLessons = enrollment.completedLessons.filter(
        (id: string) => id !== lessonId
      );
    }

    // Calculate overall progress
    const course = await Course.findById(courseId);
    if (course) {
      const totalLessons = course.curriculum.length;
      const completedCount = enrollment.completedLessons.length;
      enrollment.progress = Math.round((completedCount / totalLessons) * 100);
    }

    // Update last accessed time
    enrollment.lastAccessedAt = new Date();
    enrollment.currentLesson = lessonId;

    // Update total time spent
    enrollment.totalTimeSpent += timeSpent || 0;

    await enrollment.save();

    return NextResponse.json({
      success: true,
      message: 'Cập nhật tiến độ thành công',
      data: progressData
    });

  } catch (error) {
    console.error('Update lesson progress error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi cập nhật tiến độ',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id: courseId, lessonId } = await params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    const body = await request.json();
    const { timestamp, note } = body;

    // Find enrollment
    const enrollment = await Enrollment.findOne({
      studentId: user.userId,
      courseId: courseId
    });

    if (!enrollment) {
      return NextResponse.json({
        success: false,
        message: 'Bạn chưa đăng ký khóa học này'
      }, { status: 403 });
    }

    // Find lesson progress
    let lessonProgress = enrollment.lessonProgress.find(
      (progress: any) => progress.lessonId === lessonId
    );

    if (!lessonProgress) {
      // Create new lesson progress
      lessonProgress = {
        lessonId,
        timeSpent: 0,
        videoProgress: 0,
        isCompleted: false,
        bookmarks: [],
        notes: ''
      };
      enrollment.lessonProgress.push(lessonProgress);
    }

    // Add bookmark
    const bookmark = {
      timestamp,
      note,
      createdAt: new Date()
    };

    lessonProgress.bookmarks = lessonProgress.bookmarks || [];
    lessonProgress.bookmarks.push(bookmark);

    await enrollment.save();

    return NextResponse.json({
      success: true,
      message: 'Thêm bookmark thành công',
      data: bookmark
    });

  } catch (error) {
    console.error('Add bookmark error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi thêm bookmark',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
