"use client";

import { useState } from "react";
import Link from "next/link";
import { BookO<PERSON>, Clock, TrendingUp, Award, Play, BarChart3, Calendar, Target } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import Header from "@/components/layout/Header";
import { formatPrice } from "@/lib/utils";

// Mock data
const mockUser = {
  name: "<PERSON><PERSON><PERSON><PERSON>n <PERSON>",
  email: "<EMAIL>",
  avatar: "",
  role: "student",
  joinDate: "2024-01-15",
  totalCourses: 3,
  completedCourses: 1,
  totalHours: 45,
  averageScore: 85
};

const mockEnrolledCourses = [
  {
    id: "1",
    title: "<PERSON><PERSON><PERSON><PERSON> cho <PERSON>ờ<PERSON>",
    thumbnail: "/api/placeholder/300/200",
    progress: 75,
    totalLessons: 25,
    completedLessons: 19,
    lastAccessed: "2024-01-20",
    instructor: "Cô Minh Anh",
    nextLesson: "Bài 20: Thì hiện tại hoàn thành"
  },
  {
    id: "2", 
    title: "Luyện Nghe IELTS 6.5+",
    thumbnail: "/api/placeholder/300/200",
    progress: 40,
    totalLessons: 20,
    completedLessons: 8,
    lastAccessed: "2024-01-18",
    instructor: "Thầy Đức Minh",
    nextLesson: "Bài 9: Listening Part 2 - Maps"
  },
  {
    id: "3",
    title: "Tiếng Anh Giao Tiếp Thực Tế", 
    thumbnail: "/api/placeholder/300/200",
    progress: 10,
    totalLessons: 28,
    completedLessons: 3,
    lastAccessed: "2024-01-15",
    instructor: "Cô Thu Hà",
    nextLesson: "Bài 4: Giới thiệu bản thân"
  }
];

const mockRecentTests = [
  {
    id: "1",
    title: "Kiểm tra từ vựng cơ bản",
    type: "vocabulary",
    score: 85,
    maxScore: 100,
    completedAt: "2024-01-19",
    duration: 15
  },
  {
    id: "2", 
    title: "Luyện nghe IELTS Part 1",
    type: "listening",
    score: 78,
    maxScore: 100,
    completedAt: "2024-01-17",
    duration: 30
  },
  {
    id: "3",
    title: "Ngữ pháp thì hiện tại",
    type: "grammar", 
    score: 92,
    maxScore: 100,
    completedAt: "2024-01-15",
    duration: 20
  }
];

const mockUpcomingLessons = [
  {
    courseTitle: "Tiếng Anh Cơ Bản",
    lessonTitle: "Thì hiện tại hoàn thành",
    scheduledTime: "2024-01-21T10:00:00",
    duration: 45
  },
  {
    courseTitle: "Luyện Nghe IELTS",
    lessonTitle: "Listening Part 2 - Maps", 
    scheduledTime: "2024-01-22T14:00:00",
    duration: 60
  }
];

export default function StudentDashboard() {
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Chào mừng trở lại, {mockUser.name}!
          </h1>
          <p className="text-lg text-gray-600">
            Hãy tiếp tục hành trình học tiếng Anh của bạn
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Khóa học đã đăng ký</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockUser.totalCourses}</div>
              <p className="text-xs text-muted-foreground">
                {mockUser.completedCourses} khóa học đã hoàn thành
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tổng thời gian học</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockUser.totalHours}h</div>
              <p className="text-xs text-muted-foreground">
                +5h tuần này
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Điểm trung bình</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockUser.averageScore}%</div>
              <p className="text-xs text-muted-foreground">
                +2% so với tháng trước
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Chứng chỉ</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockUser.completedCourses}</div>
              <p className="text-xs text-muted-foreground">
                Chứng chỉ đã đạt được
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Tổng quan</TabsTrigger>
            <TabsTrigger value="courses">Khóa học</TabsTrigger>
            <TabsTrigger value="tests">Bài kiểm tra</TabsTrigger>
            <TabsTrigger value="schedule">Lịch học</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Continue Learning */}
              <Card>
                <CardHeader>
                  <CardTitle>Tiếp tục học</CardTitle>
                  <CardDescription>
                    Khóa học bạn đang theo dõi
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {mockEnrolledCourses.slice(0, 2).map(course => (
                    <div key={course.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <div className="w-16 h-16 bg-gray-200 rounded-lg flex-shrink-0"></div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-sm truncate">{course.title}</h3>
                        <p className="text-xs text-gray-500 mb-2">{course.instructor}</p>
                        <div className="flex items-center justify-between">
                          <Progress value={course.progress} className="flex-1 mr-2" />
                          <span className="text-xs text-gray-500">{course.progress}%</span>
                        </div>
                      </div>
                      <Button size="sm">
                        <Play className="h-4 w-4 mr-1" />
                        Tiếp tục
                      </Button>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Recent Test Results */}
              <Card>
                <CardHeader>
                  <CardTitle>Kết quả bài kiểm tra gần đây</CardTitle>
                  <CardDescription>
                    Thành tích của bạn trong các bài kiểm tra
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {mockRecentTests.slice(0, 3).map(test => (
                    <div key={test.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h3 className="font-medium text-sm">{test.title}</h3>
                        <p className="text-xs text-gray-500">{test.completedAt}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-blue-600">
                          {test.score}/{test.maxScore}
                        </div>
                        <Badge variant={test.score >= 80 ? "default" : test.score >= 60 ? "secondary" : "destructive"}>
                          {test.score >= 80 ? "Xuất sắc" : test.score >= 60 ? "Khá" : "Cần cải thiện"}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Courses Tab */}
          <TabsContent value="courses" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {mockEnrolledCourses.map(course => (
                <Card key={course.id}>
                  <div className="aspect-video bg-gray-200 rounded-t-lg"></div>
                  <CardHeader>
                    <CardTitle className="text-lg">{course.title}</CardTitle>
                    <CardDescription>Giảng viên: {course.instructor}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Tiến độ</span>
                        <span>{course.completedLessons}/{course.totalLessons} bài</span>
                      </div>
                      <Progress value={course.progress} />
                    </div>
                    
                    <div className="text-sm text-gray-600">
                      <p><strong>Bài tiếp theo:</strong> {course.nextLesson}</p>
                      <p><strong>Lần truy cập cuối:</strong> {course.lastAccessed}</p>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button className="flex-1">
                        <Play className="h-4 w-4 mr-2" />
                        Tiếp tục học
                      </Button>
                      <Button variant="outline">
                        <BarChart3 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Tests Tab */}
          <TabsContent value="tests" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Lịch sử bài kiểm tra</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {mockRecentTests.map(test => (
                    <div key={test.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-medium">{test.title}</h3>
                        <p className="text-sm text-gray-500">
                          {test.completedAt} • {test.duration} phút
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-xl font-bold text-blue-600">
                          {test.score}%
                        </div>
                        <Badge variant={test.score >= 80 ? "default" : test.score >= 60 ? "secondary" : "destructive"}>
                          {test.score >= 80 ? "Xuất sắc" : test.score >= 60 ? "Khá" : "Cần cải thiện"}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Bài kiểm tra được đề xuất</CardTitle>
                  <CardDescription>
                    Dựa trên tiến độ học tập của bạn
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Kiểm tra từ vựng Unit 5</h3>
                    <p className="text-sm text-gray-600 mb-3">
                      Kiểm tra kiến thức từ vựng bạn đã học trong Unit 5
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-500">
                        <Clock className="h-4 w-4 inline mr-1" />
                        20 phút
                      </div>
                      <Button size="sm">Bắt đầu</Button>
                    </div>
                  </div>
                  
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Luyện nghe cơ bản</h3>
                    <p className="text-sm text-gray-600 mb-3">
                      Thực hành kỹ năng nghe với các đoạn hội thoại đơn giản
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-500">
                        <Clock className="h-4 w-4 inline mr-1" />
                        15 phút
                      </div>
                      <Button size="sm">Bắt đầu</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Schedule Tab */}
          <TabsContent value="schedule" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Lịch học sắp tới</CardTitle>
                <CardDescription>
                  Các bài học và hoạt động được lên lịch
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {mockUpcomingLessons.map((lesson, index) => (
                  <div key={index} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <div className="flex-shrink-0">
                      <Calendar className="h-8 w-8 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium">{lesson.lessonTitle}</h3>
                      <p className="text-sm text-gray-600">{lesson.courseTitle}</p>
                      <p className="text-sm text-gray-500">
                        {new Date(lesson.scheduledTime).toLocaleDateString('vi-VN')} • 
                        {new Date(lesson.scheduledTime).toLocaleTimeString('vi-VN', { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })} • 
                        {lesson.duration} phút
                      </p>
                    </div>
                    <Button variant="outline">
                      <Target className="h-4 w-4 mr-2" />
                      Đặt nhắc nhở
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
