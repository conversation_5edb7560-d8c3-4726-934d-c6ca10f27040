"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { BookO<PERSON>, Clock, TrendingUp, Award, Play, BarChart3, Calendar, Target } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import Header from "@/components/layout/Header";
import { formatPrice } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";

// Interfaces for API data
interface DashboardStats {
  totalCourses: number;
  completedCourses: number;
  totalHours: number;
  averageScore: number;
  totalTests: number;
  completedTests: number;
}

interface EnrolledCourse {
  _id: string;
  title: string;
  thumbnail?: string;
  progress: number;
  totalLessons: number;
  completedLessons: number;
  lastAccessed: string;
  instructor: {
    name: string;
  };
  nextLesson?: string;
}

interface RecentTest {
  _id: string;
  title: string;
  type: string;
  score: number;
  maxScore: number;
  completedAt: string;
  duration: number;
}



const mockUpcomingLessons = [
  {
    courseTitle: "Tiếng Anh Cơ Bản",
    lessonTitle: "Thì hiện tại hoàn thành",
    scheduledTime: "2024-01-21T10:00:00",
    duration: 45
  },
  {
    courseTitle: "Luyện Nghe IELTS",
    lessonTitle: "Listening Part 2 - Maps", 
    scheduledTime: "2024-01-22T14:00:00",
    duration: 60
  }
];

export default function StudentDashboard() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [enrolledCourses, setEnrolledCourses] = useState<EnrolledCourse[]>([]);
  const [recentTests, setRecentTests] = useState<RecentTest[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadDashboardData();
    }
  }, [user]);

  const loadDashboardData = async () => {
    setIsLoading(true);
    try {
      // Load dashboard stats
      const statsResponse = await fetch('/api/dashboard/stats');
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        if (statsData.success) {
          setDashboardStats(statsData.data);
        }
      }

      // Load enrolled courses
      const coursesResponse = await fetch('/api/dashboard/courses');
      if (coursesResponse.ok) {
        const coursesData = await coursesResponse.json();
        if (coursesData.success) {
          setEnrolledCourses(coursesData.data);
        }
      }

      // Load recent tests
      const testsResponse = await fetch('/api/dashboard/tests');
      if (testsResponse.ok) {
        const testsData = await testsResponse.json();
        if (testsData.success) {
          setRecentTests(testsData.data);
        }
      }
    } catch (error) {
      console.error('Load dashboard data error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Đang tải dữ liệu dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Chào mừng trở lại, {user?.name || 'Học viên'}!
          </h1>
          <p className="text-lg text-gray-600">
            Hãy tiếp tục hành trình học tiếng Anh của bạn
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Khóa học đã đăng ký</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardStats?.totalCourses || 0}</div>
              <p className="text-xs text-muted-foreground">
                {dashboardStats?.completedCourses || 0} khóa học đã hoàn thành
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tổng thời gian học</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardStats?.totalHours || 0}h</div>
              <p className="text-xs text-muted-foreground">
                +5h tuần này
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Điểm trung bình</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardStats?.averageScore || 0}%</div>
              <p className="text-xs text-muted-foreground">
                +2% so với tháng trước
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Chứng chỉ</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardStats?.completedTests || 0}</div>
              <p className="text-xs text-muted-foreground">
                Bài kiểm tra đã hoàn thành
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Tổng quan</TabsTrigger>
            <TabsTrigger value="courses">Khóa học</TabsTrigger>
            <TabsTrigger value="tests">Bài kiểm tra</TabsTrigger>
            <TabsTrigger value="schedule">Lịch học</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Continue Learning */}
              <Card>
                <CardHeader>
                  <CardTitle>Tiếp tục học</CardTitle>
                  <CardDescription>
                    Khóa học bạn đang theo dõi
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {enrolledCourses.slice(0, 2).map((course: EnrolledCourse) => (
                    <div key={course._id} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <div className="w-16 h-16 bg-gray-200 rounded-lg flex-shrink-0"></div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-sm truncate">{course.title}</h3>
                        <p className="text-xs text-gray-500 mb-2">{course.instructor.name}</p>
                        <div className="flex items-center justify-between">
                          <Progress value={course.progress} className="flex-1 mr-2" />
                          <span className="text-xs text-gray-500">{course.progress}%</span>
                        </div>
                      </div>
                      <Button size="sm">
                        <Play className="h-4 w-4 mr-1" />
                        Tiếp tục
                      </Button>
                    </div>
                  ))}
                  {enrolledCourses.length === 0 && (
                    <p className="text-center text-gray-500 py-4">Chưa có khóa học nào</p>
                  )}
                </CardContent>
              </Card>

              {/* Recent Test Results */}
              <Card>
                <CardHeader>
                  <CardTitle>Kết quả bài kiểm tra gần đây</CardTitle>
                  <CardDescription>
                    Thành tích của bạn trong các bài kiểm tra
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {recentTests.slice(0, 3).map((test: RecentTest) => (
                    <div key={test._id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h3 className="font-medium text-sm">{test.title}</h3>
                        <p className="text-xs text-gray-500">{new Date(test.completedAt).toLocaleDateString('vi-VN')}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-blue-600">
                          {test.score}/{test.maxScore}
                        </div>
                        <Badge variant={test.score >= 80 ? "default" : test.score >= 60 ? "secondary" : "destructive"}>
                          {test.score >= 80 ? "Xuất sắc" : test.score >= 60 ? "Khá" : "Cần cải thiện"}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  {recentTests.length === 0 && (
                    <p className="text-center text-gray-500 py-4">Chưa có bài kiểm tra nào</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Courses Tab */}
          <TabsContent value="courses" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {enrolledCourses.map((course: EnrolledCourse) => (
                <Card key={course._id}>
                  <div className="aspect-video bg-gray-200 rounded-t-lg"></div>
                  <CardHeader>
                    <CardTitle className="text-lg">{course.title}</CardTitle>
                    <CardDescription>Giảng viên: {course.instructor.name}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Tiến độ</span>
                        <span>{course.completedLessons}/{course.totalLessons} bài</span>
                      </div>
                      <Progress value={course.progress} />
                    </div>

                    <div className="text-sm text-gray-600">
                      <p><strong>Bài tiếp theo:</strong> {course.nextLesson || 'Chưa có'}</p>
                      <p><strong>Lần truy cập cuối:</strong> {new Date(course.lastAccessed).toLocaleDateString('vi-VN')}</p>
                    </div>

                    <div className="flex gap-2">
                      <Button className="flex-1">
                        <Play className="h-4 w-4 mr-2" />
                        Tiếp tục học
                      </Button>
                      <Button variant="outline">
                        <BarChart3 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
              {enrolledCourses.length === 0 && (
                <div className="col-span-full text-center py-12">
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Chưa có khóa học nào</p>
                  <Button className="mt-4" asChild>
                    <Link href="/courses">Khám phá khóa học</Link>
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Tests Tab */}
          <TabsContent value="tests" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Lịch sử bài kiểm tra</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {recentTests.map((test: RecentTest) => (
                    <div key={test._id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-medium">{test.title}</h3>
                        <p className="text-sm text-gray-500">
                          {new Date(test.completedAt).toLocaleDateString('vi-VN')} • {test.duration} phút
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-xl font-bold text-blue-600">
                          {Math.round((test.score / test.maxScore) * 100)}%
                        </div>
                        <Badge variant={test.score >= 80 ? "default" : test.score >= 60 ? "secondary" : "destructive"}>
                          {test.score >= 80 ? "Xuất sắc" : test.score >= 60 ? "Khá" : "Cần cải thiện"}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  {recentTests.length === 0 && (
                    <p className="text-center text-gray-500 py-4">Chưa có bài kiểm tra nào</p>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Bài kiểm tra được đề xuất</CardTitle>
                  <CardDescription>
                    Dựa trên tiến độ học tập của bạn
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Kiểm tra từ vựng Unit 5</h3>
                    <p className="text-sm text-gray-600 mb-3">
                      Kiểm tra kiến thức từ vựng bạn đã học trong Unit 5
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-500">
                        <Clock className="h-4 w-4 inline mr-1" />
                        20 phút
                      </div>
                      <Button size="sm">Bắt đầu</Button>
                    </div>
                  </div>
                  
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Luyện nghe cơ bản</h3>
                    <p className="text-sm text-gray-600 mb-3">
                      Thực hành kỹ năng nghe với các đoạn hội thoại đơn giản
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-500">
                        <Clock className="h-4 w-4 inline mr-1" />
                        15 phút
                      </div>
                      <Button size="sm">Bắt đầu</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Schedule Tab */}
          <TabsContent value="schedule" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Lịch học sắp tới</CardTitle>
                <CardDescription>
                  Các bài học và hoạt động được lên lịch
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {mockUpcomingLessons.map((lesson, index) => (
                  <div key={index} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <div className="flex-shrink-0">
                      <Calendar className="h-8 w-8 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium">{lesson.lessonTitle}</h3>
                      <p className="text-sm text-gray-600">{lesson.courseTitle}</p>
                      <p className="text-sm text-gray-500">
                        {new Date(lesson.scheduledTime).toLocaleDateString('vi-VN')} • 
                        {new Date(lesson.scheduledTime).toLocaleTimeString('vi-VN', { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })} • 
                        {lesson.duration} phút
                      </p>
                    </div>
                    <Button variant="outline">
                      <Target className="h-4 w-4 mr-2" />
                      Đặt nhắc nhở
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
