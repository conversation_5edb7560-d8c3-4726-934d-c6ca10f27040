import { Users, Target, Award, BookOpen, Heart, Globe, Zap, Shield } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Header from "@/components/layout/Header";

const teamMembers = [
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    role: "Founder & CEO",
    bio: "10+ năm kinh nghiệm trong lĩnh vực giáo dục tiếng <PERSON>h. Tốt nghiệp Thạc sĩ <PERSON>ôn ngữ học tại Đại học Cambridge.",
    avatar: "/api/placeholder/150/150",
    specialties: ["IELTS", "TOEFL", "Business English"]
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    role: "Head of Curriculum",
    bio: "Chuyên gia phát triển chương trình học với 8 năm kinh nghiệm. Từng làm việc tại British Council.",
    avatar: "/api/placeholder/150/150",
    specialties: ["Curriculum Design", "Assessment", "Teacher Training"]
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Lead Teacher",
    bio: "Giáo viên tiếng Anh xuất sắc với chứng chỉ CELTA và DELTA. Chuyên về giao tiếp và phát âm.",
    avatar: "/api/placeholder/150/150",
    specialties: ["Speaking", "Pronunciation", "Communication"]
  },
  {
    name: "Phạm Văn Nam",
    role: "Technology Director",
    bio: "Chuyên gia công nghệ giáo dục với 12 năm kinh nghiệm phát triển các nền tảng học trực tuyến.",
    avatar: "/api/placeholder/150/150",
    specialties: ["EdTech", "AI in Education", "Platform Development"]
  }
];

const values = [
  {
    icon: Target,
    title: "Chất lượng hàng đầu",
    description: "Cam kết mang đến những khóa học chất lượng cao nhất với phương pháp giảng dạy hiện đại và hiệu quả."
  },
  {
    icon: Heart,
    title: "Tận tâm với học viên",
    description: "Đặt học viên làm trung tâm, luôn lắng nghe và hỗ trợ tối đa để giúp mọi người đạt được mục tiêu."
  },
  {
    icon: Zap,
    title: "Đổi mới sáng tạo",
    description: "Không ngừng cải tiến và ứng dụng công nghệ mới để tạo ra trải nghiệm học tập tốt nhất."
  },
  {
    icon: Shield,
    title: "Uy tín và minh bạch",
    description: "Xây dựng niềm tin thông qua sự minh bạch trong chương trình học và kết quả đào tạo."
  }
];

const achievements = [
  {
    number: "10,000+",
    label: "Học viên đã tin tưởng",
    description: "Hơn 10,000 học viên đã học tập và đạt được mục tiêu tiếng Anh"
  },
  {
    number: "95%",
    label: "Tỷ lệ hài lòng",
    description: "95% học viên hài lòng với chất lượng khóa học và dịch vụ"
  },
  {
    number: "200+",
    label: "Khóa học đa dạng",
    description: "Hơn 200 khóa học từ cơ bản đến nâng cao cho mọi đối tượng"
  },
  {
    number: "50+",
    label: "Giáo viên chuyên nghiệp",
    description: "Đội ngũ 50+ giáo viên có trình độ cao và kinh nghiệm phong phú"
  }
];

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-6">
            Về English Learning Platform
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Chúng tôi là nền tảng học tiếng Anh trực tuyến hàng đầu Việt Nam, 
            cam kết mang đến trải nghiệm học tập chất lượng cao và hiệu quả nhất 
            cho mọi học viên.
          </p>
        </div>

        {/* Mission & Vision */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Target className="h-6 w-6 mr-2 text-blue-600" />
                Sứ mệnh
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed">
                Democratize English education bằng cách cung cấp các khóa học tiếng Anh 
                chất lượng cao, dễ tiếp cận và phù hợp với mọi đối tượng học viên. 
                Chúng tôi tin rằng việc thành thạo tiếng Anh sẽ mở ra nhiều cơ hội 
                và thay đổi cuộc sống của mọi người.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Globe className="h-6 w-6 mr-2 text-green-600" />
                Tầm nhìn
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed">
                Trở thành nền tảng học tiếng Anh trực tuyến số 1 tại Việt Nam và 
                khu vực Đông Nam Á. Chúng tôi hướng tới việc tạo ra một cộng đồng 
                học tập toàn cầu nơi mọi người có thể học tiếng Anh một cách hiệu quả 
                và thú vị.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Core Values */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Giá trị cốt lõi</h2>
            <p className="text-lg text-gray-600">
              Những giá trị định hướng mọi hoạt động của chúng tôi
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <value.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <CardTitle className="text-lg">{value.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-sm">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Achievements */}
        <div className="bg-blue-600 rounded-2xl p-8 mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Thành tựu của chúng tôi</h2>
            <p className="text-blue-100">
              Những con số ấn tượng sau hành trình phát triển
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <div key={index} className="text-center text-white">
                <div className="text-4xl font-bold mb-2">{achievement.number}</div>
                <div className="text-xl font-semibold mb-2">{achievement.label}</div>
                <div className="text-blue-100 text-sm">{achievement.description}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Team Section */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Đội ngũ của chúng tôi</h2>
            <p className="text-lg text-gray-600">
              Những chuyên gia tâm huyết và giàu kinh nghiệm
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="w-24 h-24 bg-gray-300 rounded-full mx-auto mb-4"></div>
                  <CardTitle className="text-lg">{member.name}</CardTitle>
                  <CardDescription className="text-blue-600 font-medium">
                    {member.role}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-600 text-sm">{member.bio}</p>
                  <div className="flex flex-wrap gap-1 justify-center">
                    {member.specialties.map((specialty, idx) => (
                      <Badge key={idx} variant="secondary" className="text-xs">
                        {specialty}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Story Section */}
        <Card className="mb-16">
          <CardHeader>
            <CardTitle className="text-2xl text-center">Câu chuyện của chúng tôi</CardTitle>
          </CardHeader>
          <CardContent className="prose max-w-none">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <p className="text-gray-700 mb-4">
                  English Learning Platform được thành lập vào năm 2020 với mong muốn 
                  cách mạng hóa cách học tiếng Anh tại Việt Nam. Xuất phát từ nhận thức 
                  rằng nhiều người Việt gặp khó khăn trong việc tiếp cận các khóa học 
                  tiếng Anh chất lượng cao.
                </p>
                <p className="text-gray-700 mb-4">
                  Chúng tôi đã tập hợp đội ngũ các chuyên gia giáo dục và công nghệ 
                  để xây dựng một nền tảng học tập hiện đại, tương tác và hiệu quả. 
                  Với phương pháp giảng dạy dựa trên nghiên cứu khoa học và công nghệ 
                  AI tiên tiến.
                </p>
                <p className="text-gray-700">
                  Sau 4 năm phát triển, chúng tôi tự hào đã giúp hàng chục nghìn học viên 
                  đạt được mục tiêu tiếng Anh của mình và mở ra những cơ hội mới trong 
                  cuộc sống và sự nghiệp.
                </p>
              </div>
              <div className="bg-gray-200 rounded-lg h-64 flex items-center justify-center">
                <p className="text-gray-500">Hình ảnh về lịch sử phát triển</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact CTA */}
        <div className="text-center bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
          <h2 className="text-3xl font-bold mb-4">Sẵn sàng bắt đầu hành trình học tiếng Anh?</h2>
          <p className="text-xl mb-6">
            Tham gia cùng hàng nghìn học viên đã tin tưởng chọn chúng tôi
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Đăng ký ngay
            </button>
            <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
              Liên hệ tư vấn
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
