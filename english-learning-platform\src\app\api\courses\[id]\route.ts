import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Course from '@/models/Course';
import Enrollment from '@/models/Enrollment';
import { getAuthUser } from '@/lib/auth';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = await params;

    // Find course
    const course = await Course.findById(id)
      .populate('teacherId', 'name email profile');

    if (!course) {
      return NextResponse.json({
        success: false,
        message: '<PERSON>hông tìm thấy khóa học'
      }, { status: 404 });
    }

    // Check if course is published or user has permission
    const user = await getAuthUser(request);
    const canAccess = course.isPublished ||
                     (user && (user.role === 'admin' || user.userId === course.teacherId._id.toString()));

    if (!canAccess) {
      return NextResponse.json({
        success: false,
        message: '<PERSON><PERSON><PERSON><PERSON> học chưa được xuất bản'
      }, { status: 403 });
    }

    // Get enrollment info if user is logged in
    let enrollmentInfo = null;
    if (user && user.role === 'student') {
      enrollmentInfo = await Enrollment.findOne({
        studentId: user.userId,
        courseId: id
      });
    }

    // Get total enrollment count
    const totalEnrollments = await Enrollment.countDocuments({ courseId: id });

    // Prepare course data
    let courseData = course.toJSON();
    
    // For non-enrolled students, hide premium content
    if (user && user.role === 'student' && !enrollmentInfo) {
      courseData.curriculum = courseData.curriculum.map((lesson: any) => ({
        ...lesson,
        videoUrl: lesson.isPreview ? lesson.videoUrl : undefined,
        materials: lesson.isPreview ? lesson.materials : [],
        transcript: undefined,
        quiz: undefined
      }));
    }

    // Add enrollment and progress info
    const response = {
      ...courseData,
      totalEnrollments,
      isEnrolled: !!enrollmentInfo,
      enrollmentInfo: enrollmentInfo ? {
        progress: enrollmentInfo.progress,
        completedLessons: enrollmentInfo.completedLessons,
        lastAccessedAt: enrollmentInfo.lastAccessedAt,
        currentLesson: enrollmentInfo.currentLesson,
        totalTimeSpent: enrollmentInfo.totalTimeSpent,
        certificateIssued: enrollmentInfo.certificateIssued
      } : null
    };

    return NextResponse.json({
      success: true,
      data: response
    });

  } catch (error) {
    console.error('Get course error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi tải khóa học',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = await params;
    
    // Check authentication and permission
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find course
    const course = await Course.findById(id);
    if (!course) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy khóa học'
      }, { status: 404 });
    }

    // Check permission
    const canEdit = user.role === 'admin' || user.userId === course.teacherId.toString();
    if (!canEdit) {
      return NextResponse.json({
        success: false,
        message: 'Forbidden - Bạn không có quyền chỉnh sửa khóa học này'
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      title,
      description,
      category,
      level,
      price,
      thumbnail,
      curriculum,
      tags,
      requirements,
      whatYouWillLearn,
      language,
      subtitles,
      isPublished
    } = body;

    // Update course
    const updateData: any = {};
    
    if (title !== undefined) updateData.title = title.trim();
    if (description !== undefined) updateData.description = description.trim();
    if (category !== undefined) updateData.category = category;
    if (level !== undefined) updateData.level = level;
    if (price !== undefined) updateData.price = price;
    if (thumbnail !== undefined) updateData.thumbnail = thumbnail;
    if (curriculum !== undefined) {
      updateData.curriculum = curriculum.map((lesson: any, index: number) => ({
        ...lesson,
        order: index + 1
      }));
      // Recalculate total duration
      updateData.totalDuration = curriculum.reduce((sum: number, lesson: any) => sum + lesson.duration, 0);
    }
    if (tags !== undefined) updateData.tags = tags;
    if (requirements !== undefined) updateData.requirements = requirements;
    if (whatYouWillLearn !== undefined) updateData.whatYouWillLearn = whatYouWillLearn;
    if (language !== undefined) updateData.language = language;
    if (subtitles !== undefined) updateData.subtitles = subtitles;
    if (isPublished !== undefined) updateData.isPublished = isPublished;

    const updatedCourse = await Course.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('teacherId', 'name email profile');

    return NextResponse.json({
      success: true,
      message: 'Cập nhật khóa học thành công',
      data: updatedCourse
    });

  } catch (error) {
    console.error('Update course error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi cập nhật khóa học',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = await params;
    
    // Check authentication and permission
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find course
    const course = await Course.findById(id);
    if (!course) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy khóa học'
      }, { status: 404 });
    }

    // Check permission
    const canDelete = user.role === 'admin' || user.userId === course.teacherId.toString();
    if (!canDelete) {
      return NextResponse.json({
        success: false,
        message: 'Forbidden - Bạn không có quyền xóa khóa học này'
      }, { status: 403 });
    }

    // Check if course has enrollments
    const enrollmentCount = await Enrollment.countDocuments({ courseId: id });
    if (enrollmentCount > 0) {
      return NextResponse.json({
        success: false,
        message: 'Không thể xóa khóa học đã có học viên đăng ký'
      }, { status: 409 });
    }

    await Course.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Xóa khóa học thành công'
    });

  } catch (error) {
    console.error('Delete course error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi xóa khóa học',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
