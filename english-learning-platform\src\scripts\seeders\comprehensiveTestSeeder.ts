import mongoose from 'mongoose';
import connectDB from '@/lib/mongodb';
import Test from '@/models/Test';
import User from '@/models/User';
import { QuestionType, TestType, CourseLevel } from '@/types';

const comprehensiveTests = [
  {
    title: 'Kiểm tra Tổng hợp 4 Kỹ năng - Trình độ Trung cấp',
    description: 'Bài kiểm tra đánh giá toàn diện 4 kỹ năng: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>i<PERSON><PERSON> dành cho học viên trình độ trung cấp',
    type: TestType.COMPREHENSIVE,
    level: CourseLevel.INTERMEDIATE,
    duration: 90, // 90 minutes
    passingScore: 70,
    instructions: `
      <h3>Hướng dẫn làm bài:</h3>
      <ul>
        <li>Bài kiểm tra gồm 4 phần: <PERSON><PERSON> (25%), <PERSON><PERSON><PERSON> (25%), <PERSON><PERSON><PERSON> (25%), <PERSON><PERSON><PERSON><PERSON> (25%)</li>
        <li><PERSON>hờ<PERSON> gian làm bài: 90 phút</li>
        <li><PERSON><PERSON><PERSON><PERSON> đạt: 70%</li>
        <li><PERSON><PERSON><PERSON> kỹ hướng dẫn từng phần tr<PERSON>ớc khi làm bài</li>
        <li>Kiểm tra thiết bị âm thanh trước khi bắt đầu phần Nghe và Nói</li>
      </ul>
    `,
    questions: [
      // LISTENING SECTION (25%)
      {
        type: QuestionType.LISTENING_MULTIPLE_CHOICE,
        question: 'Listen to the conversation and choose the best answer: What time does the meeting start?',
        options: ['9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM'],
        correctAnswer: '9:30 AM',
        points: 5,
        audioUrl: '/audio/listening/meeting-time.mp3',
        explanation: 'The speaker clearly states "The meeting will start at nine thirty in the morning."',
        order: 1
      },
      {
        type: QuestionType.LISTENING_FILL_BLANK,
        question: 'Listen to the weather forecast and fill in the blanks: Tomorrow will be _____ with a high of _____ degrees and a low of _____ degrees.',
        correctAnswer: ['sunny', '25', '18'],
        points: 6,
        audioUrl: '/audio/listening/weather-forecast.mp3',
        explanation: 'The forecast mentions sunny weather with temperatures ranging from 18 to 25 degrees.',
        order: 2
      },
      {
        type: QuestionType.LISTENING_MULTIPLE_CHOICE,
        question: 'Listen to the dialogue and answer: Why is Sarah calling John?',
        options: [
          'To invite him to a party',
          'To ask about homework',
          'To cancel their meeting',
          'To discuss a project'
        ],
        correctAnswer: 'To discuss a project',
        points: 5,
        audioUrl: '/audio/listening/sarah-john-call.mp3',
        explanation: 'Sarah mentions she wants to talk about their group project deadline.',
        order: 3
      },
      {
        type: QuestionType.LISTENING_FILL_BLANK,
        question: 'Listen to the announcement and complete: Flight _____ to _____ is now boarding at gate _____.',
        correctAnswer: ['BA205', 'London', 'B12'],
        points: 6,
        audioUrl: '/audio/listening/flight-announcement.mp3',
        explanation: 'The announcement clearly states flight BA205 to London boarding at gate B12.',
        order: 4
      },

      // SPEAKING SECTION (25%)
      {
        type: QuestionType.SPEAKING_RECORD,
        question: 'Describe your daily routine. Include what time you wake up, what you do in the morning, afternoon, and evening. Speak for 1-2 minutes.',
        correctAnswer: 'Manual grading required',
        points: 10,
        timeLimit: 120,
        explanation: 'This question assesses fluency, vocabulary, grammar, and pronunciation.',
        order: 5
      },
      {
        type: QuestionType.SPEAKING_RECORD,
        question: 'Look at the picture and describe what you see. Then explain what you think is happening and how the people might be feeling.',
        correctAnswer: 'Manual grading required',
        points: 10,
        timeLimit: 90,
        imageUrl: '/images/speaking/family-picnic.jpg',
        explanation: 'This question tests descriptive language, inference skills, and emotional vocabulary.',
        order: 6
      },
      {
        type: QuestionType.SPEAKING_RECORD,
        question: 'Express your opinion: Do you think social media has a positive or negative impact on society? Give reasons for your answer.',
        correctAnswer: 'Manual grading required',
        points: 10,
        timeLimit: 150,
        explanation: 'This question evaluates opinion expression, argumentation, and advanced vocabulary.',
        order: 7
      },

      // READING SECTION (25%)
      {
        type: QuestionType.READING_COMPREHENSION,
        question: 'According to the passage, what is the main benefit of renewable energy?',
        readingPassage: `
          Renewable energy sources such as solar, wind, and hydroelectric power are becoming increasingly important in our fight against climate change. Unlike fossil fuels, renewable energy sources produce little to no greenhouse gas emissions during operation. This makes them crucial for reducing our carbon footprint and slowing global warming.
          
          Solar panels convert sunlight directly into electricity, while wind turbines harness the power of moving air. Hydroelectric dams use flowing water to generate power. All of these technologies have improved significantly in recent years, becoming more efficient and cost-effective.
          
          The main advantage of renewable energy is its sustainability. While fossil fuels will eventually run out, the sun will continue to shine, the wind will keep blowing, and rivers will keep flowing for billions of years. This makes renewable energy a reliable long-term solution for our energy needs.
          
          However, renewable energy also faces challenges. Solar and wind power depend on weather conditions, making energy storage important. Additionally, the initial cost of installing renewable energy systems can be high, though they typically pay for themselves over time through reduced energy bills.
        `,
        options: [
          'It is cheaper than fossil fuels',
          'It produces no greenhouse gas emissions',
          'It is sustainable and will not run out',
          'It is more efficient than traditional energy'
        ],
        correctAnswer: 'It is sustainable and will not run out',
        points: 8,
        explanation: 'The passage states that "The main advantage of renewable energy is its sustainability."',
        order: 8
      },
      {
        type: QuestionType.TRUE_FALSE,
        question: 'True or False: According to the passage, renewable energy systems always have low initial costs.',
        readingPassage: `[Same passage as above]`,
        correctAnswer: 'False',
        points: 4,
        explanation: 'The passage mentions that "the initial cost of installing renewable energy systems can be high."',
        order: 9
      },
      {
        type: QuestionType.WRITING_SHORT_ANSWER,
        question: 'Based on the passage, name two challenges that renewable energy faces.',
        readingPassage: `[Same passage as above]`,
        correctAnswer: 'Weather dependency and high initial costs',
        points: 6,
        explanation: 'The passage mentions weather dependency for solar/wind and high initial installation costs.',
        order: 10
      },
      {
        type: QuestionType.MULTIPLE_CHOICE,
        question: 'What does the word "harness" mean in the context of "wind turbines harness the power of moving air"?',
        options: ['Control', 'Capture and use', 'Prevent', 'Measure'],
        correctAnswer: 'Capture and use',
        points: 4,
        explanation: 'In this context, "harness" means to capture and utilize the wind\'s energy.',
        order: 11
      },

      // WRITING SECTION (25%)
      {
        type: QuestionType.WRITING_ESSAY,
        question: `
          Write an essay (200-250 words) on the following topic:
          
          "The Impact of Technology on Education"
          
          In your essay, you should:
          - Discuss how technology has changed education
          - Give examples of educational technologies
          - Express your opinion on whether these changes are positive or negative
          - Provide reasons to support your opinion
        `,
        correctAnswer: 'Manual grading required',
        points: 15,
        wordLimit: 250,
        keywords: ['technology', 'education', 'online learning', 'digital', 'students', 'teachers'],
        explanation: 'This essay tests writing structure, vocabulary, grammar, and critical thinking skills.',
        order: 12
      },
      {
        type: QuestionType.WRITING_SHORT_ANSWER,
        question: 'Write a formal email (80-100 words) to your teacher explaining why you were absent from class yesterday and requesting the homework assignments you missed.',
        correctAnswer: 'Manual grading required',
        points: 10,
        wordLimit: 100,
        explanation: 'This tests formal writing skills, email structure, and appropriate tone.',
        order: 13
      }
    ],
    tags: ['comprehensive', 'four-skills', 'intermediate', 'assessment'],
    difficulty: 'medium',
    isPublished: true
  }
];

export async function seedComprehensiveTests() {
  try {
    await connectDB();
    
    // Find a teacher user to assign as creator
    const teacher = await User.findOne({ role: 'teacher' });
    if (!teacher) {
      console.log('No teacher found. Please create a teacher user first.');
      return;
    }

    console.log('🌱 Seeding comprehensive tests...');

    // Clear existing comprehensive tests
    await Test.deleteMany({ type: TestType.COMPREHENSIVE });

    // Create comprehensive tests
    for (const testData of comprehensiveTests) {
      const test = new Test({
        ...testData,
        createdBy: teacher._id,
        questions: testData.questions.map((q, index) => ({
          ...q,
          order: index + 1
        }))
      });

      await test.save();
      console.log(`✅ Created test: ${test.title}`);
    }

    console.log('🎉 Comprehensive test seeding completed!');
    
  } catch (error) {
    console.error('❌ Error seeding comprehensive tests:', error);
  }
}

// Run seeder if called directly
if (require.main === module) {
  seedComprehensiveTests()
    .then(() => {
      console.log('Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}
