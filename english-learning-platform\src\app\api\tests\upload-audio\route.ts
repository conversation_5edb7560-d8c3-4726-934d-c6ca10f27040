import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;
    const questionId = formData.get('questionId') as string;
    const duration = formData.get('duration') as string;

    if (!audioFile) {
      return NextResponse.json(
        { success: false, message: 'Không tìm thấy file audio' },
        { status: 400 }
      );
    }

    if (!questionId) {
      return NextResponse.json(
        { success: false, message: 'Thiếu thông tin câu hỏi' },
        { status: 400 }
      );
    }

    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), 'public', 'uploads', 'audio');
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const fileExtension = audioFile.name.split('.').pop() || 'webm';
    const fileName = `${questionId}-${timestamp}.${fileExtension}`;
    const filePath = join(uploadsDir, fileName);

    // Convert file to buffer and save
    const bytes = await audioFile.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Return the public URL
    const audioUrl = `/uploads/audio/${fileName}`;

    // Here you could also save the audio record to database
    // const audioRecord = {
    //   questionId,
    //   audioUrl,
    //   duration: parseInt(duration),
    //   uploadedAt: new Date(),
    //   fileSize: buffer.length,
    //   mimeType: audioFile.type
    // };

    return NextResponse.json({
      success: true,
      audioUrl,
      message: 'Tải lên file audio thành công',
      data: {
        fileName,
        fileSize: buffer.length,
        duration: parseInt(duration),
        mimeType: audioFile.type
      }
    });

  } catch (error) {
    console.error('Upload audio error:', error);
    return NextResponse.json(
      { success: false, message: 'Lỗi khi tải lên file audio' },
      { status: 500 }
    );
  }
}
