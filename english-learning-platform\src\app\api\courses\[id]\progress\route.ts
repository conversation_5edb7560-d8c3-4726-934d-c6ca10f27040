import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Course from '@/models/Course';
import Enrollment from '@/models/Enrollment';
import { getAuthUser } from '@/lib/auth';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id: courseId } = params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find course
    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy khóa học'
      }, { status: 404 });
    }

    // Find enrollment
    const enrollment = await Enrollment.findOne({
      studentId: user.userId,
      courseId: courseId
    });

    if (!enrollment) {
      return NextResponse.json({
        success: false,
        message: '<PERSON>ạn chưa đăng ký khóa học này'
      }, { status: 403 });
    }

    // Calculate detailed progress
    const totalLessons = course.curriculum.length;
    const completedLessons = enrollment.completedLessons.length;
    const progressPercentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0;

    // Get lesson progress details
    const lessonProgressMap = new Map();
    enrollment.lessonProgress.forEach((progress: any) => {
      lessonProgressMap.set(progress.lessonId, progress);
    });

    // Build detailed curriculum with progress
    const curriculumWithProgress = course.curriculum.map((lesson: any) => {
      const lessonId = lesson.order.toString();
      const progress = lessonProgressMap.get(lessonId);
      
      return {
        ...lesson.toObject(),
        progress: progress || {
          lessonId,
          timeSpent: 0,
          videoProgress: 0,
          isCompleted: false,
          bookmarks: [],
          notes: ''
        }
      };
    });

    // Calculate time statistics
    const totalTimeSpent = enrollment.totalTimeSpent || 0;
    const averageTimePerLesson = completedLessons > 0 ? Math.round(totalTimeSpent / completedLessons) : 0;

    // Get next lesson to study
    const nextLesson = curriculumWithProgress.find((lesson: any) => !lesson.progress.isCompleted);

    // Calculate completion date estimate
    let estimatedCompletionDate = null;
    if (nextLesson && averageTimePerLesson > 0) {
      const remainingLessons = totalLessons - completedLessons;
      const estimatedRemainingTime = remainingLessons * averageTimePerLesson;
      estimatedCompletionDate = new Date(Date.now() + estimatedRemainingTime * 1000);
    }

    // Check if course is completed
    const isCompleted = completedLessons === totalLessons;
    
    // Update completion status if needed
    if (isCompleted && !enrollment.completedAt) {
      enrollment.completedAt = new Date();
      await enrollment.save();
    }

    return NextResponse.json({
      success: true,
      data: {
        courseId,
        courseTitle: course.title,
        enrolledAt: enrollment.enrolledAt,
        lastAccessedAt: enrollment.lastAccessedAt,
        currentLesson: enrollment.currentLesson,
        progress: {
          percentage: progressPercentage,
          completedLessons,
          totalLessons,
          totalTimeSpent,
          averageTimePerLesson,
          isCompleted,
          completedAt: enrollment.completedAt
        },
        curriculum: curriculumWithProgress,
        nextLesson: nextLesson ? {
          id: nextLesson._id,
          title: nextLesson.title,
          order: nextLesson.order,
          duration: nextLesson.duration
        } : null,
        statistics: {
          totalVideoTime: course.curriculum.reduce((total: number, lesson: any) => total + lesson.duration, 0),
          completionRate: progressPercentage,
          studyStreak: 0, // TODO: Calculate study streak
          estimatedCompletionDate
        },
        certificates: enrollment.certificateIssued ? [{
          id: enrollment.certificateId,
          issuedAt: enrollment.completedAt,
          downloadUrl: `/api/courses/${courseId}/certificate`
        }] : []
      }
    });

  } catch (error) {
    console.error('Get course progress error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi tải tiến độ khóa học',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id: courseId } = params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    const body = await request.json();
    const { action } = body;

    // Find enrollment
    const enrollment = await Enrollment.findOne({
      studentId: user.userId,
      courseId: courseId
    });

    if (!enrollment) {
      return NextResponse.json({
        success: false,
        message: 'Bạn chưa đăng ký khóa học này'
      }, { status: 403 });
    }

    if (action === 'reset_progress') {
      // Reset all progress
      enrollment.progress = 0;
      enrollment.completedLessons = [];
      enrollment.lessonProgress = [];
      enrollment.totalTimeSpent = 0;
      enrollment.currentLesson = null;
      enrollment.completedAt = null;
      enrollment.certificateIssued = false;
      enrollment.certificateId = null;

      await enrollment.save();

      return NextResponse.json({
        success: true,
        message: 'Đã reset tiến độ khóa học'
      });
    }

    if (action === 'generate_certificate') {
      const course = await Course.findById(courseId);
      if (!course) {
        return NextResponse.json({
          success: false,
          message: 'Không tìm thấy khóa học'
        }, { status: 404 });
      }

      // Check if course is completed
      const totalLessons = course.curriculum.length;
      const completedLessons = enrollment.completedLessons.length;
      
      if (completedLessons < totalLessons) {
        return NextResponse.json({
          success: false,
          message: 'Bạn cần hoàn thành tất cả bài học để nhận chứng chỉ'
        }, { status: 400 });
      }

      if (!enrollment.certificateIssued) {
        enrollment.certificateIssued = true;
        enrollment.certificateId = `cert_${courseId}_${user.userId}_${Date.now()}`;
        await enrollment.save();
      }

      return NextResponse.json({
        success: true,
        message: 'Chứng chỉ đã được tạo',
        data: {
          certificateId: enrollment.certificateId,
          downloadUrl: `/api/courses/${courseId}/certificate`
        }
      });
    }

    return NextResponse.json({
      success: false,
      message: 'Hành động không hợp lệ'
    }, { status: 400 });

  } catch (error) {
    console.error('Course progress action error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi thực hiện hành động',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
