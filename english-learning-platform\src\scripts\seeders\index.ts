import connectDB from '@/lib/mongodb';
import { seedUsers } from './userSeeder';
import { seedCourses } from './courseSeeder';
import { seedTests } from './testSeeder';
import { seedTestResults } from './testResultSeeder';

async function runSeeders() {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Connect to database
    await connectDB();
    console.log('✅ Connected to MongoDB');

    // Run seeders in order
    console.log('\n📝 Seeding users...');
    const users = await seedUsers();
    console.log(`✅ Created ${users.length} users`);

    console.log('\n📚 Seeding courses...');
    const courses = await seedCourses(users);
    console.log(`✅ Created ${courses.length} courses`);

    console.log('\n📋 Seeding tests...');
    const tests = await seedTests();
    console.log(`✅ Created ${tests.length} tests`);

    console.log('\n📊 Seeding test results...');
    const testResults = await seedTestResults(users, tests);
    console.log(`✅ Created ${testResults.length} test results`);

    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- Users: ${users.length}`);
    console.log(`- Courses: ${courses.length}`);
    console.log(`- Tests: ${tests.length}`);
    console.log(`- Test Results: ${testResults.length}`);

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

// Run if called directly
if (require.main === module) {
  runSeeders();
}

export default runSeeders;
