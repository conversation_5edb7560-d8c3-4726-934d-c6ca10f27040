{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('vi-VN', {\n    style: 'currency',\n    currency: 'VND'\n  }).format(price);\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('vi-VN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  }).format(dateObj);\n}\n\nexport function formatDuration(minutes: number): string {\n  const hours = Math.floor(minutes / 60);\n  const remainingMinutes = minutes % 60;\n\n  if (hours === 0) {\n    return `${remainingMinutes} phút`;\n  } else if (remainingMinutes === 0) {\n    return `${hours} giờ`;\n  } else {\n    return `${hours} giờ ${remainingMinutes} phút`;\n  }\n}\n\nexport function calculateProgress(completedLessons: number[], totalLessons: number): number {\n  if (totalLessons === 0) return 0;\n  return Math.round((completedLessons.length / totalLessons) * 100);\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2);\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '') // Remove diacritics\n    .replace(/[^a-z0-9 -]/g, '') // Remove special characters\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen\n    .trim();\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n\n  if (password.length < 6) {\n    errors.push('Mật khẩu phải có ít nhất 6 ký tự');\n  }\n\n  if (!/[A-Z]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 chữ hoa');\n  }\n\n  if (!/[a-z]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 chữ thường');\n  }\n\n  if (!/[0-9]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 số');\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\nexport function generateRandomString(length: number): string {\n  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += characters.charAt(Math.floor(Math.random() * characters.length));\n  }\n  return result;\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,mBAAmB,UAAU;IAEnC,IAAI,UAAU,GAAG;QACf,OAAO,GAAG,iBAAiB,KAAK,CAAC;IACnC,OAAO,IAAI,qBAAqB,GAAG;QACjC,OAAO,GAAG,MAAM,IAAI,CAAC;IACvB,OAAO;QACL,OAAO,GAAG,MAAM,KAAK,EAAE,iBAAiB,KAAK,CAAC;IAChD;AACF;AAEO,SAAS,kBAAkB,gBAA0B,EAAE,YAAoB;IAChF,IAAI,iBAAiB,GAAG,OAAO;IAC/B,OAAO,KAAK,KAAK,CAAC,AAAC,iBAAiB,MAAM,GAAG,eAAgB;AAC/D;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAAI,oBAAoB;KACpD,OAAO,CAAC,gBAAgB,IAAI,4BAA4B;KACxD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,OAAO,KAAK,8CAA8C;KAClE,IAAI;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,qBAAqB,MAAc;IACjD,MAAM,aAAa;IACnB,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,WAAW,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM;IAC1E;IACA,OAAO;AACT;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { ChevronRight, Home } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface BreadcrumbItem {\n  label: string;\n  href?: string;\n  current?: boolean;\n}\n\ninterface BreadcrumbProps {\n  items: BreadcrumbItem[];\n  className?: string;\n  showHome?: boolean;\n}\n\nexport function Breadcrumb({ items, className, showHome = true }: BreadcrumbProps) {\n  const allItems = showHome \n    ? [{ label: \"Trang chủ\", href: \"/\" }, ...items]\n    : items;\n\n  return (\n    <nav className={cn(\"flex\", className)} aria-label=\"Breadcrumb\">\n      <ol className=\"inline-flex items-center space-x-1 md:space-x-3\">\n        {allItems.map((item, index) => (\n          <li key={index} className=\"inline-flex items-center\">\n            {index > 0 && (\n              <ChevronRight className=\"w-4 h-4 text-gray-400 mx-1\" />\n            )}\n            \n            {item.current || !item.href ? (\n              <span className=\"text-sm font-medium text-gray-500 flex items-center\">\n                {index === 0 && showHome && (\n                  <Home className=\"w-4 h-4 mr-1\" />\n                )}\n                {item.label}\n              </span>\n            ) : (\n              <Link\n                href={item.href}\n                className=\"inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors\"\n              >\n                {index === 0 && showHome && (\n                  <Home className=\"w-4 h-4 mr-1\" />\n                )}\n                {item.label}\n              </Link>\n            )}\n          </li>\n        ))}\n      </ol>\n    </nav>\n  );\n}\n\n// Hook để tự động tạo breadcrumb từ URL\nexport function useBreadcrumb() {\n  if (typeof window === 'undefined') return [];\n  \n  const pathname = window.location.pathname;\n  const segments = pathname.split('/').filter(Boolean);\n  \n  const breadcrumbMap: Record<string, string> = {\n    'courses': 'Khóa học',\n    'tests': 'Luyện thi',\n    'dashboard': 'Dashboard',\n    'profile': 'Hồ sơ',\n    'about': 'Giới thiệu',\n    'contact': 'Liên hệ',\n    'auth': 'Xác thực',\n    'login': 'Đăng nhập',\n    'register': 'Đăng ký',\n    'teacher': 'Giáo viên',\n    'admin': 'Quản trị',\n    'student': 'Học viên'\n  };\n\n  const items: BreadcrumbItem[] = [];\n  let currentPath = '';\n\n  segments.forEach((segment, index) => {\n    currentPath += `/${segment}`;\n    const isLast = index === segments.length - 1;\n    \n    items.push({\n      label: breadcrumbMap[segment] || segment,\n      href: isLast ? undefined : currentPath,\n      current: isLast\n    });\n  });\n\n  return items;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;;;;;AAcO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,IAAI,EAAmB;IAC/E,MAAM,WAAW,WACb;QAAC;YAAE,OAAO;YAAa,MAAM;QAAI;WAAM;KAAM,GAC7C;IAEJ,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAY,cAAW;kBAChD,cAAA,6LAAC;YAAG,WAAU;sBACX,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC;oBAAe,WAAU;;wBACvB,QAAQ,mBACP,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAGzB,KAAK,OAAO,IAAI,CAAC,KAAK,IAAI,iBACzB,6LAAC;4BAAK,WAAU;;gCACb,UAAU,KAAK,0BACd,6LAAC,sMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAEjB,KAAK,KAAK;;;;;;iDAGb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAU;;gCAET,UAAU,KAAK,0BACd,6LAAC,sMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAEjB,KAAK,KAAK;;;;;;;;mBApBR;;;;;;;;;;;;;;;AA4BnB;KArCgB;AAwCT,SAAS;IACd,uCAAmC;;IAAS;IAE5C,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ;IACzC,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAE5C,MAAM,gBAAwC;QAC5C,WAAW;QACX,SAAS;QACT,aAAa;QACb,WAAW;QACX,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,WAAW;QACX,SAAS;QACT,WAAW;IACb;IAEA,MAAM,QAA0B,EAAE;IAClC,IAAI,cAAc;IAElB,SAAS,OAAO,CAAC,CAAC,SAAS;QACzB,eAAe,CAAC,CAAC,EAAE,SAAS;QAC5B,MAAM,SAAS,UAAU,SAAS,MAAM,GAAG;QAE3C,MAAM,IAAI,CAAC;YACT,OAAO,aAAa,CAAC,QAAQ,IAAI;YACjC,MAAM,SAAS,YAAY;YAC3B,SAAS;QACX;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/loading.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\n\ninterface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\";\n  className?: string;\n}\n\nexport function LoadingSpinner({ size = \"md\", className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: \"h-4 w-4\",\n    md: \"h-6 w-6\", \n    lg: \"h-8 w-8\"\n  };\n\n  return (\n    <div\n      className={cn(\n        \"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600\",\n        sizeClasses[size],\n        className\n      )}\n    />\n  );\n}\n\ninterface LoadingCardProps {\n  className?: string;\n}\n\nexport function LoadingCard({ className }: LoadingCardProps) {\n  return (\n    <div className={cn(\"animate-pulse\", className)}>\n      <div className=\"bg-gray-200 rounded-lg h-48 mb-4\"></div>\n      <div className=\"space-y-3\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-5/6\"></div>\n      </div>\n    </div>\n  );\n}\n\ninterface LoadingPageProps {\n  message?: string;\n}\n\nexport function LoadingPage({ message = \"Đang tải...\" }: LoadingPageProps) {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <LoadingSpinner size=\"lg\" className=\"mx-auto mb-4\" />\n        <p className=\"text-gray-600\">{message}</p>\n      </div>\n    </div>\n  );\n}\n\ninterface LoadingButtonProps {\n  children: React.ReactNode;\n  isLoading?: boolean;\n  className?: string;\n  disabled?: boolean;\n  onClick?: () => void;\n  type?: \"button\" | \"submit\" | \"reset\";\n}\n\nexport function LoadingButton({ \n  children, \n  isLoading = false, \n  className,\n  disabled,\n  onClick,\n  type = \"button\"\n}: LoadingButtonProps) {\n  return (\n    <button\n      type={type}\n      onClick={onClick}\n      disabled={disabled || isLoading}\n      className={cn(\n        \"inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n        className\n      )}\n    >\n      {isLoading && <LoadingSpinner size=\"sm\" className=\"mr-2\" />}\n      {children}\n    </button>\n  );\n}\n\nexport function LoadingOverlay() {\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 flex items-center space-x-3\">\n        <LoadingSpinner size=\"md\" />\n        <span className=\"text-gray-700\">Đang xử lý...</span>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR;KAhBgB;AAsBT,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;;0BAClC,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;MAXgB;AAiBT,SAAS,YAAY,EAAE,UAAU,aAAa,EAAoB;IACvE,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAe,MAAK;oBAAK,WAAU;;;;;;8BACpC,6LAAC;oBAAE,WAAU;8BAAiB;;;;;;;;;;;;;;;;;AAItC;MATgB;AAoBT,SAAS,cAAc,EAC5B,QAAQ,EACR,YAAY,KAAK,EACjB,SAAS,EACT,QAAQ,EACR,OAAO,EACP,OAAO,QAAQ,EACI;IACnB,qBACE,6LAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU,YAAY;QACtB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+QACA;;YAGD,2BAAa,6LAAC;gBAAe,MAAK;gBAAK,WAAU;;;;;;YACjD;;;;;;;AAGP;MAtBgB;AAwBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAe,MAAK;;;;;;8BACrB,6LAAC;oBAAK,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAIxC;MATgB", "debugId": null}}, {"offset": {"line": 714, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/error.tsx"], "sourcesContent": ["import { Alert<PERSON><PERSON>gle, <PERSON>fresh<PERSON><PERSON>, Home, ArrowLeft } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { cn } from \"@/lib/utils\";\n\ninterface ErrorMessageProps {\n  title?: string;\n  message?: string;\n  className?: string;\n}\n\nexport function ErrorMessage({ \n  title = \"Đã xảy ra lỗi\", \n  message = \"Vui lòng thử lại sau.\", \n  className \n}: ErrorMessageProps) {\n  return (\n    <div className={cn(\"flex items-center space-x-2 text-red-600\", className)}>\n      <AlertTriangle className=\"h-4 w-4\" />\n      <div>\n        <p className=\"font-medium text-sm\">{title}</p>\n        {message && <p className=\"text-xs text-red-500\">{message}</p>}\n      </div>\n    </div>\n  );\n}\n\ninterface ErrorCardProps {\n  title?: string;\n  message?: string;\n  onRetry?: () => void;\n  className?: string;\n}\n\nexport function ErrorCard({ \n  title = \"Không thể tải dữ liệu\", \n  message = \"Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại.\", \n  onRetry,\n  className \n}: ErrorCardProps) {\n  return (\n    <Card className={cn(\"border-red-200\", className)}>\n      <CardContent className=\"pt-6\">\n        <div className=\"text-center\">\n          <AlertTriangle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">{title}</h3>\n          <p className=\"text-gray-600 mb-4\">{message}</p>\n          {onRetry && (\n            <Button onClick={onRetry} variant=\"outline\">\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\n              Thử lại\n            </Button>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n\ninterface ErrorPageProps {\n  title?: string;\n  message?: string;\n  showHomeButton?: boolean;\n  showBackButton?: boolean;\n  onRetry?: () => void;\n}\n\nexport function ErrorPage({ \n  title = \"Oops! Đã xảy ra lỗi\", \n  message = \"Trang bạn đang tìm kiếm không tồn tại hoặc đã xảy ra lỗi hệ thống.\",\n  showHomeButton = true,\n  showBackButton = true,\n  onRetry\n}: ErrorPageProps) {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center px-4\">\n      <div className=\"max-w-md w-full text-center\">\n        <div className=\"mb-8\">\n          <AlertTriangle className=\"h-24 w-24 text-red-500 mx-auto mb-6\" />\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">{title}</h1>\n          <p className=\"text-gray-600 mb-8\">{message}</p>\n        </div>\n        \n        <div className=\"space-y-3\">\n          {onRetry && (\n            <Button onClick={onRetry} className=\"w-full\">\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\n              Thử lại\n            </Button>\n          )}\n          \n          {showHomeButton && (\n            <Button variant=\"outline\" className=\"w-full\" onClick={() => window.location.href = '/'}>\n              <Home className=\"h-4 w-4 mr-2\" />\n              Về trang chủ\n            </Button>\n          )}\n          \n          {showBackButton && (\n            <Button variant=\"ghost\" className=\"w-full\" onClick={() => window.history.back()}>\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Quay lại\n            </Button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface FormErrorProps {\n  errors: Record<string, string>;\n  className?: string;\n}\n\nexport function FormError({ errors, className }: FormErrorProps) {\n  const errorMessages = Object.values(errors).filter(Boolean);\n  \n  if (errorMessages.length === 0) return null;\n\n  return (\n    <div className={cn(\"bg-red-50 border border-red-200 rounded-md p-3\", className)}>\n      <div className=\"flex\">\n        <AlertTriangle className=\"h-4 w-4 text-red-400 mt-0.5 mr-2 flex-shrink-0\" />\n        <div className=\"text-sm\">\n          {errorMessages.length === 1 ? (\n            <p className=\"text-red-800\">{errorMessages[0]}</p>\n          ) : (\n            <div>\n              <p className=\"text-red-800 font-medium mb-1\">Vui lòng kiểm tra lại:</p>\n              <ul className=\"list-disc list-inside text-red-700 space-y-1\">\n                {errorMessages.map((error, index) => (\n                  <li key={index}>{error}</li>\n                ))}\n              </ul>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface NetworkErrorProps {\n  onRetry?: () => void;\n  className?: string;\n}\n\nexport function NetworkError({ onRetry, className }: NetworkErrorProps) {\n  return (\n    <div className={cn(\"text-center py-8\", className)}>\n      <div className=\"mb-4\">\n        <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n          <AlertTriangle className=\"h-8 w-8 text-red-600\" />\n        </div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n          Lỗi kết nối mạng\n        </h3>\n        <p className=\"text-gray-600 mb-4\">\n          Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối internet và thử lại.\n        </p>\n      </div>\n      \n      {onRetry && (\n        <Button onClick={onRetry} variant=\"outline\">\n          <RefreshCw className=\"h-4 w-4 mr-2\" />\n          Thử lại\n        </Button>\n      )}\n    </div>\n  );\n}\n\ninterface NotFoundProps {\n  title?: string;\n  message?: string;\n  showHomeButton?: boolean;\n}\n\nexport function NotFound({ \n  title = \"Không tìm thấy trang\", \n  message = \"Trang bạn đang tìm kiếm không tồn tại hoặc đã được di chuyển.\",\n  showHomeButton = true\n}: NotFoundProps) {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center px-4\">\n      <div className=\"max-w-md w-full text-center\">\n        <div className=\"mb-8\">\n          <div className=\"text-6xl font-bold text-gray-400 mb-4\">404</div>\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">{title}</h1>\n          <p className=\"text-gray-600 mb-8\">{message}</p>\n        </div>\n        \n        {showHomeButton && (\n          <Button onClick={() => window.location.href = '/'}>\n            <Home className=\"h-4 w-4 mr-2\" />\n            Về trang chủ\n          </Button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAQO,SAAS,aAAa,EAC3B,QAAQ,eAAe,EACvB,UAAU,uBAAuB,EACjC,SAAS,EACS;IAClB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;;0BAC7D,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,6LAAC;;kCACC,6LAAC;wBAAE,WAAU;kCAAuB;;;;;;oBACnC,yBAAW,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAIzD;KAdgB;AAuBT,SAAS,UAAU,EACxB,QAAQ,uBAAuB,EAC/B,UAAU,kDAAkD,EAC5D,OAAO,EACP,SAAS,EACM;IACf,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBACpC,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2NAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,6LAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;oBAClC,yBACC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAS,SAAQ;;0CAChC,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;;;;;AAQpD;MAvBgB;AAiCT,SAAS,UAAU,EACxB,QAAQ,qBAAqB,EAC7B,UAAU,oEAAoE,EAC9E,iBAAiB,IAAI,EACrB,iBAAiB,IAAI,EACrB,OAAO,EACQ;IACf,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAGrC,6LAAC;oBAAI,WAAU;;wBACZ,yBACC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAS,WAAU;;8CAClC,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;wBAKzC,gCACC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,WAAU;4BAAS,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;8CACjF,6LAAC,sMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;wBAKpC,gCACC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,WAAU;4BAAS,SAAS,IAAM,OAAO,OAAO,CAAC,IAAI;;8CAC3E,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAQpD;MAzCgB;AAgDT,SAAS,UAAU,EAAE,MAAM,EAAE,SAAS,EAAkB;IAC7D,MAAM,gBAAgB,OAAO,MAAM,CAAC,QAAQ,MAAM,CAAC;IAEnD,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;kBACnE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;8BACzB,6LAAC;oBAAI,WAAU;8BACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;wBAAE,WAAU;kCAAgB,aAAa,CAAC,EAAE;;;;;6CAE7C,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;0CAC7C,6LAAC;gCAAG,WAAU;0CACX,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;kDAAgB;uCAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3B;MA1BgB;AAiCT,SAAS,aAAa,EAAE,OAAO,EAAE,SAAS,EAAqB;IACpE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;;0BACrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;kCAE3B,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;YAKnC,yBACC,6LAAC,qIAAA,CAAA,SAAM;gBAAC,SAAS;gBAAS,SAAQ;;kCAChC,6LAAC,mNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;;;;;;AAMhD;MAvBgB;AA+BT,SAAS,SAAS,EACvB,QAAQ,sBAAsB,EAC9B,UAAU,+DAA+D,EACzE,iBAAiB,IAAI,EACP;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAwC;;;;;;sCACvD,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;gBAGpC,gCACC,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;sCAC5C,6LAAC,sMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;;;;;AAO7C;MAvBgB", "debugId": null}}, {"offset": {"line": 1187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 1483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 1545, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { useState } from \"react\";\nimport { Book<PERSON>pen, Menu, X } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { useAuth } from \"@/contexts/AuthContext\";\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const { user, isAuthenticated, logout } = useAuth();\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  const handleLogout = async () => {\n    await logout();\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <BookOpen className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-xl font-bold text-gray-900\">\n              English Learning Platform\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <Link\n              href=\"/courses\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Khóa Học\n            </Link>\n            <Link\n              href=\"/tests\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Luyện Thi\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Giới Thiệu\n            </Link>\n            <Link\n              href=\"/contact\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Liên Hệ\n            </Link>\n          </nav>\n\n          {/* Desktop Auth/User Menu */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {isAuthenticated && user ? (\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                    <Avatar className=\"h-8 w-8\">\n                      <AvatarImage src={user.avatar} alt={user.name} />\n                      <AvatarFallback>\n                        {user.name.split(' ').map((n: string) => n[0]).join('')}\n                      </AvatarFallback>\n                    </Avatar>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                  <DropdownMenuLabel className=\"font-normal\">\n                    <div className=\"flex flex-col space-y-1\">\n                      <p className=\"text-sm font-medium leading-none\">{user.name}</p>\n                      <p className=\"text-xs leading-none text-muted-foreground\">\n                        {user.email}\n                      </p>\n                    </div>\n                  </DropdownMenuLabel>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard\">Dashboard</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/profile\">Hồ sơ cá nhân</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/settings\">Cài đặt</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem onClick={handleLogout}>\n                    Đăng xuất\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            ) : (\n              <>\n                <Link href=\"/auth/login\">\n                  <Button variant=\"ghost\">\n                    Đăng Nhập\n                  </Button>\n                </Link>\n                <Link href=\"/auth/register\">\n                  <Button>\n                    Đăng Ký\n                  </Button>\n                </Link>\n              </>\n            )}\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            className=\"md:hidden p-2\"\n            onClick={toggleMenu}\n            aria-label=\"Toggle menu\"\n          >\n            {isMenuOpen ? (\n              <X className=\"h-6 w-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"h-6 w-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <Link\n                href=\"/courses\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Khóa Học\n              </Link>\n              <Link\n                href=\"/tests\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Luyện Thi\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Giới Thiệu\n              </Link>\n              <Link\n                href=\"/contact\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Liên Hệ\n              </Link>\n\n              <div className=\"flex flex-col space-y-2 pt-4 border-t border-gray-200\">\n                {isAuthenticated && user ? (\n                  <>\n                    <div className=\"flex items-center space-x-3 px-3 py-2\">\n                      <Avatar className=\"h-8 w-8\">\n                        <AvatarImage src={user.avatar} alt={user.name} />\n                        <AvatarFallback>\n                          {user.name.split(' ').map((n: string) => n[0]).join('')}\n                        </AvatarFallback>\n                      </Avatar>\n                      <div>\n                        <p className=\"text-sm font-medium\">{user.name}</p>\n                        <p className=\"text-xs text-gray-500\">{user.email}</p>\n                      </div>\n                    </div>\n                    <Link href=\"/dashboard\" onClick={() => setIsMenuOpen(false)}>\n                      <Button variant=\"ghost\" className=\"w-full justify-start\">\n                        Dashboard\n                      </Button>\n                    </Link>\n                    <Link href=\"/profile\" onClick={() => setIsMenuOpen(false)}>\n                      <Button variant=\"ghost\" className=\"w-full justify-start\">\n                        Hồ sơ cá nhân\n                      </Button>\n                    </Link>\n                    <Button\n                      variant=\"ghost\"\n                      className=\"w-full justify-start\"\n                      onClick={handleLogout}\n                    >\n                      Đăng xuất\n                    </Button>\n                  </>\n                ) : (\n                  <>\n                    <Link href=\"/auth/login\" onClick={() => setIsMenuOpen(false)}>\n                      <Button variant=\"ghost\" className=\"w-full justify-start\">\n                        Đăng Nhập\n                      </Button>\n                    </Link>\n                    <Link href=\"/auth/register\" onClick={() => setIsMenuOpen(false)}>\n                      <Button className=\"w-full\">\n                        Đăng Ký\n                      </Button>\n                    </Link>\n                  </>\n                )}\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAQA;AACA;;;AAfA;;;;;;;;AAiBe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhD,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,MAAM,eAAe;QACnB,MAAM;QACN,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAMzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACZ,mBAAmB,qBAClB,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,WAAU;sDAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,qIAAA,CAAA,cAAW;wDAAC,KAAK,KAAK,MAAM;wDAAE,KAAK,KAAK,IAAI;;;;;;kEAC7C,6LAAC,qIAAA,CAAA,iBAAc;kEACZ,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;kDAK5D,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,WAAU;wCAAO,OAAM;wCAAM,UAAU;;0DAC1D,6LAAC,+IAAA,CAAA,oBAAiB;gDAAC,WAAU;0DAC3B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAoC,KAAK,IAAI;;;;;;sEAC1D,6LAAC;4DAAE,WAAU;sEACV,KAAK,KAAK;;;;;;;;;;;;;;;;;0DAIjB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0DACtB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAa;;;;;;;;;;;0DAE1B,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAW;;;;;;;;;;;0DAExB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAY;;;;;;;;;;;0DAEzB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0DACtB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS;0DAAc;;;;;;;;;;;;;;;;;qDAM7C;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAQ;;;;;;;;;;;kDAI1B,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;;;;;;;;sCAShB,6LAAC;4BACC,WAAU;4BACV,SAAS;4BACT,cAAW;sCAEV,2BACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAID,6LAAC;gCAAI,WAAU;0CACZ,mBAAmB,qBAClB;;sDACE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,WAAU;;sEAChB,6LAAC,qIAAA,CAAA,cAAW;4DAAC,KAAK,KAAK,MAAM;4DAAE,KAAK,KAAK,IAAI;;;;;;sEAC7C,6LAAC,qIAAA,CAAA,iBAAc;sEACZ,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;8DAGxD,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAuB,KAAK,IAAI;;;;;;sEAC7C,6LAAC;4DAAE,WAAU;sEAAyB,KAAK,KAAK;;;;;;;;;;;;;;;;;;sDAGpD,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,SAAS,IAAM,cAAc;sDACnD,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAAuB;;;;;;;;;;;sDAI3D,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,SAAS,IAAM,cAAc;sDACjD,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAAuB;;;;;;;;;;;sDAI3D,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;sDACV;;;;;;;iEAKH;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,SAAS,IAAM,cAAc;sDACpD,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAAuB;;;;;;;;;;;sDAI3D,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAiB,SAAS,IAAM,cAAc;sDACvD,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAajD;GAjNwB;;QAEoB,kIAAA,CAAA,UAAO;;;KAF3B", "debugId": null}}, {"offset": {"line": 2110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/types/index.ts"], "sourcesContent": ["// Enum types\nexport enum UserRole {\n  ADMIN = 'admin',\n  TEACHER = 'teacher',\n  STUDENT = 'student'\n}\n\nexport enum CourseCategory {\n  LISTENING = 'listening',\n  SPEAKING = 'speaking',\n  READING = 'reading',\n  WRITING = 'writing',\n  COMPREHENSIVE = 'comprehensive'\n}\n\nexport enum CourseLevel {\n  BEGINNER = 'beginner',\n  INTERMEDIATE = 'intermediate',\n  ADVANCED = 'advanced'\n}\n\nexport enum TestType {\n  VOCABULARY = 'vocabulary',\n  GRAMMAR = 'grammar',\n  LISTENING = 'listening',\n  SPEAKING = 'speaking',\n  READING = 'reading',\n  WRITING = 'writing',\n  COMPREHENSIVE = 'comprehensive',\n  PRACTICE = 'practice'\n}\n\nexport enum QuestionType {\n  MULTIPLE_CHOICE = 'multiple_choice',\n  FILL_IN_BLANK = 'fill_in_blank',\n  TRUE_FALSE = 'true_false',\n  ESSAY = 'essay',\n  AUDIO_RESPONSE = 'audio_response',\n  DRAG_DROP = 'drag_drop',\n  MATCHING = 'matching',\n  // Enhanced question types for language skills\n  LISTENING_MULTIPLE_CHOICE = 'listening_multiple_choice',\n  LISTENING_FILL_BLANK = 'listening_fill_blank',\n  SPEAKING_RECORD = 'speaking_record',\n  READING_COMPREHENSION = 'reading_comprehension',\n  WRITING_ESSAY = 'writing_essay',\n  WRITING_SHORT_ANSWER = 'writing_short_answer'\n}\n\nexport enum PaymentStatus {\n  PENDING = 'pending',\n  COMPLETED = 'completed',\n  FAILED = 'failed'\n}\n\n// User types\nexport interface User {\n  _id: string;\n  email: string;\n  name: string;\n  role: UserRole;\n  avatar?: string;\n  phone?: string;\n  dateOfBirth?: Date;\n  isEmailVerified: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n  profile?: UserProfile;\n}\n\nexport interface UserProfile {\n  bio?: string;\n  experience?: string; // for teachers\n  education?: string;\n  skills?: string[];\n}\n\n// Course types\nexport interface Course {\n  _id: string;\n  title: string;\n  description: string;\n  shortDescription: string;\n  teacherId: string;\n  teacher?: User;\n  category: CourseCategory;\n  level: CourseLevel;\n  price: number;\n  duration: number; // in hours\n  thumbnail?: string;\n  videoIntro?: string;\n  isPublished: boolean;\n  curriculum: Lesson[];\n  ratings: Rating[];\n  averageRating: number;\n  totalStudents: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Lesson {\n  title: string;\n  description: string;\n  videoUrl?: string;\n  materials: string[];\n  duration: number; // in minutes\n}\n\nexport interface Rating {\n  userId: string;\n  user?: User;\n  rating: number;\n  comment: string;\n  createdAt: Date;\n}\n\n// Enrollment types\nexport interface Enrollment {\n  _id: string;\n  studentId: string;\n  student?: User;\n  courseId: string;\n  course?: Course;\n  enrolledAt: Date;\n  progress: number; // percentage\n  completedLessons: number[];\n  lastAccessedAt: Date;\n  paymentStatus: PaymentStatus;\n  paymentId?: string;\n}\n\n// Test types\nexport interface Test {\n  _id: string;\n  title: string;\n  description: string;\n  type: TestType;\n  level: CourseLevel;\n  duration: number; // in minutes\n  totalQuestions: number;\n  passingScore: number;\n  questions: Question[];\n  createdBy: string;\n  creator?: User;\n  isPublished: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Question {\n  _id?: string;\n  type: QuestionType;\n  question: string;\n  options?: string[]; // for multiple choice\n  correctAnswer: string | string[]; // can be array for multiple correct answers\n  points: number;\n  order?: number;\n  explanation?: string;\n  // Enhanced fields for language skills\n  audioUrl?: string; // for listening questions\n  audioSegments?: AudioSegment[]; // multiple audio files\n  imageUrl?: string;\n  readingPassage?: string; // for reading comprehension\n  wordLimit?: number; // for writing questions\n  timeLimit?: number; // for speaking questions (in seconds)\n  keywords?: string[]; // for essay scoring\n  rubric?: GradingRubric; // for manual grading\n  autoGrade?: boolean; // whether to use automatic grading\n}\n\nexport interface AudioSegment {\n  url: string;\n  title: string;\n  duration: number; // in seconds\n  transcript?: string; // for reference\n}\n\nexport interface GradingRubric {\n  criteria: GradingCriterion[];\n  maxScore: number;\n}\n\nexport interface GradingCriterion {\n  name: string;\n  description: string;\n  maxPoints: number;\n  levels: GradingLevel[];\n}\n\nexport interface GradingLevel {\n  score: number;\n  description: string;\n}\n\nexport interface TestResult {\n  _id: string;\n  studentId: string;\n  student?: User;\n  testId: string;\n  test?: Test;\n  answers: Answer[];\n  totalScore: number;\n  percentage: number;\n  timeSpent: number; // in minutes\n  startedAt: Date;\n  completedAt: Date;\n  feedback?: string;\n}\n\nexport interface Answer {\n  questionId: string;\n  questionIndex: number;\n  answer: string | string[]; // can be array for multiple answers\n  isCorrect: boolean;\n  points: number;\n  maxPoints: number;\n  // Enhanced fields for different answer types\n  audioUrl?: string; // for speaking responses\n  audioBlob?: Blob; // for client-side audio data\n  timeSpent?: number; // time spent on question (in seconds)\n  attempts?: number; // number of attempts\n  feedback?: string; // teacher feedback\n  gradedBy?: string; // teacher who graded (for manual grading)\n  gradedAt?: Date; // when it was graded\n  rubricScores?: RubricScore[]; // detailed scoring for essays/speaking\n}\n\nexport interface RubricScore {\n  criterionId: string;\n  criterionName: string;\n  score: number;\n  maxScore: number;\n  feedback?: string;\n}\n\n// API Response types\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\n// Form types\nexport interface LoginForm {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterForm {\n  name: string;\n  email: string;\n  password: string;\n  confirmPassword: string;\n  role?: UserRole;\n}\n\nexport interface CourseForm {\n  title: string;\n  description: string;\n  shortDescription: string;\n  category: CourseCategory;\n  level: CourseLevel;\n  price: number;\n  duration: number;\n  thumbnail?: string;\n  videoIntro?: string;\n  curriculum: Lesson[];\n}\n\n// Dashboard types\nexport interface DashboardStats {\n  totalCourses: number;\n  totalStudents: number;\n  totalRevenue: number;\n  totalTests: number;\n}\n\nexport interface StudentDashboard {\n  enrolledCourses: Course[];\n  recentTests: TestResult[];\n  progress: {\n    courseId: string;\n    courseName: string;\n    progress: number;\n  }[];\n  stats: {\n    totalCourses: number;\n    completedCourses: number;\n    averageScore: number;\n    totalTestsTaken: number;\n  };\n}\n"], "names": [], "mappings": "AAAA,aAAa;;;;;;;;;AACN,IAAA,AAAK,kCAAA;;;;WAAA;;AAML,IAAA,AAAK,wCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,qCAAA;;;;WAAA;;AAML,IAAA,AAAK,kCAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,sCAAA;;;;;;;;IAQV,8CAA8C;;;;;;;WARpC;;AAiBL,IAAA,AAAK,uCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 2182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/test/AudioPlayer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useRef, useEffect } from \"react\";\nimport { Play, Pause, RotateCcw, Volume2, VolumeX } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Slider } from \"@/components/ui/slider\";\nimport { Card, CardContent } from \"@/components/ui/card\";\n\ninterface AudioPlayerProps {\n  audioUrl: string;\n  title?: string;\n  onPlay?: () => void;\n  onPause?: () => void;\n  onEnded?: () => void;\n  allowReplay?: boolean;\n  maxReplays?: number;\n  showTranscript?: boolean;\n  transcript?: string;\n}\n\nexport default function AudioPlayer({\n  audioUrl,\n  title = \"Audio\",\n  onPlay,\n  onPause,\n  onEnded,\n  allowReplay = true,\n  maxReplays = 3,\n  showTranscript = false,\n  transcript\n}: AudioPlayerProps) {\n  const audioRef = useRef<HTMLAudioElement>(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [currentTime, setCurrentTime] = useState(0);\n  const [duration, setDuration] = useState(0);\n  const [volume, setVolume] = useState(1);\n  const [isMuted, setIsMuted] = useState(false);\n  const [replayCount, setReplayCount] = useState(0);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const audio = audioRef.current;\n    if (!audio) return;\n\n    const handleLoadedMetadata = () => {\n      setDuration(audio.duration);\n      setIsLoading(false);\n    };\n\n    const handleTimeUpdate = () => {\n      setCurrentTime(audio.currentTime);\n    };\n\n    const handleEnded = () => {\n      setIsPlaying(false);\n      setCurrentTime(0);\n      onEnded?.();\n    };\n\n    const handleError = () => {\n      setError(\"Không thể tải file audio\");\n      setIsLoading(false);\n    };\n\n    const handleCanPlay = () => {\n      setIsLoading(false);\n    };\n\n    audio.addEventListener('loadedmetadata', handleLoadedMetadata);\n    audio.addEventListener('timeupdate', handleTimeUpdate);\n    audio.addEventListener('ended', handleEnded);\n    audio.addEventListener('error', handleError);\n    audio.addEventListener('canplay', handleCanPlay);\n\n    return () => {\n      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);\n      audio.removeEventListener('timeupdate', handleTimeUpdate);\n      audio.removeEventListener('ended', handleEnded);\n      audio.removeEventListener('error', handleError);\n      audio.removeEventListener('canplay', handleCanPlay);\n    };\n  }, [onEnded]);\n\n  const togglePlay = () => {\n    const audio = audioRef.current;\n    if (!audio) return;\n\n    if (isPlaying) {\n      audio.pause();\n      setIsPlaying(false);\n      onPause?.();\n    } else {\n      audio.play();\n      setIsPlaying(true);\n      onPlay?.();\n    }\n  };\n\n  const handleReplay = () => {\n    const audio = audioRef.current;\n    if (!audio || !allowReplay || replayCount >= maxReplays) return;\n\n    audio.currentTime = 0;\n    setCurrentTime(0);\n    setReplayCount(prev => prev + 1);\n    \n    if (!isPlaying) {\n      audio.play();\n      setIsPlaying(true);\n      onPlay?.();\n    }\n  };\n\n  const handleSeek = (value: number[]) => {\n    const audio = audioRef.current;\n    if (!audio) return;\n\n    const newTime = (value[0] / 100) * duration;\n    audio.currentTime = newTime;\n    setCurrentTime(newTime);\n  };\n\n  const handleVolumeChange = (value: number[]) => {\n    const audio = audioRef.current;\n    if (!audio) return;\n\n    const newVolume = value[0] / 100;\n    audio.volume = newVolume;\n    setVolume(newVolume);\n    setIsMuted(newVolume === 0);\n  };\n\n  const toggleMute = () => {\n    const audio = audioRef.current;\n    if (!audio) return;\n\n    if (isMuted) {\n      audio.volume = volume;\n      setIsMuted(false);\n    } else {\n      audio.volume = 0;\n      setIsMuted(true);\n    }\n  };\n\n  const formatTime = (time: number) => {\n    const minutes = Math.floor(time / 60);\n    const seconds = Math.floor(time % 60);\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  };\n\n  const progress = duration > 0 ? (currentTime / duration) * 100 : 0;\n\n  if (error) {\n    return (\n      <Card className=\"w-full\">\n        <CardContent className=\"p-4\">\n          <div className=\"text-center text-red-600\">\n            <p className=\"text-sm\">{error}</p>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className=\"w-full\">\n      <CardContent className=\"p-4\">\n        <audio ref={audioRef} src={audioUrl} preload=\"metadata\" />\n        \n        {/* Title */}\n        <div className=\"mb-4\">\n          <h4 className=\"font-medium text-gray-900\">{title}</h4>\n          {allowReplay && maxReplays > 0 && (\n            <p className=\"text-sm text-gray-500\">\n              Còn lại: {maxReplays - replayCount} lần nghe\n            </p>\n          )}\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"mb-4\">\n          <Slider\n            value={[progress]}\n            onValueChange={handleSeek}\n            max={100}\n            step={0.1}\n            className=\"w-full\"\n            disabled={isLoading || duration === 0}\n          />\n          <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n            <span>{formatTime(currentTime)}</span>\n            <span>{formatTime(duration)}</span>\n          </div>\n        </div>\n\n        {/* Controls */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={togglePlay}\n              disabled={isLoading}\n            >\n              {isLoading ? (\n                <div className=\"w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin\" />\n              ) : isPlaying ? (\n                <Pause className=\"h-4 w-4\" />\n              ) : (\n                <Play className=\"h-4 w-4\" />\n              )}\n            </Button>\n\n            {allowReplay && (\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={handleReplay}\n                disabled={isLoading || replayCount >= maxReplays}\n              >\n                <RotateCcw className=\"h-4 w-4\" />\n              </Button>\n            )}\n          </div>\n\n          {/* Volume Control */}\n          <div className=\"flex items-center gap-2\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={toggleMute}\n            >\n              {isMuted ? (\n                <VolumeX className=\"h-4 w-4\" />\n              ) : (\n                <Volume2 className=\"h-4 w-4\" />\n              )}\n            </Button>\n            <div className=\"w-20\">\n              <Slider\n                value={[isMuted ? 0 : volume * 100]}\n                onValueChange={handleVolumeChange}\n                max={100}\n                step={1}\n                className=\"w-full\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Transcript */}\n        {showTranscript && transcript && (\n          <div className=\"mt-4 p-3 bg-gray-50 rounded-lg\">\n            <h5 className=\"text-sm font-medium text-gray-700 mb-2\">Transcript:</h5>\n            <p className=\"text-sm text-gray-600\">{transcript}</p>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;AAEA;;;AANA;;;;;;AAoBe,SAAS,YAAY,EAClC,QAAQ,EACR,QAAQ,OAAO,EACf,MAAM,EACN,OAAO,EACP,OAAO,EACP,cAAc,IAAI,EAClB,aAAa,CAAC,EACd,iBAAiB,KAAK,EACtB,UAAU,EACO;;IACjB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,QAAQ,SAAS,OAAO;YAC9B,IAAI,CAAC,OAAO;YAEZ,MAAM;8DAAuB;oBAC3B,YAAY,MAAM,QAAQ;oBAC1B,aAAa;gBACf;;YAEA,MAAM;0DAAmB;oBACvB,eAAe,MAAM,WAAW;gBAClC;;YAEA,MAAM;qDAAc;oBAClB,aAAa;oBACb,eAAe;oBACf;gBACF;;YAEA,MAAM;qDAAc;oBAClB,SAAS;oBACT,aAAa;gBACf;;YAEA,MAAM;uDAAgB;oBACpB,aAAa;gBACf;;YAEA,MAAM,gBAAgB,CAAC,kBAAkB;YACzC,MAAM,gBAAgB,CAAC,cAAc;YACrC,MAAM,gBAAgB,CAAC,SAAS;YAChC,MAAM,gBAAgB,CAAC,SAAS;YAChC,MAAM,gBAAgB,CAAC,WAAW;YAElC;yCAAO;oBACL,MAAM,mBAAmB,CAAC,kBAAkB;oBAC5C,MAAM,mBAAmB,CAAC,cAAc;oBACxC,MAAM,mBAAmB,CAAC,SAAS;oBACnC,MAAM,mBAAmB,CAAC,SAAS;oBACnC,MAAM,mBAAmB,CAAC,WAAW;gBACvC;;QACF;gCAAG;QAAC;KAAQ;IAEZ,MAAM,aAAa;QACjB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,IAAI,WAAW;YACb,MAAM,KAAK;YACX,aAAa;YACb;QACF,OAAO;YACL,MAAM,IAAI;YACV,aAAa;YACb;QACF;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,SAAS,CAAC,eAAe,eAAe,YAAY;QAEzD,MAAM,WAAW,GAAG;QACpB,eAAe;QACf,eAAe,CAAA,OAAQ,OAAO;QAE9B,IAAI,CAAC,WAAW;YACd,MAAM,IAAI;YACV,aAAa;YACb;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,UAAU,AAAC,KAAK,CAAC,EAAE,GAAG,MAAO;QACnC,MAAM,WAAW,GAAG;QACpB,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,YAAY,KAAK,CAAC,EAAE,GAAG;QAC7B,MAAM,MAAM,GAAG;QACf,UAAU;QACV,WAAW,cAAc;IAC3B;IAEA,MAAM,aAAa;QACjB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,IAAI,SAAS;YACX,MAAM,MAAM,GAAG;YACf,WAAW;QACb,OAAO;YACL,MAAM,MAAM,GAAG;YACf,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,OAAO,GAAG,QAAQ,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC5D;IAEA,MAAM,WAAW,WAAW,IAAI,AAAC,cAAc,WAAY,MAAM;IAEjE,IAAI,OAAO;QACT,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAW;;;;;;;;;;;;;;;;;;;;;IAKlC;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,6LAAC;oBAAM,KAAK;oBAAU,KAAK;oBAAU,SAAQ;;;;;;8BAG7C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;wBAC1C,eAAe,aAAa,mBAC3B,6LAAC;4BAAE,WAAU;;gCAAwB;gCACzB,aAAa;gCAAY;;;;;;;;;;;;;8BAMzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,OAAO;gCAAC;6BAAS;4BACjB,eAAe;4BACf,KAAK;4BACL,MAAM;4BACN,WAAU;4BACV,UAAU,aAAa,aAAa;;;;;;sCAEtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAM,WAAW;;;;;;8CAClB,6LAAC;8CAAM,WAAW;;;;;;;;;;;;;;;;;;8BAKtB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU;8CAET,0BACC,6LAAC;wCAAI,WAAU;;;;;+CACb,0BACF,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;6DAEjB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;gCAInB,6BACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU,aAAa,eAAe;8CAEtC,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAM3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;8CAER,wBACC,6LAAC,+MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,6LAAC,+MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAGvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,OAAO;4CAAC,UAAU,IAAI,SAAS;yCAAI;wCACnC,eAAe;wCACf,KAAK;wCACL,MAAM;wCACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAOjB,kBAAkB,4BACjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;;;;;;;;;;;;AAMlD;GAlPwB;KAAA", "debugId": null}}, {"offset": {"line": 2643, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/test/AudioRecorder.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useRef, useEffect } from \"react\";\nimport { Mic, Square, Play, Pause, RotateCcw, Upload } from \"lucide-react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Progress } from \"@/components/ui/progress\";\n\ninterface AudioRecorderProps {\n  questionId: string;\n  timeLimit?: number; // in seconds\n  onRecordingComplete?: (audioBlob: Blob, duration: number) => void;\n  onUploadComplete?: (audioUrl: string) => void;\n  maxAttempts?: number;\n  allowReRecord?: boolean;\n  prompt?: string;\n}\n\nexport default function AudioRecorder({\n  questionId,\n  timeLimit = 120, // 2 minutes default\n  onRecordingComplete,\n  onUploadComplete,\n  maxAttempts = 3,\n  allowReRecord = true,\n  prompt\n}: AudioRecorderProps) {\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null);\n  const audioRef = useRef<HTMLAudioElement>(null);\n  const chunksRef = useRef<Blob[]>([]);\n  \n  const [isRecording, setIsRecording] = useState(false);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [recordingTime, setRecordingTime] = useState(0);\n  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);\n  const [audioUrl, setAudioUrl] = useState<string | null>(null);\n  const [attempts, setAttempts] = useState(0);\n  const [isUploading, setIsUploading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [permissionGranted, setPermissionGranted] = useState(false);\n  const [stream, setStream] = useState<MediaStream | null>(null);\n\n  useEffect(() => {\n    // Request microphone permission on component mount\n    requestMicrophonePermission();\n    \n    return () => {\n      // Cleanup\n      if (stream) {\n        stream.getTracks().forEach(track => track.stop());\n      }\n      if (audioUrl) {\n        URL.revokeObjectURL(audioUrl);\n      }\n    };\n  }, []);\n\n  useEffect(() => {\n    let interval: NodeJS.Timeout;\n    \n    if (isRecording) {\n      interval = setInterval(() => {\n        setRecordingTime(prev => {\n          if (prev >= timeLimit) {\n            stopRecording();\n            return prev;\n          }\n          return prev + 1;\n        });\n      }, 1000);\n    }\n\n    return () => {\n      if (interval) clearInterval(interval);\n    };\n  }, [isRecording, timeLimit]);\n\n  const requestMicrophonePermission = async () => {\n    try {\n      const mediaStream = await navigator.mediaDevices.getUserMedia({ \n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          sampleRate: 44100\n        }\n      });\n      setStream(mediaStream);\n      setPermissionGranted(true);\n      setError(null);\n    } catch (err) {\n      setError(\"Không thể truy cập microphone. Vui lòng cho phép quyền truy cập.\");\n      setPermissionGranted(false);\n    }\n  };\n\n  const startRecording = () => {\n    if (!stream || !permissionGranted) {\n      requestMicrophonePermission();\n      return;\n    }\n\n    if (attempts >= maxAttempts) {\n      setError(`Bạn đã sử dụng hết ${maxAttempts} lần ghi âm.`);\n      return;\n    }\n\n    try {\n      chunksRef.current = [];\n      const mediaRecorder = new MediaRecorder(stream, {\n        mimeType: 'audio/webm;codecs=opus'\n      });\n\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          chunksRef.current.push(event.data);\n        }\n      };\n\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunksRef.current, { type: 'audio/webm;codecs=opus' });\n        setAudioBlob(blob);\n        \n        // Create URL for playback\n        if (audioUrl) {\n          URL.revokeObjectURL(audioUrl);\n        }\n        const url = URL.createObjectURL(blob);\n        setAudioUrl(url);\n        \n        onRecordingComplete?.(blob, recordingTime);\n      };\n\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start(100); // Collect data every 100ms\n      \n      setIsRecording(true);\n      setRecordingTime(0);\n      setError(null);\n    } catch (err) {\n      setError(\"Lỗi khi bắt đầu ghi âm. Vui lòng thử lại.\");\n    }\n  };\n\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      setIsRecording(false);\n      setAttempts(prev => prev + 1);\n    }\n  };\n\n  const playRecording = () => {\n    if (!audioRef.current || !audioUrl) return;\n\n    if (isPlaying) {\n      audioRef.current.pause();\n      setIsPlaying(false);\n    } else {\n      audioRef.current.play();\n      setIsPlaying(true);\n    }\n  };\n\n  const reRecord = () => {\n    if (!allowReRecord) return;\n    \n    setAudioBlob(null);\n    if (audioUrl) {\n      URL.revokeObjectURL(audioUrl);\n      setAudioUrl(null);\n    }\n    setRecordingTime(0);\n    setIsPlaying(false);\n    setError(null);\n  };\n\n  const uploadRecording = async () => {\n    if (!audioBlob) return;\n\n    setIsUploading(true);\n    setError(null);\n\n    try {\n      const formData = new FormData();\n      formData.append('audio', audioBlob, `recording-${questionId}-${Date.now()}.webm`);\n      formData.append('questionId', questionId);\n      formData.append('duration', recordingTime.toString());\n\n      const response = await fetch('/api/tests/upload-audio', {\n        method: 'POST',\n        body: formData,\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        onUploadComplete?.(data.audioUrl);\n      } else {\n        setError(data.message || 'Lỗi khi tải lên file ghi âm');\n      }\n    } catch (err) {\n      setError('Lỗi kết nối. Vui lòng thử lại.');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const progressPercentage = (recordingTime / timeLimit) * 100;\n\n  if (!permissionGranted) {\n    return (\n      <Card className=\"w-full\">\n        <CardContent className=\"p-6 text-center\">\n          <Mic className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\n          <h3 className=\"text-lg font-medium mb-2\">Cần quyền truy cập Microphone</h3>\n          <p className=\"text-gray-600 mb-4\">\n            Để ghi âm câu trả lời, vui lòng cho phép truy cập microphone.\n          </p>\n          <Button onClick={requestMicrophonePermission}>\n            Cho phép truy cập Microphone\n          </Button>\n          {error && (\n            <p className=\"text-red-600 text-sm mt-2\">{error}</p>\n          )}\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className=\"w-full\">\n      <CardHeader>\n        <CardTitle className=\"text-lg\">Ghi âm câu trả lời</CardTitle>\n        {prompt && (\n          <p className=\"text-sm text-gray-600\">{prompt}</p>\n        )}\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Recording Status */}\n        <div className=\"text-center\">\n          <div className=\"text-2xl font-mono font-bold text-blue-600 mb-2\">\n            {formatTime(recordingTime)} / {formatTime(timeLimit)}\n          </div>\n          <Progress value={progressPercentage} className=\"w-full\" />\n          <p className=\"text-sm text-gray-500 mt-1\">\n            Lần thử: {attempts}/{maxAttempts}\n          </p>\n        </div>\n\n        {/* Recording Controls */}\n        <div className=\"flex justify-center gap-2\">\n          {!isRecording && !audioBlob && (\n            <Button\n              onClick={startRecording}\n              disabled={attempts >= maxAttempts}\n              className=\"bg-red-600 hover:bg-red-700\"\n            >\n              <Mic className=\"h-4 w-4 mr-2\" />\n              Bắt đầu ghi âm\n            </Button>\n          )}\n\n          {isRecording && (\n            <Button\n              onClick={stopRecording}\n              variant=\"outline\"\n              className=\"border-red-600 text-red-600 hover:bg-red-50\"\n            >\n              <Square className=\"h-4 w-4 mr-2\" />\n              Dừng ghi âm\n            </Button>\n          )}\n\n          {audioBlob && !isRecording && (\n            <>\n              <Button\n                onClick={playRecording}\n                variant=\"outline\"\n              >\n                {isPlaying ? (\n                  <Pause className=\"h-4 w-4 mr-2\" />\n                ) : (\n                  <Play className=\"h-4 w-4 mr-2\" />\n                )}\n                {isPlaying ? 'Tạm dừng' : 'Nghe lại'}\n              </Button>\n\n              {allowReRecord && attempts < maxAttempts && (\n                <Button\n                  onClick={reRecord}\n                  variant=\"outline\"\n                >\n                  <RotateCcw className=\"h-4 w-4 mr-2\" />\n                  Ghi lại\n                </Button>\n              )}\n\n              <Button\n                onClick={uploadRecording}\n                disabled={isUploading}\n                className=\"bg-green-600 hover:bg-green-700\"\n              >\n                {isUploading ? (\n                  <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\" />\n                ) : (\n                  <Upload className=\"h-4 w-4 mr-2\" />\n                )}\n                {isUploading ? 'Đang tải lên...' : 'Nộp bài'}\n              </Button>\n            </>\n          )}\n        </div>\n\n        {/* Audio Element for Playback */}\n        {audioUrl && (\n          <audio\n            ref={audioRef}\n            src={audioUrl}\n            onEnded={() => setIsPlaying(false)}\n            className=\"hidden\"\n          />\n        )}\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"text-center text-red-600 text-sm\">\n            {error}\n          </div>\n        )}\n\n        {/* Instructions */}\n        <div className=\"text-xs text-gray-500 text-center\">\n          <p>• Nói rõ ràng và gần microphone</p>\n          <p>• Tối đa {maxAttempts} lần ghi âm</p>\n          <p>• Thời gian tối đa: {formatTime(timeLimit)}</p>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAkBe,SAAS,cAAc,EACpC,UAAU,EACV,YAAY,GAAG,EACf,mBAAmB,EACnB,gBAAgB,EAChB,cAAc,CAAC,EACf,gBAAgB,IAAI,EACpB,MAAM,EACa;;IACnB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAwB;IACtD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU,EAAE;IAEnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,mDAAmD;YACnD;YAEA;2CAAO;oBACL,UAAU;oBACV,IAAI,QAAQ;wBACV,OAAO,SAAS,GAAG,OAAO;uDAAC,CAAA,QAAS,MAAM,IAAI;;oBAChD;oBACA,IAAI,UAAU;wBACZ,IAAI,eAAe,CAAC;oBACtB;gBACF;;QACF;kCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI;YAEJ,IAAI,aAAa;gBACf,WAAW;+CAAY;wBACrB;uDAAiB,CAAA;gCACf,IAAI,QAAQ,WAAW;oCACrB;oCACA,OAAO;gCACT;gCACA,OAAO,OAAO;4BAChB;;oBACF;8CAAG;YACL;YAEA;2CAAO;oBACL,IAAI,UAAU,cAAc;gBAC9B;;QACF;kCAAG;QAAC;QAAa;KAAU;IAE3B,MAAM,8BAA8B;QAClC,IAAI;YACF,MAAM,cAAc,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAC5D,OAAO;oBACL,kBAAkB;oBAClB,kBAAkB;oBAClB,YAAY;gBACd;YACF;YACA,UAAU;YACV,qBAAqB;YACrB,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,qBAAqB;QACvB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,UAAU,CAAC,mBAAmB;YACjC;YACA;QACF;QAEA,IAAI,YAAY,aAAa;YAC3B,SAAS,CAAC,mBAAmB,EAAE,YAAY,YAAY,CAAC;YACxD;QACF;QAEA,IAAI;YACF,UAAU,OAAO,GAAG,EAAE;YACtB,MAAM,gBAAgB,IAAI,cAAc,QAAQ;gBAC9C,UAAU;YACZ;YAEA,cAAc,eAAe,GAAG,CAAC;gBAC/B,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG;oBACvB,UAAU,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI;gBACnC;YACF;YAEA,cAAc,MAAM,GAAG;gBACrB,MAAM,OAAO,IAAI,KAAK,UAAU,OAAO,EAAE;oBAAE,MAAM;gBAAyB;gBAC1E,aAAa;gBAEb,0BAA0B;gBAC1B,IAAI,UAAU;oBACZ,IAAI,eAAe,CAAC;gBACtB;gBACA,MAAM,MAAM,IAAI,eAAe,CAAC;gBAChC,YAAY;gBAEZ,sBAAsB,MAAM;YAC9B;YAEA,iBAAiB,OAAO,GAAG;YAC3B,cAAc,KAAK,CAAC,MAAM,2BAA2B;YAErD,eAAe;YACf,iBAAiB;YACjB,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,iBAAiB,OAAO,IAAI,aAAa;YAC3C,iBAAiB,OAAO,CAAC,IAAI;YAC7B,eAAe;YACf,YAAY,CAAA,OAAQ,OAAO;QAC7B;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,UAAU;QAEpC,IAAI,WAAW;YACb,SAAS,OAAO,CAAC,KAAK;YACtB,aAAa;QACf,OAAO;YACL,SAAS,OAAO,CAAC,IAAI;YACrB,aAAa;QACf;IACF;IAEA,MAAM,WAAW;QACf,IAAI,CAAC,eAAe;QAEpB,aAAa;QACb,IAAI,UAAU;YACZ,IAAI,eAAe,CAAC;YACpB,YAAY;QACd;QACA,iBAAiB;QACjB,aAAa;QACb,SAAS;IACX;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,WAAW;QAEhB,eAAe;QACf,SAAS;QAET,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,KAAK,GAAG,GAAG,KAAK,CAAC;YAChF,SAAS,MAAM,CAAC,cAAc;YAC9B,SAAS,MAAM,CAAC,YAAY,cAAc,QAAQ;YAElD,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,mBAAmB,KAAK,QAAQ;YAClC,OAAO;gBACL,SAAS,KAAK,OAAO,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,qBAAqB,AAAC,gBAAgB,YAAa;IAEzD,IAAI,CAAC,mBAAmB;QACtB,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC,mMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;kCACf,6LAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;kCAA6B;;;;;;oBAG7C,uBACC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAKpD;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAU;;;;;;oBAC9B,wBACC,6LAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;0BAG1C,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,WAAW;oCAAe;oCAAI,WAAW;;;;;;;0CAE5C,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,OAAO;gCAAoB,WAAU;;;;;;0CAC/C,6LAAC;gCAAE,WAAU;;oCAA6B;oCAC9B;oCAAS;oCAAE;;;;;;;;;;;;;kCAKzB,6LAAC;wBAAI,WAAU;;4BACZ,CAAC,eAAe,CAAC,2BAChB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,YAAY;gCACtB,WAAU;;kDAEV,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAKnC,6BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,WAAU;;kDAEV,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAKtC,aAAa,CAAC,6BACb;;kDACE,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;;4CAEP,0BACC,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;qEAEjB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAEjB,YAAY,aAAa;;;;;;;oCAG3B,iBAAiB,WAAW,6BAC3B,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;;0DAER,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAK1C,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;;4CAET,4BACC,6LAAC;gDAAI,WAAU;;;;;qEAEf,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAEnB,cAAc,oBAAoB;;;;;;;;;;;;;;;oBAO1C,0BACC,6LAAC;wBACC,KAAK;wBACL,KAAK;wBACL,SAAS,IAAM,aAAa;wBAC5B,WAAU;;;;;;oBAKb,uBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAKL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CACH,6LAAC;;oCAAE;oCAAU;oCAAY;;;;;;;0CACzB,6LAAC;;oCAAE;oCAAqB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;AAK7C;GAvUwB;KAAA", "debugId": null}}, {"offset": {"line": 3172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/test/QuestionComponents.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\nimport { Label } from \"@/components/ui/label\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport { Progress } from \"@/components/ui/progress\";\nimport AudioPlayer from \"./AudioPlayer\";\nimport AudioRecorder from \"./AudioRecorder\";\nimport { Question, QuestionType } from \"@/types\";\n\ninterface QuestionComponentProps {\n  question: Question;\n  questionIndex: number;\n  answer?: string | string[];\n  onAnswerChange: (answer: string | string[]) => void;\n  isSubmitted?: boolean;\n  showCorrectAnswer?: boolean;\n  timeRemaining?: number;\n}\n\n// Listening Multiple Choice Component\nexport function ListeningMultipleChoice({\n  question,\n  questionIndex,\n  answer,\n  onAnswerChange,\n  isSubmitted,\n  showCorrectAnswer\n}: QuestionComponentProps) {\n  const [hasPlayedAudio, setHasPlayedAudio] = useState(false);\n\n  return (\n    <Card className=\"w-full\">\n      <CardHeader>\n        <CardTitle className=\"text-lg\">\n          Câu {questionIndex + 1}: Nghe và chọn đáp án đúng\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Audio Player */}\n        {question.audioUrl && (\n          <AudioPlayer\n            audioUrl={question.audioUrl}\n            title=\"Nghe đoạn hội thoại/bài nghe\"\n            onPlay={() => setHasPlayedAudio(true)}\n            allowReplay={true}\n            maxReplays={2}\n          />\n        )}\n\n        {/* Question Text */}\n        <div className=\"p-4 bg-blue-50 rounded-lg\">\n          <p className=\"text-gray-800\">{question.question}</p>\n        </div>\n\n        {/* Multiple Choice Options */}\n        <RadioGroup\n          value={answer as string}\n          onValueChange={onAnswerChange}\n          disabled={isSubmitted}\n        >\n          {question.options?.map((option, index) => {\n            const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n            const isCorrect = showCorrectAnswer && option === question.correctAnswer;\n            const isSelected = answer === option;\n            const isWrong = showCorrectAnswer && isSelected && !isCorrect;\n\n            return (\n              <div\n                key={index}\n                className={`flex items-center space-x-2 p-3 rounded-lg border ${\n                  isCorrect\n                    ? 'bg-green-50 border-green-200'\n                    : isWrong\n                    ? 'bg-red-50 border-red-200'\n                    : 'bg-white border-gray-200'\n                }`}\n              >\n                <RadioGroupItem value={option} id={`option-${questionIndex}-${index}`} />\n                <Label\n                  htmlFor={`option-${questionIndex}-${index}`}\n                  className=\"flex-1 cursor-pointer\"\n                >\n                  <span className=\"font-medium mr-2\">{optionLetter}.</span>\n                  {option}\n                </Label>\n              </div>\n            );\n          })}\n        </RadioGroup>\n\n        {!hasPlayedAudio && (\n          <div className=\"text-amber-600 text-sm\">\n            ⚠️ Vui lòng nghe audio trước khi trả lời\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n\n// Listening Fill in the Blank Component\nexport function ListeningFillBlank({\n  question,\n  questionIndex,\n  answer,\n  onAnswerChange,\n  isSubmitted,\n  showCorrectAnswer\n}: QuestionComponentProps) {\n  const [blanks, setBlanks] = useState<string[]>([]);\n  const [hasPlayedAudio, setHasPlayedAudio] = useState(false);\n\n  useEffect(() => {\n    // Initialize blanks array based on question format\n    const blankCount = (question.question.match(/_+/g) || []).length;\n    setBlanks(new Array(blankCount).fill(''));\n    \n    // If answer is provided, populate blanks\n    if (answer && Array.isArray(answer)) {\n      setBlanks(answer);\n    }\n  }, [question, answer]);\n\n  const handleBlankChange = (index: number, value: string) => {\n    const newBlanks = [...blanks];\n    newBlanks[index] = value;\n    setBlanks(newBlanks);\n    onAnswerChange(newBlanks);\n  };\n\n  const renderQuestionWithBlanks = () => {\n    const parts = question.question.split(/_+/);\n    const result = [];\n\n    for (let i = 0; i < parts.length; i++) {\n      result.push(<span key={`text-${i}`}>{parts[i]}</span>);\n      \n      if (i < parts.length - 1) {\n        const correctAnswer = Array.isArray(question.correctAnswer) \n          ? question.correctAnswer[i] \n          : '';\n        const userAnswer = blanks[i] || '';\n        const isCorrect = showCorrectAnswer && userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();\n        const isWrong = showCorrectAnswer && userAnswer && !isCorrect;\n\n        result.push(\n          <Input\n            key={`blank-${i}`}\n            value={blanks[i] || ''}\n            onChange={(e) => handleBlankChange(i, e.target.value)}\n            disabled={isSubmitted}\n            className={`inline-block w-24 mx-1 text-center ${\n              isCorrect\n                ? 'border-green-500 bg-green-50'\n                : isWrong\n                ? 'border-red-500 bg-red-50'\n                : ''\n            }`}\n            placeholder=\"...\"\n          />\n        );\n      }\n    }\n\n    return result;\n  };\n\n  return (\n    <Card className=\"w-full\">\n      <CardHeader>\n        <CardTitle className=\"text-lg\">\n          Câu {questionIndex + 1}: Nghe và điền từ còn thiếu\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Audio Player */}\n        {question.audioUrl && (\n          <AudioPlayer\n            audioUrl={question.audioUrl}\n            title=\"Nghe đoạn hội thoại/bài nghe\"\n            onPlay={() => setHasPlayedAudio(true)}\n            allowReplay={true}\n            maxReplays={3}\n          />\n        )}\n\n        {/* Question with Blanks */}\n        <div className=\"p-4 bg-blue-50 rounded-lg\">\n          <div className=\"text-gray-800 leading-relaxed\">\n            {renderQuestionWithBlanks()}\n          </div>\n        </div>\n\n        {/* Show correct answers if needed */}\n        {showCorrectAnswer && Array.isArray(question.correctAnswer) && (\n          <div className=\"p-3 bg-green-50 border border-green-200 rounded-lg\">\n            <p className=\"text-sm font-medium text-green-800 mb-2\">Đáp án đúng:</p>\n            <div className=\"text-sm text-green-700\">\n              {question.correctAnswer.map((answer, index) => (\n                <span key={index} className=\"mr-4\">\n                  Chỗ trống {index + 1}: <strong>{answer}</strong>\n                </span>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {!hasPlayedAudio && (\n          <div className=\"text-amber-600 text-sm\">\n            ⚠️ Vui lòng nghe audio trước khi trả lời\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n\n// Speaking Record Component\nexport function SpeakingRecord({\n  question,\n  questionIndex,\n  answer,\n  onAnswerChange,\n  isSubmitted\n}: QuestionComponentProps) {\n  const handleRecordingComplete = (audioBlob: Blob, duration: number) => {\n    // Store the audio blob for later upload\n    onAnswerChange(`audio-blob-${Date.now()}`);\n  };\n\n  const handleUploadComplete = (audioUrl: string) => {\n    onAnswerChange(audioUrl);\n  };\n\n  return (\n    <Card className=\"w-full\">\n      <CardHeader>\n        <CardTitle className=\"text-lg\">\n          Câu {questionIndex + 1}: Ghi âm câu trả lời\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Question Text */}\n        <div className=\"p-4 bg-blue-50 rounded-lg\">\n          <p className=\"text-gray-800\">{question.question}</p>\n        </div>\n\n        {/* Audio Recorder */}\n        <AudioRecorder\n          questionId={question._id || `q-${questionIndex}`}\n          timeLimit={question.timeLimit || 120}\n          onRecordingComplete={handleRecordingComplete}\n          onUploadComplete={handleUploadComplete}\n          maxAttempts={3}\n          allowReRecord={!isSubmitted}\n          prompt=\"Hãy nói rõ ràng và tự nhiên. Bạn có thể chuẩn bị trong vài giây trước khi bắt đầu ghi âm.\"\n        />\n\n        {/* Instructions */}\n        <div className=\"p-3 bg-gray-50 rounded-lg\">\n          <h4 className=\"font-medium text-gray-700 mb-2\">Hướng dẫn:</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            <li>• Đọc kỹ câu hỏi trước khi ghi âm</li>\n            <li>• Nói rõ ràng và với tốc độ vừa phải</li>\n            <li>• Trả lời đầy đủ theo yêu cầu của câu hỏi</li>\n            <li>• Thời gian tối đa: {Math.floor((question.timeLimit || 120) / 60)} phút</li>\n          </ul>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n\n// Reading Comprehension Component\nexport function ReadingComprehension({\n  question,\n  questionIndex,\n  answer,\n  onAnswerChange,\n  isSubmitted,\n  showCorrectAnswer,\n  timeRemaining\n}: QuestionComponentProps) {\n  return (\n    <Card className=\"w-full\">\n      <CardHeader>\n        <CardTitle className=\"text-lg flex justify-between items-center\">\n          <span>Câu {questionIndex + 1}: Đọc hiểu</span>\n          {timeRemaining && timeRemaining > 0 && (\n            <div className=\"text-sm text-gray-500\">\n              Thời gian còn lại: {Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')}\n            </div>\n          )}\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Reading Passage */}\n        {question.readingPassage && (\n          <div className=\"p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500\">\n            <h4 className=\"font-medium text-gray-700 mb-3\">Đoạn văn:</h4>\n            <div className=\"text-gray-800 leading-relaxed whitespace-pre-line\">\n              {question.readingPassage}\n            </div>\n          </div>\n        )}\n\n        {/* Question */}\n        <div className=\"p-4 bg-blue-50 rounded-lg\">\n          <p className=\"text-gray-800\">{question.question}</p>\n        </div>\n\n        {/* Answer Options */}\n        {question.type === QuestionType.MULTIPLE_CHOICE && question.options && (\n          <RadioGroup\n            value={answer as string}\n            onValueChange={onAnswerChange}\n            disabled={isSubmitted}\n          >\n            {question.options.map((option, index) => {\n              const optionLetter = String.fromCharCode(65 + index);\n              const isCorrect = showCorrectAnswer && option === question.correctAnswer;\n              const isSelected = answer === option;\n              const isWrong = showCorrectAnswer && isSelected && !isCorrect;\n\n              return (\n                <div\n                  key={index}\n                  className={`flex items-center space-x-2 p-3 rounded-lg border ${\n                    isCorrect\n                      ? 'bg-green-50 border-green-200'\n                      : isWrong\n                      ? 'bg-red-50 border-red-200'\n                      : 'bg-white border-gray-200'\n                  }`}\n                >\n                  <RadioGroupItem value={option} id={`reading-${questionIndex}-${index}`} />\n                  <Label\n                    htmlFor={`reading-${questionIndex}-${index}`}\n                    className=\"flex-1 cursor-pointer\"\n                  >\n                    <span className=\"font-medium mr-2\">{optionLetter}.</span>\n                    {option}\n                  </Label>\n                </div>\n              );\n            })}\n          </RadioGroup>\n        )}\n\n        {question.type === QuestionType.TRUE_FALSE && (\n          <RadioGroup\n            value={answer as string}\n            onValueChange={onAnswerChange}\n            disabled={isSubmitted}\n          >\n            {['True', 'False'].map((option) => {\n              const isCorrect = showCorrectAnswer && option === question.correctAnswer;\n              const isSelected = answer === option;\n              const isWrong = showCorrectAnswer && isSelected && !isCorrect;\n\n              return (\n                <div\n                  key={option}\n                  className={`flex items-center space-x-2 p-3 rounded-lg border ${\n                    isCorrect\n                      ? 'bg-green-50 border-green-200'\n                      : isWrong\n                      ? 'bg-red-50 border-red-200'\n                      : 'bg-white border-gray-200'\n                  }`}\n                >\n                  <RadioGroupItem value={option} id={`tf-${questionIndex}-${option}`} />\n                  <Label\n                    htmlFor={`tf-${questionIndex}-${option}`}\n                    className=\"flex-1 cursor-pointer\"\n                  >\n                    {option === 'True' ? 'Đúng' : 'Sai'}\n                  </Label>\n                </div>\n              );\n            })}\n          </RadioGroup>\n        )}\n\n        {question.type === QuestionType.WRITING_SHORT_ANSWER && (\n          <div className=\"space-y-2\">\n            <Label htmlFor={`short-answer-${questionIndex}`}>Câu trả lời ngắn:</Label>\n            <Input\n              id={`short-answer-${questionIndex}`}\n              value={answer as string || ''}\n              onChange={(e) => onAnswerChange(e.target.value)}\n              disabled={isSubmitted}\n              placeholder=\"Nhập câu trả lời của bạn...\"\n              className={\n                showCorrectAnswer\n                  ? (answer as string)?.toLowerCase().trim() === (question.correctAnswer as string)?.toLowerCase().trim()\n                    ? 'border-green-500 bg-green-50'\n                    : 'border-red-500 bg-red-50'\n                  : ''\n              }\n            />\n            {showCorrectAnswer && (\n              <p className=\"text-sm text-green-600\">\n                Đáp án đúng: <strong>{question.correctAnswer as string}</strong>\n              </p>\n            )}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n\n// Writing Essay Component\nexport function WritingEssay({\n  question,\n  questionIndex,\n  answer,\n  onAnswerChange,\n  isSubmitted,\n  timeRemaining\n}: QuestionComponentProps) {\n  const [wordCount, setWordCount] = useState(0);\n  const [charCount, setCharCount] = useState(0);\n\n  useEffect(() => {\n    const text = (answer as string) || '';\n    setCharCount(text.length);\n    setWordCount(text.trim() ? text.trim().split(/\\s+/).length : 0);\n  }, [answer]);\n\n  const handleTextChange = (value: string) => {\n    onAnswerChange(value);\n  };\n\n  const minWords = question.wordLimit ? Math.floor(question.wordLimit * 0.8) : 150;\n  const maxWords = question.wordLimit || 300;\n  const isWordCountValid = wordCount >= minWords && wordCount <= maxWords;\n\n  return (\n    <Card className=\"w-full\">\n      <CardHeader>\n        <CardTitle className=\"text-lg flex justify-between items-center\">\n          <span>Câu {questionIndex + 1}: Viết luận</span>\n          {timeRemaining && timeRemaining > 0 && (\n            <div className=\"text-sm text-gray-500\">\n              Thời gian còn lại: {Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')}\n            </div>\n          )}\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Essay Prompt */}\n        <div className=\"p-4 bg-blue-50 rounded-lg\">\n          <h4 className=\"font-medium text-gray-700 mb-2\">Đề bài:</h4>\n          <p className=\"text-gray-800\">{question.question}</p>\n        </div>\n\n        {/* Writing Guidelines */}\n        <div className=\"p-3 bg-gray-50 rounded-lg\">\n          <h4 className=\"font-medium text-gray-700 mb-2\">Yêu cầu:</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            <li>• Số từ: {minWords} - {maxWords} từ</li>\n            <li>• Cấu trúc: Mở bài - Thân bài - Kết luận</li>\n            <li>• Sử dụng ngữ pháp và từ vựng chính xác</li>\n            <li>• Trình bày ý tưởng rõ ràng và logic</li>\n          </ul>\n        </div>\n\n        {/* Text Editor */}\n        <div className=\"space-y-2\">\n          <div className=\"flex justify-between items-center\">\n            <Label htmlFor={`essay-${questionIndex}`}>Bài viết của bạn:</Label>\n            <div className={`text-sm ${isWordCountValid ? 'text-green-600' : 'text-red-600'}`}>\n              {wordCount}/{maxWords} từ ({charCount} ký tự)\n            </div>\n          </div>\n          <Textarea\n            id={`essay-${questionIndex}`}\n            value={answer as string || ''}\n            onChange={(e) => handleTextChange(e.target.value)}\n            disabled={isSubmitted}\n            placeholder=\"Bắt đầu viết bài luận của bạn ở đây...\"\n            className={`min-h-[300px] resize-none ${\n              !isWordCountValid && wordCount > 0 ? 'border-red-500' : ''\n            }`}\n          />\n        </div>\n\n        {/* Word Count Progress */}\n        <div className=\"space-y-2\">\n          <div className=\"flex justify-between text-xs text-gray-500\">\n            <span>Tối thiểu: {minWords} từ</span>\n            <span>Tối đa: {maxWords} từ</span>\n          </div>\n          <Progress\n            value={(wordCount / maxWords) * 100}\n            className={`h-2 ${wordCount > maxWords ? 'bg-red-100' : ''}`}\n          />\n          {wordCount < minWords && (\n            <p className=\"text-xs text-amber-600\">\n              Cần thêm {minWords - wordCount} từ để đạt yêu cầu tối thiểu\n            </p>\n          )}\n          {wordCount > maxWords && (\n            <p className=\"text-xs text-red-600\">\n              Vượt quá {wordCount - maxWords} từ so với giới hạn\n            </p>\n          )}\n        </div>\n\n        {/* Writing Tips */}\n        <div className=\"p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\n          <h4 className=\"font-medium text-yellow-800 mb-2\">💡 Gợi ý viết bài:</h4>\n          <ul className=\"text-sm text-yellow-700 space-y-1\">\n            <li>• Mở bài: Giới thiệu chủ đề và quan điểm của bạn</li>\n            <li>• Thân bài: Trình bày các luận điểm với ví dụ cụ thể</li>\n            <li>• Kết luận: Tóm tắt và khẳng định lại quan điểm</li>\n            <li>• Kiểm tra lại chính tả và ngữ pháp trước khi nộp</li>\n          </ul>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAEA;;;;;;;;;;;;;;;;AAKA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;AA0BO,SAAS,wBAAwB,EACtC,QAAQ,EACR,aAAa,EACb,MAAM,EACN,cAAc,EACd,WAAW,EACX,iBAAiB,EACM;;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;wBAAU;wBACxB,gBAAgB;wBAAE;;;;;;;;;;;;0BAG3B,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,SAAS,QAAQ,kBAChB,6LAAC,4IAAA,CAAA,UAAW;wBACV,UAAU,SAAS,QAAQ;wBAC3B,OAAM;wBACN,QAAQ,IAAM,kBAAkB;wBAChC,aAAa;wBACb,YAAY;;;;;;kCAKhB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAiB,SAAS,QAAQ;;;;;;;;;;;kCAIjD,6LAAC;wBACC,OAAO;wBACP,eAAe;wBACf,UAAU;kCAET,SAAS,OAAO,EAAE,IAAI,CAAC,QAAQ;4BAC9B,MAAM,eAAe,OAAO,YAAY,CAAC,KAAK,QAAQ,aAAa;4BACnE,MAAM,YAAY,qBAAqB,WAAW,SAAS,aAAa;4BACxE,MAAM,aAAa,WAAW;4BAC9B,MAAM,UAAU,qBAAqB,cAAc,CAAC;4BAEpD,qBACE,6LAAC;gCAEC,WAAW,CAAC,kDAAkD,EAC5D,YACI,iCACA,UACA,6BACA,4BACJ;;kDAEF,6LAAC;wCAAe,OAAO;wCAAQ,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE,OAAO;;;;;;kDACrE,6LAAC;wCACC,SAAS,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE,OAAO;wCAC3C,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;;oDAAoB;oDAAa;;;;;;;4CAChD;;;;;;;;+BAfE;;;;;wBAmBX;;;;;;oBAGD,CAAC,gCACA,6LAAC;wBAAI,WAAU;kCAAyB;;;;;;;;;;;;;;;;;;AAOlD;GA9EgB;KAAA;AAiFT,SAAS,mBAAmB,EACjC,QAAQ,EACR,aAAa,EACb,MAAM,EACN,cAAc,EACd,WAAW,EACX,iBAAiB,EACM;;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,mDAAmD;YACnD,MAAM,aAAa,CAAC,SAAS,QAAQ,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,MAAM;YAChE,UAAU,IAAI,MAAM,YAAY,IAAI,CAAC;YAErC,yCAAyC;YACzC,IAAI,UAAU,MAAM,OAAO,CAAC,SAAS;gBACnC,UAAU;YACZ;QACF;uCAAG;QAAC;QAAU;KAAO;IAErB,MAAM,oBAAoB,CAAC,OAAe;QACxC,MAAM,YAAY;eAAI;SAAO;QAC7B,SAAS,CAAC,MAAM,GAAG;QACnB,UAAU;QACV,eAAe;IACjB;IAEA,MAAM,2BAA2B;QAC/B,MAAM,QAAQ,SAAS,QAAQ,CAAC,KAAK,CAAC;QACtC,MAAM,SAAS,EAAE;QAEjB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,OAAO,IAAI,eAAC,6LAAC;0BAAwB,KAAK,CAAC,EAAE;eAAtB,CAAC,KAAK,EAAE,GAAG;;;;;YAElC,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG;gBACxB,MAAM,gBAAgB,MAAM,OAAO,CAAC,SAAS,aAAa,IACtD,SAAS,aAAa,CAAC,EAAE,GACzB;gBACJ,MAAM,aAAa,MAAM,CAAC,EAAE,IAAI;gBAChC,MAAM,YAAY,qBAAqB,WAAW,WAAW,GAAG,IAAI,OAAO,cAAc,WAAW,GAAG,IAAI;gBAC3G,MAAM,UAAU,qBAAqB,cAAc,CAAC;gBAEpD,OAAO,IAAI,eACT,6LAAC,oIAAA,CAAA,QAAK;oBAEJ,OAAO,MAAM,CAAC,EAAE,IAAI;oBACpB,UAAU,CAAC,IAAM,kBAAkB,GAAG,EAAE,MAAM,CAAC,KAAK;oBACpD,UAAU;oBACV,WAAW,CAAC,mCAAmC,EAC7C,YACI,iCACA,UACA,6BACA,IACJ;oBACF,aAAY;mBAXP,CAAC,MAAM,EAAE,GAAG;;;;;YAcvB;QACF;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;wBAAU;wBACxB,gBAAgB;wBAAE;;;;;;;;;;;;0BAG3B,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,SAAS,QAAQ,kBAChB,6LAAC,4IAAA,CAAA,UAAW;wBACV,UAAU,SAAS,QAAQ;wBAC3B,OAAM;wBACN,QAAQ,IAAM,kBAAkB;wBAChC,aAAa;wBACb,YAAY;;;;;;kCAKhB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;oBAKJ,qBAAqB,MAAM,OAAO,CAAC,SAAS,aAAa,mBACxD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACZ,SAAS,aAAa,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACnC,6LAAC;wCAAiB,WAAU;;4CAAO;4CACtB,QAAQ;4CAAE;0DAAE,6LAAC;0DAAQ;;;;;;;uCADvB;;;;;;;;;;;;;;;;oBAQlB,CAAC,gCACA,6LAAC;wBAAI,WAAU;kCAAyB;;;;;;;;;;;;;;;;;;AAOlD;IAlHgB;MAAA;AAqHT,SAAS,eAAe,EAC7B,QAAQ,EACR,aAAa,EACb,MAAM,EACN,cAAc,EACd,WAAW,EACY;IACvB,MAAM,0BAA0B,CAAC,WAAiB;QAChD,wCAAwC;QACxC,eAAe,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI;IAC3C;IAEA,MAAM,uBAAuB,CAAC;QAC5B,eAAe;IACjB;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;wBAAU;wBACxB,gBAAgB;wBAAE;;;;;;;;;;;;0BAG3B,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAiB,SAAS,QAAQ;;;;;;;;;;;kCAIjD,6LAAC,8IAAA,CAAA,UAAa;wBACZ,YAAY,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,eAAe;wBAChD,WAAW,SAAS,SAAS,IAAI;wBACjC,qBAAqB;wBACrB,kBAAkB;wBAClB,aAAa;wBACb,eAAe,CAAC;wBAChB,QAAO;;;;;;kCAIT,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;;4CAAG;4CAAqB,KAAK,KAAK,CAAC,CAAC,SAAS,SAAS,IAAI,GAAG,IAAI;4CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlF;MArDgB;AAwDT,SAAS,qBAAqB,EACnC,QAAQ,EACR,aAAa,EACb,MAAM,EACN,cAAc,EACd,WAAW,EACX,iBAAiB,EACjB,aAAa,EACU;IACvB,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC;;gCAAK;gCAAK,gBAAgB;gCAAE;;;;;;;wBAC5B,iBAAiB,gBAAgB,mBAChC,6LAAC;4BAAI,WAAU;;gCAAwB;gCACjB,KAAK,KAAK,CAAC,gBAAgB;gCAAI;gCAAE,CAAC,gBAAgB,EAAE,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;;;;;;;;;;;;;0BAKzG,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,SAAS,cAAc,kBACtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAI,WAAU;0CACZ,SAAS,cAAc;;;;;;;;;;;;kCAM9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAiB,SAAS,QAAQ;;;;;;;;;;;oBAIhD,SAAS,IAAI,KAAK,wHAAA,CAAA,eAAY,CAAC,eAAe,IAAI,SAAS,OAAO,kBACjE,6LAAC;wBACC,OAAO;wBACP,eAAe;wBACf,UAAU;kCAET,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;4BAC7B,MAAM,eAAe,OAAO,YAAY,CAAC,KAAK;4BAC9C,MAAM,YAAY,qBAAqB,WAAW,SAAS,aAAa;4BACxE,MAAM,aAAa,WAAW;4BAC9B,MAAM,UAAU,qBAAqB,cAAc,CAAC;4BAEpD,qBACE,6LAAC;gCAEC,WAAW,CAAC,kDAAkD,EAC5D,YACI,iCACA,UACA,6BACA,4BACJ;;kDAEF,6LAAC;wCAAe,OAAO;wCAAQ,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,OAAO;;;;;;kDACtE,6LAAC;wCACC,SAAS,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,OAAO;wCAC5C,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;;oDAAoB;oDAAa;;;;;;;4CAChD;;;;;;;;+BAfE;;;;;wBAmBX;;;;;;oBAIH,SAAS,IAAI,KAAK,wHAAA,CAAA,eAAY,CAAC,UAAU,kBACxC,6LAAC;wBACC,OAAO;wBACP,eAAe;wBACf,UAAU;kCAET;4BAAC;4BAAQ;yBAAQ,CAAC,GAAG,CAAC,CAAC;4BACtB,MAAM,YAAY,qBAAqB,WAAW,SAAS,aAAa;4BACxE,MAAM,aAAa,WAAW;4BAC9B,MAAM,UAAU,qBAAqB,cAAc,CAAC;4BAEpD,qBACE,6LAAC;gCAEC,WAAW,CAAC,kDAAkD,EAC5D,YACI,iCACA,UACA,6BACA,4BACJ;;kDAEF,6LAAC;wCAAe,OAAO;wCAAQ,IAAI,CAAC,GAAG,EAAE,cAAc,CAAC,EAAE,QAAQ;;;;;;kDAClE,6LAAC;wCACC,SAAS,CAAC,GAAG,EAAE,cAAc,CAAC,EAAE,QAAQ;wCACxC,WAAU;kDAET,WAAW,SAAS,SAAS;;;;;;;+BAd3B;;;;;wBAkBX;;;;;;oBAIH,SAAS,IAAI,KAAK,wHAAA,CAAA,eAAY,CAAC,oBAAoB,kBAClD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,SAAS,CAAC,aAAa,EAAE,eAAe;0CAAE;;;;;;0CACjD,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAI,CAAC,aAAa,EAAE,eAAe;gCACnC,OAAO,UAAoB;gCAC3B,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,UAAU;gCACV,aAAY;gCACZ,WACE,oBACI,AAAC,QAAmB,cAAc,WAAY,SAAS,aAAa,EAAa,cAAc,SAC7F,iCACA,6BACF;;;;;;4BAGP,mCACC,6LAAC;gCAAE,WAAU;;oCAAyB;kDACvB,6LAAC;kDAAQ,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5D;MAzIgB;AA4IT,SAAS,aAAa,EAC3B,QAAQ,EACR,aAAa,EACb,MAAM,EACN,cAAc,EACd,WAAW,EACX,aAAa,EACU;;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,OAAO,AAAC,UAAqB;YACnC,aAAa,KAAK,MAAM;YACxB,aAAa,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,GAAG;QAC/D;iCAAG;QAAC;KAAO;IAEX,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,MAAM,WAAW,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC,SAAS,SAAS,GAAG,OAAO;IAC7E,MAAM,WAAW,SAAS,SAAS,IAAI;IACvC,MAAM,mBAAmB,aAAa,YAAY,aAAa;IAE/D,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC;;gCAAK;gCAAK,gBAAgB;gCAAE;;;;;;;wBAC5B,iBAAiB,gBAAgB,mBAChC,6LAAC;4BAAI,WAAU;;gCAAwB;gCACjB,KAAK,KAAK,CAAC,gBAAgB;gCAAI;gCAAE,CAAC,gBAAgB,EAAE,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;;;;;;;;;;;;;0BAKzG,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAE,WAAU;0CAAiB,SAAS,QAAQ;;;;;;;;;;;;kCAIjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;;4CAAG;4CAAU;4CAAS;4CAAI;4CAAS;;;;;;;kDACpC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;kCAKR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAS,CAAC,MAAM,EAAE,eAAe;kDAAE;;;;;;kDAC1C,6LAAC;wCAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,mBAAmB,gBAAgB;;4CAC9E;4CAAU;4CAAE;4CAAS;4CAAM;4CAAU;;;;;;;;;;;;;0CAG1C,6LAAC;gCACC,IAAI,CAAC,MAAM,EAAE,eAAe;gCAC5B,OAAO,UAAoB;gCAC3B,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAChD,UAAU;gCACV,aAAY;gCACZ,WAAW,CAAC,0BAA0B,EACpC,CAAC,oBAAoB,YAAY,IAAI,mBAAmB,IACxD;;;;;;;;;;;;kCAKN,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAK;4CAAY;4CAAS;;;;;;;kDAC3B,6LAAC;;4CAAK;4CAAS;4CAAS;;;;;;;;;;;;;0CAE1B,6LAAC,uIAAA,CAAA,WAAQ;gCACP,OAAO,AAAC,YAAY,WAAY;gCAChC,WAAW,CAAC,IAAI,EAAE,YAAY,WAAW,eAAe,IAAI;;;;;;4BAE7D,YAAY,0BACX,6LAAC;gCAAE,WAAU;;oCAAyB;oCAC1B,WAAW;oCAAU;;;;;;;4BAGlC,YAAY,0BACX,6LAAC;gCAAE,WAAU;;oCAAuB;oCACxB,YAAY;oCAAS;;;;;;;;;;;;;kCAMrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB;IA9GgB;MAAA", "debugId": null}}, {"offset": {"line": 4251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/app/tests/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { Clock, <PERSON><PERSON>Triangle, <PERSON><PERSON><PERSON>cle, Play, Pause, SkipForward } from \"lucide-react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Breadcrumb } from \"@/components/ui/breadcrumb\";\nimport { LoadingPage } from \"@/components/ui/loading\";\nimport { ErrorPage } from \"@/components/ui/error\";\nimport Header from \"@/components/layout/Header\";\nimport { toast } from \"sonner\";\nimport { QuestionType } from \"@/types\";\nimport {\n  ListeningMultipleChoice,\n  ListeningFillBlank,\n  SpeakingRecord,\n  ReadingComprehension,\n  WritingEssay\n} from \"@/components/test/QuestionComponents\";\n\n// Interface for test data from API\ninterface TestData {\n  _id: string;\n  title: string;\n  description: string;\n  type: string;\n  level: string;\n  duration: number;\n  totalQuestions: number;\n  passingScore: number;\n  instructions: string;\n  questions: Array<{\n    _id: string;\n    type: string;\n    question: string;\n    options?: string[];\n    correctAnswer: string | string[];\n    points: number;\n    order: number;\n    explanation?: string;\n  }>;\n  isPublished: boolean;\n  createdBy?: {\n    name: string;\n  };\n}\n\ninterface TestDetailPageProps {\n  params: {\n    id: string;\n  };\n}\n\nexport default function TestDetailPage({ params }: TestDetailPageProps) {\n  const router = useRouter();\n  const [test, setTest] = useState<TestData | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [hasStarted, setHasStarted] = useState(false);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState<Record<string, string>>({});\n  const [timeLeft, setTimeLeft] = useState(0); // in seconds\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [showConfirmSubmit, setShowConfirmSubmit] = useState(false);\n\n  // Load test data from API\n  useEffect(() => {\n    loadTest();\n  }, []);\n\n  const loadTest = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const { id } = await params;\n      const response = await fetch(`/api/tests/${id}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setTest(data.data);\n        setTimeLeft(data.data.duration * 60); // Convert minutes to seconds\n      } else {\n        setError(data.message || 'Không thể tải bài kiểm tra');\n      }\n    } catch (error) {\n      console.error('Load test error:', error);\n      setError('Lỗi kết nối. Vui lòng thử lại.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Timer countdown\n  useEffect(() => {\n    if (!hasStarted || timeLeft <= 0) return;\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => {\n        if (prev <= 1) {\n          handleAutoSubmit();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [hasStarted, timeLeft]);\n\n  const breadcrumbItems = [\n    { label: \"Luyện thi\", href: \"/tests\" },\n    { label: test?.title || \"Bài kiểm tra\", current: true }\n  ];\n\n  const formatTime = (seconds: number) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const getTimeColor = () => {\n    const percentage = (timeLeft / (test.duration * 60)) * 100;\n    if (percentage > 50) return \"text-green-600\";\n    if (percentage > 25) return \"text-yellow-600\";\n    return \"text-red-600\";\n  };\n\n  const handleStartTest = () => {\n    setHasStarted(true);\n    toast.success(\"Bài kiểm tra đã bắt đầu!\");\n  };\n\n  const handleAnswerChange = (questionId: string, answer: string) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  const handleNextQuestion = () => {\n    if (currentQuestion < test.questions.length - 1) {\n      setCurrentQuestion(prev => prev + 1);\n    }\n  };\n\n  const handlePreviousQuestion = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(prev => prev - 1);\n    }\n  };\n\n  const handleQuestionJump = (index: number) => {\n    setCurrentQuestion(index);\n  };\n\n  const handleAutoSubmit = () => {\n    toast.warning(\"Hết thời gian! Bài kiểm tra sẽ được nộp tự động.\");\n    submitTest();\n  };\n\n  const handleSubmitClick = () => {\n    const unansweredCount = test.questions.length - Object.keys(answers).length;\n    if (unansweredCount > 0) {\n      setShowConfirmSubmit(true);\n    } else {\n      submitTest();\n    }\n  };\n\n  const submitTest = async () => {\n    if (!test) return;\n\n    setIsSubmitting(true);\n\n    try {\n      const { id } = await params;\n      const response = await fetch(`/api/tests/${id}/submit`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          answers,\n          timeSpent: (test.duration * 60) - timeLeft,\n          startedAt: new Date(Date.now() - ((test.duration * 60) - timeLeft) * 1000)\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        toast.success(\"Nộp bài thành công!\");\n        router.push(`/tests/${id}/result`);\n      } else {\n        toast.error(data.message || \"Lỗi khi nộp bài\");\n      }\n    } catch (error) {\n      console.error(\"Submit error:\", error);\n      toast.error(\"Lỗi kết nối. Vui lòng thử lại.\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (isLoading) {\n    return <LoadingPage message=\"Đang tải bài kiểm tra...\" />;\n  }\n\n  if (error) {\n    return <ErrorPage title=\"Không thể tải bài kiểm tra\" message={error} />;\n  }\n\n  if (!test) {\n    return <ErrorPage title=\"Không tìm thấy bài kiểm tra\" message=\"Bài kiểm tra không tồn tại hoặc đã bị xóa\" />;\n  }\n\n  if (!hasStarted) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        \n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <Breadcrumb items={breadcrumbItems} className=\"mb-6\" />\n          \n          <Card>\n            <CardHeader className=\"text-center\">\n              <CardTitle className=\"text-2xl\">{test.title}</CardTitle>\n              <CardDescription>{test.description}</CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              {/* Test Info */}\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-center\">\n                <div className=\"p-4 bg-blue-50 rounded-lg\">\n                  <Clock className=\"h-8 w-8 text-blue-600 mx-auto mb-2\" />\n                  <div className=\"text-2xl font-bold text-blue-600\">{test.duration}</div>\n                  <div className=\"text-sm text-gray-600\">phút</div>\n                </div>\n                <div className=\"p-4 bg-green-50 rounded-lg\">\n                  <CheckCircle className=\"h-8 w-8 text-green-600 mx-auto mb-2\" />\n                  <div className=\"text-2xl font-bold text-green-600\">{test.totalQuestions}</div>\n                  <div className=\"text-sm text-gray-600\">câu hỏi</div>\n                </div>\n                <div className=\"p-4 bg-purple-50 rounded-lg\">\n                  <AlertTriangle className=\"h-8 w-8 text-purple-600 mx-auto mb-2\" />\n                  <div className=\"text-2xl font-bold text-purple-600\">{test.passingScore}%</div>\n                  <div className=\"text-sm text-gray-600\">điểm đạt</div>\n                </div>\n              </div>\n\n              {/* Instructions */}\n              <div className=\"prose max-w-none\">\n                <div dangerouslySetInnerHTML={{ __html: test.instructions }} />\n              </div>\n\n              {/* Start Button */}\n              <div className=\"text-center\">\n                <Button size=\"lg\" onClick={handleStartTest}>\n                  <Play className=\"h-5 w-5 mr-2\" />\n                  Bắt đầu làm bài\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQ = test.questions[currentQuestion];\n  const progress = ((currentQuestion + 1) / test.questions.length) * 100;\n  const answeredCount = Object.keys(answers).length;\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Test Header */}\n        <div className=\"bg-white rounded-lg shadow-sm p-6 mb-6\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">{test.title}</h1>\n              <p className=\"text-gray-600\">\n                Câu {currentQuestion + 1} / {test.questions.length}\n              </p>\n            </div>\n            \n            <div className=\"flex items-center gap-6\">\n              {/* Progress */}\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-sm text-gray-600\">Tiến độ:</span>\n                <div className=\"w-32\">\n                  <Progress value={progress} />\n                </div>\n                <span className=\"text-sm font-medium\">{Math.round(progress)}%</span>\n              </div>\n              \n              {/* Timer */}\n              <div className={`flex items-center gap-2 ${getTimeColor()}`}>\n                <Clock className=\"h-5 w-5\" />\n                <span className=\"text-lg font-mono font-bold\">\n                  {formatTime(timeLeft)}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n          {/* Question Navigation */}\n          <div className=\"lg:col-span-1\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-lg\">Danh sách câu hỏi</CardTitle>\n                <CardDescription>\n                  Đã trả lời: {answeredCount}/{test.questions.length}\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-5 lg:grid-cols-4 gap-2\">\n                  {test.questions.map((_, index) => (\n                    <button\n                      key={index}\n                      onClick={() => handleQuestionJump(index)}\n                      className={`\n                        w-10 h-10 rounded-lg text-sm font-medium transition-colors\n                        ${index === currentQuestion \n                          ? 'bg-blue-600 text-white' \n                          : answers[test.questions[index].id]\n                            ? 'bg-green-100 text-green-700 hover:bg-green-200'\n                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                        }\n                      `}\n                    >\n                      {index + 1}\n                    </button>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Question Content */}\n          <div className=\"lg:col-span-3\">\n            <Card>\n              <CardHeader>\n                <div className=\"flex items-center justify-between\">\n                  <CardTitle>Câu {currentQuestion + 1}</CardTitle>\n                  <Badge variant=\"outline\">{currentQ.points} điểm</Badge>\n                </div>\n              </CardHeader>\n              <CardContent className=\"space-y-6\">\n                {/* Enhanced Question Components */}\n                {currentQ.type === QuestionType.LISTENING_MULTIPLE_CHOICE && (\n                  <ListeningMultipleChoice\n                    question={currentQ}\n                    questionIndex={currentQuestion}\n                    answer={answers[currentQ._id]}\n                    onAnswerChange={(answer) => handleAnswerChange(currentQ._id, answer)}\n                    isSubmitted={false}\n                  />\n                )}\n\n                {currentQ.type === QuestionType.LISTENING_FILL_BLANK && (\n                  <ListeningFillBlank\n                    question={currentQ}\n                    questionIndex={currentQuestion}\n                    answer={answers[currentQ._id]}\n                    onAnswerChange={(answer) => handleAnswerChange(currentQ._id, answer)}\n                    isSubmitted={false}\n                  />\n                )}\n\n                {currentQ.type === QuestionType.SPEAKING_RECORD && (\n                  <SpeakingRecord\n                    question={currentQ}\n                    questionIndex={currentQuestion}\n                    answer={answers[currentQ._id]}\n                    onAnswerChange={(answer) => handleAnswerChange(currentQ._id, answer)}\n                    isSubmitted={false}\n                  />\n                )}\n\n                {(currentQ.type === QuestionType.READING_COMPREHENSION ||\n                  currentQ.type === QuestionType.MULTIPLE_CHOICE ||\n                  currentQ.type === QuestionType.TRUE_FALSE) && (\n                  <ReadingComprehension\n                    question={currentQ}\n                    questionIndex={currentQuestion}\n                    answer={answers[currentQ._id]}\n                    onAnswerChange={(answer) => handleAnswerChange(currentQ._id, answer)}\n                    isSubmitted={false}\n                    timeRemaining={timeLeft}\n                  />\n                )}\n\n                {(currentQ.type === QuestionType.WRITING_ESSAY ||\n                  currentQ.type === QuestionType.ESSAY) && (\n                  <WritingEssay\n                    question={currentQ}\n                    questionIndex={currentQuestion}\n                    answer={answers[currentQ._id]}\n                    onAnswerChange={(answer) => handleAnswerChange(currentQ._id, answer)}\n                    isSubmitted={false}\n                    timeRemaining={timeLeft}\n                  />\n                )}\n\n                {/* Fallback for legacy question types */}\n                {currentQ.type === 'multiple_choice' && (\n                  <div className=\"space-y-4\">\n                    <div className=\"p-4 bg-blue-50 rounded-lg\">\n                      <p className=\"text-gray-800\">{currentQ.question}</p>\n                    </div>\n                    <div className=\"space-y-3\">\n                      {currentQ.options?.map((option, index) => (\n                        <label\n                          key={index}\n                          className={`\n                            flex items-center p-4 border rounded-lg cursor-pointer transition-colors\n                            ${answers[currentQ._id] === option\n                              ? 'border-blue-500 bg-blue-50'\n                              : 'border-gray-200 hover:border-gray-300'\n                            }\n                          `}\n                        >\n                          <input\n                            type=\"radio\"\n                            name={`question-${currentQ._id}`}\n                            value={option}\n                            checked={answers[currentQ._id] === option}\n                            onChange={(e) => handleAnswerChange(currentQ._id, e.target.value)}\n                            className=\"mr-3\"\n                          />\n                          <span className=\"text-gray-900\">{option}</span>\n                        </label>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Navigation */}\n                <div className=\"flex items-center justify-between pt-6 border-t\">\n                  <Button\n                    variant=\"outline\"\n                    onClick={handlePreviousQuestion}\n                    disabled={currentQuestion === 0}\n                  >\n                    Câu trước\n                  </Button>\n                  \n                  <div className=\"flex gap-2\">\n                    {currentQuestion === test.questions.length - 1 ? (\n                      <Button onClick={handleSubmitClick} disabled={isSubmitting}>\n                        {isSubmitting ? \"Đang nộp bài...\" : \"Nộp bài\"}\n                      </Button>\n                    ) : (\n                      <Button onClick={handleNextQuestion}>\n                        Câu tiếp theo\n                        <SkipForward className=\"h-4 w-4 ml-2\" />\n                      </Button>\n                    )}\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n\n        {/* Confirm Submit Dialog */}\n        {showConfirmSubmit && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n            <Card className=\"w-full max-w-md mx-4\">\n              <CardHeader>\n                <CardTitle>Xác nhận nộp bài</CardTitle>\n                <CardDescription>\n                  Bạn còn {test.questions.length - answeredCount} câu chưa trả lời. \n                  Bạn có chắc chắn muốn nộp bài?\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"flex gap-3\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setShowConfirmSubmit(false)}\n                  className=\"flex-1\"\n                >\n                  Tiếp tục làm bài\n                </Button>\n                <Button\n                  onClick={() => {\n                    setShowConfirmSubmit(false);\n                    submitTest();\n                  }}\n                  className=\"flex-1\"\n                >\n                  Nộp bài\n                </Button>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;;;;;;AAwDe,SAAS,eAAe,EAAE,MAAM,EAAuB;;IACpE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,aAAa;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG,EAAE;IAEL,MAAM,WAAW;QACf,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;YACrB,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,IAAI;YAC/C,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,QAAQ,KAAK,IAAI;gBACjB,YAAY,KAAK,IAAI,CAAC,QAAQ,GAAG,KAAK,6BAA6B;YACrE,OAAO;gBACL,SAAS,KAAK,OAAO,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,kBAAkB;IAClB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,cAAc,YAAY,GAAG;YAElC,MAAM,QAAQ;kDAAY;oBACxB;0DAAY,CAAA;4BACV,IAAI,QAAQ,GAAG;gCACb;gCACA,OAAO;4BACT;4BACA,OAAO,OAAO;wBAChB;;gBACF;iDAAG;YAEH;4CAAO,IAAM,cAAc;;QAC7B;mCAAG;QAAC;QAAY;KAAS;IAEzB,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAa,MAAM;QAAS;QACrC;YAAE,OAAO,MAAM,SAAS;YAAgB,SAAS;QAAK;KACvD;IAED,MAAM,aAAa,CAAC;QAClB,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,mBAAmB,UAAU;QACnC,OAAO,GAAG,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACjG;IAEA,MAAM,eAAe;QACnB,MAAM,aAAa,AAAC,WAAW,CAAC,KAAK,QAAQ,GAAG,EAAE,IAAK;QACvD,IAAI,aAAa,IAAI,OAAO;QAC5B,IAAI,aAAa,IAAI,OAAO;QAC5B,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,cAAc;QACd,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;IACH;IAEA,MAAM,qBAAqB;QACzB,IAAI,kBAAkB,KAAK,SAAS,CAAC,MAAM,GAAG,GAAG;YAC/C,mBAAmB,CAAA,OAAQ,OAAO;QACpC;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,kBAAkB,GAAG;YACvB,mBAAmB,CAAA,OAAQ,OAAO;QACpC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;IACrB;IAEA,MAAM,mBAAmB;QACvB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,kBAAkB,KAAK,SAAS,CAAC,MAAM,GAAG,OAAO,IAAI,CAAC,SAAS,MAAM;QAC3E,IAAI,kBAAkB,GAAG;YACvB,qBAAqB;QACvB,OAAO;YACL;QACF;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM;QAEX,gBAAgB;QAEhB,IAAI;YACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;YACrB,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,EAAE;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,WAAW,AAAC,KAAK,QAAQ,GAAG,KAAM;oBAClC,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,CAAC,AAAC,KAAK,QAAQ,GAAG,KAAM,QAAQ,IAAI;gBACvE;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC;YACnC,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,OAAO,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,WAAW;QACb,qBAAO,6LAAC,sIAAA,CAAA,cAAW;YAAC,SAAQ;;;;;;IAC9B;IAEA,IAAI,OAAO;QACT,qBAAO,6LAAC,oIAAA,CAAA,YAAS;YAAC,OAAM;YAA6B,SAAS;;;;;;IAChE;IAEA,IAAI,CAAC,MAAM;QACT,qBAAO,6LAAC,oIAAA,CAAA,YAAS;YAAC,OAAM;YAA8B,SAAQ;;;;;;IAChE;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;8BAEP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yIAAA,CAAA,aAAU;4BAAC,OAAO;4BAAiB,WAAU;;;;;;sCAE9C,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAY,KAAK,KAAK;;;;;;sDAC3C,6LAAC,mIAAA,CAAA,kBAAe;sDAAE,KAAK,WAAW;;;;;;;;;;;;8CAEpC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAI,WAAU;sEAAoC,KAAK,QAAQ;;;;;;sEAChE,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;4DAAI,WAAU;sEAAqC,KAAK,cAAc;;;;;;sEACvE,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;sEACzB,6LAAC;4DAAI,WAAU;;gEAAsC,KAAK,YAAY;gEAAC;;;;;;;sEACvE,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAK3C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,yBAAyB;oDAAE,QAAQ,KAAK,YAAY;gDAAC;;;;;;;;;;;sDAI5D,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAS;;kEACzB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASjD;IAEA,MAAM,WAAW,KAAK,SAAS,CAAC,gBAAgB;IAChD,MAAM,WAAW,AAAC,CAAC,kBAAkB,CAAC,IAAI,KAAK,SAAS,CAAC,MAAM,GAAI;IACnE,MAAM,gBAAgB,OAAO,IAAI,CAAC,SAAS,MAAM;IAEjD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoC,KAAK,KAAK;;;;;;sDAC5D,6LAAC;4CAAE,WAAU;;gDAAgB;gDACtB,kBAAkB;gDAAE;gDAAI,KAAK,SAAS,CAAC,MAAM;;;;;;;;;;;;;8CAItD,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,OAAO;;;;;;;;;;;8DAEnB,6LAAC;oDAAK,WAAU;;wDAAuB,KAAK,KAAK,CAAC;wDAAU;;;;;;;;;;;;;sDAI9D,6LAAC;4CAAI,WAAW,CAAC,wBAAwB,EAAE,gBAAgB;;8DACzD,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DACb,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOtB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;8DAC/B,6LAAC,mIAAA,CAAA,kBAAe;;wDAAC;wDACF;wDAAc;wDAAE,KAAK,SAAS,CAAC,MAAM;;;;;;;;;;;;;sDAGtD,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;0DACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,sBACtB,6LAAC;wDAEC,SAAS,IAAM,mBAAmB;wDAClC,WAAW,CAAC;;wBAEV,EAAE,UAAU,kBACR,2BACA,OAAO,CAAC,KAAK,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,GAC/B,mDACA,8CACL;sBACH,CAAC;kEAEA,QAAQ;uDAZJ;;;;;;;;;;;;;;;;;;;;;;;;;;0CAqBjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,mIAAA,CAAA,YAAS;;4DAAC;4DAAK,kBAAkB;;;;;;;kEAClC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;;4DAAW,SAAS,MAAM;4DAAC;;;;;;;;;;;;;;;;;;sDAG9C,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;gDAEpB,SAAS,IAAI,KAAK,wHAAA,CAAA,eAAY,CAAC,yBAAyB,kBACvD,6LAAC,mJAAA,CAAA,0BAAuB;oDACtB,UAAU;oDACV,eAAe;oDACf,QAAQ,OAAO,CAAC,SAAS,GAAG,CAAC;oDAC7B,gBAAgB,CAAC,SAAW,mBAAmB,SAAS,GAAG,EAAE;oDAC7D,aAAa;;;;;;gDAIhB,SAAS,IAAI,KAAK,wHAAA,CAAA,eAAY,CAAC,oBAAoB,kBAClD,6LAAC,mJAAA,CAAA,qBAAkB;oDACjB,UAAU;oDACV,eAAe;oDACf,QAAQ,OAAO,CAAC,SAAS,GAAG,CAAC;oDAC7B,gBAAgB,CAAC,SAAW,mBAAmB,SAAS,GAAG,EAAE;oDAC7D,aAAa;;;;;;gDAIhB,SAAS,IAAI,KAAK,wHAAA,CAAA,eAAY,CAAC,eAAe,kBAC7C,6LAAC,mJAAA,CAAA,iBAAc;oDACb,UAAU;oDACV,eAAe;oDACf,QAAQ,OAAO,CAAC,SAAS,GAAG,CAAC;oDAC7B,gBAAgB,CAAC,SAAW,mBAAmB,SAAS,GAAG,EAAE;oDAC7D,aAAa;;;;;;gDAIhB,CAAC,SAAS,IAAI,KAAK,wHAAA,CAAA,eAAY,CAAC,qBAAqB,IACpD,SAAS,IAAI,KAAK,wHAAA,CAAA,eAAY,CAAC,eAAe,IAC9C,SAAS,IAAI,KAAK,wHAAA,CAAA,eAAY,CAAC,UAAU,mBACzC,6LAAC,mJAAA,CAAA,uBAAoB;oDACnB,UAAU;oDACV,eAAe;oDACf,QAAQ,OAAO,CAAC,SAAS,GAAG,CAAC;oDAC7B,gBAAgB,CAAC,SAAW,mBAAmB,SAAS,GAAG,EAAE;oDAC7D,aAAa;oDACb,eAAe;;;;;;gDAIlB,CAAC,SAAS,IAAI,KAAK,wHAAA,CAAA,eAAY,CAAC,aAAa,IAC5C,SAAS,IAAI,KAAK,wHAAA,CAAA,eAAY,CAAC,KAAK,mBACpC,6LAAC,mJAAA,CAAA,eAAY;oDACX,UAAU;oDACV,eAAe;oDACf,QAAQ,OAAO,CAAC,SAAS,GAAG,CAAC;oDAC7B,gBAAgB,CAAC,SAAW,mBAAmB,SAAS,GAAG,EAAE;oDAC7D,aAAa;oDACb,eAAe;;;;;;gDAKlB,SAAS,IAAI,KAAK,mCACjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;0EAAiB,SAAS,QAAQ;;;;;;;;;;;sEAEjD,6LAAC;4DAAI,WAAU;sEACZ,SAAS,OAAO,EAAE,IAAI,CAAC,QAAQ,sBAC9B,6LAAC;oEAEC,WAAW,CAAC;;4BAEV,EAAE,OAAO,CAAC,SAAS,GAAG,CAAC,KAAK,SACxB,+BACA,wCACH;0BACH,CAAC;;sFAED,6LAAC;4EACC,MAAK;4EACL,MAAM,CAAC,SAAS,EAAE,SAAS,GAAG,EAAE;4EAChC,OAAO;4EACP,SAAS,OAAO,CAAC,SAAS,GAAG,CAAC,KAAK;4EACnC,UAAU,CAAC,IAAM,mBAAmB,SAAS,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;4EAChE,WAAU;;;;;;sFAEZ,6LAAC;4EAAK,WAAU;sFAAiB;;;;;;;mEAjB5B;;;;;;;;;;;;;;;;8DAyBf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS;4DACT,UAAU,oBAAoB;sEAC/B;;;;;;sEAID,6LAAC;4DAAI,WAAU;sEACZ,oBAAoB,KAAK,SAAS,CAAC,MAAM,GAAG,kBAC3C,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAS;gEAAmB,UAAU;0EAC3C,eAAe,oBAAoB;;;;;qFAGtC,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAS;;oEAAoB;kFAEnC,6LAAC,uNAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWtC,mCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;;gDAAC;gDACN,KAAK,SAAS,CAAC,MAAM,GAAG;gDAAc;;;;;;;;;;;;;8CAInD,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,qBAAqB;4CACpC,WAAU;sDACX;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;gDACP,qBAAqB;gDACrB;4CACF;4CACA,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GApcwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}