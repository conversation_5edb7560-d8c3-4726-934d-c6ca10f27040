"use client";

import { useState } from "react";
import { Download, Share2, Award, Calendar, Clock, User, BookOpen, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

interface CertificateData {
  id: string;
  studentName: string;
  studentEmail: string;
  courseName: string;
  instructorName: string;
  completedAt: Date;
  issuedAt: Date;
  duration: number; // in hours
  totalTimeSpent: number; // in hours
  verificationUrl: string;
}

interface CertificateViewerProps {
  certificateData: CertificateData;
  onDownload?: () => void;
  onShare?: () => void;
}

export default function CertificateViewer({ 
  certificateData, 
  onDownload, 
  onShare 
}: CertificateViewerProps) {
  const [isGenerating, setIsGenerating] = useState(false);

  const handleDownload = async () => {
    if (onDownload) {
      setIsGenerating(true);
      try {
        await onDownload();
        toast.success("Đ<PERSON> tải xuống chứng chỉ");
      } catch (error) {
        toast.error("Lỗi khi tải xuống chứng chỉ");
      } finally {
        setIsGenerating(false);
      }
    }
  };

  const handleShare = () => {
    if (onShare) {
      onShare();
    } else {
      // Default share functionality
      if (navigator.share) {
        navigator.share({
          title: `Chứng chỉ hoàn thành khóa học: ${certificateData.courseName}`,
          text: `Tôi đã hoàn thành khóa học "${certificateData.courseName}" và nhận được chứng chỉ!`,
          url: certificateData.verificationUrl
        });
      } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(certificateData.verificationUrl);
        toast.success("Đã sao chép link xác thực vào clipboard");
      }
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Certificate Preview */}
      <Card className="bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200">
        <CardContent className="p-8">
          <div className="text-center space-y-6">
            {/* Header */}
            <div className="space-y-2">
              <div className="flex justify-center">
                <Award className="h-16 w-16 text-yellow-500" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900">CHỨNG CHỈ HOÀN THÀNH</h1>
              <p className="text-gray-600">English Learning Platform</p>
            </div>

            {/* Main Content */}
            <div className="space-y-4">
              <p className="text-lg text-gray-700">Chứng nhận rằng</p>
              
              <h2 className="text-4xl font-bold text-blue-900 border-b-2 border-blue-300 pb-2 inline-block">
                {certificateData.studentName}
              </h2>
              
              <p className="text-lg text-gray-700">đã hoàn thành xuất sắc khóa học</p>
              
              <h3 className="text-2xl font-semibold text-gray-800 max-w-2xl mx-auto">
                {certificateData.courseName}
              </h3>
            </div>

            {/* Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8 pt-6 border-t border-blue-200">
              <div className="space-y-2">
                <div className="flex items-center justify-center gap-2 text-gray-600">
                  <Calendar className="h-4 w-4" />
                  <span className="text-sm">Ngày hoàn thành</span>
                </div>
                <p className="font-medium">{formatDate(certificateData.completedAt)}</p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-center gap-2 text-gray-600">
                  <User className="h-4 w-4" />
                  <span className="text-sm">Giảng viên</span>
                </div>
                <p className="font-medium">{certificateData.instructorName}</p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-center gap-2 text-gray-600">
                  <Clock className="h-4 w-4" />
                  <span className="text-sm">Thời lượng khóa học</span>
                </div>
                <p className="font-medium">{certificateData.duration} giờ</p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-center gap-2 text-gray-600">
                  <BookOpen className="h-4 w-4" />
                  <span className="text-sm">Thời gian học thực tế</span>
                </div>
                <p className="font-medium">{certificateData.totalTimeSpent} giờ</p>
              </div>
            </div>

            {/* Certificate ID */}
            <div className="pt-6 border-t border-blue-200">
              <p className="text-xs text-gray-500">Mã chứng chỉ: {certificateData.id}</p>
              <p className="text-xs text-gray-500">
                Xác thực tại: {certificateData.verificationUrl}
              </p>
            </div>

            {/* Signature Area */}
            <div className="pt-6">
              <div className="flex justify-center">
                <div className="text-center">
                  <div className="w-32 border-b border-gray-400 mb-2"></div>
                  <p className="text-sm text-gray-600">Chữ ký điện tử</p>
                  <p className="text-xs text-gray-500">English Learning Platform</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Certificate Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
            Thông tin chứng chỉ
          </CardTitle>
          <CardDescription>
            Chi tiết về chứng chỉ hoàn thành khóa học của bạn
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-600">Người nhận</label>
                <p className="text-gray-900">{certificateData.studentName}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600">Email</label>
                <p className="text-gray-900">{certificateData.studentEmail}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600">Khóa học</label>
                <p className="text-gray-900">{certificateData.courseName}</p>
              </div>
            </div>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-600">Ngày hoàn thành</label>
                <p className="text-gray-900">{formatDate(certificateData.completedAt)}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600">Ngày cấp chứng chỉ</label>
                <p className="text-gray-900">{formatDate(certificateData.issuedAt)}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600">Trạng thái</label>
                <Badge variant="default" className="ml-2">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Đã xác thực
                </Badge>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
            <Button onClick={handleDownload} disabled={isGenerating} className="flex-1">
              <Download className="h-4 w-4 mr-2" />
              {isGenerating ? 'Đang tạo PDF...' : 'Tải xuống PDF'}
            </Button>
            
            <Button variant="outline" onClick={handleShare} className="flex-1">
              <Share2 className="h-4 w-4 mr-2" />
              Chia sẻ chứng chỉ
            </Button>
          </div>

          {/* Verification Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Xác thực chứng chỉ</h4>
            <p className="text-sm text-blue-800 mb-2">
              Chứng chỉ này có thể được xác thực bằng cách truy cập link sau:
            </p>
            <div className="flex items-center gap-2">
              <code className="flex-1 p-2 bg-white rounded text-xs text-blue-900 border">
                {certificateData.verificationUrl}
              </code>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  navigator.clipboard.writeText(certificateData.verificationUrl);
                  toast.success("Đã sao chép link");
                }}
              >
                Sao chép
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
