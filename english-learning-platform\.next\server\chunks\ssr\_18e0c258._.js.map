{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('vi-VN', {\n    style: 'currency',\n    currency: 'VND'\n  }).format(price);\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('vi-VN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  }).format(dateObj);\n}\n\nexport function formatDuration(minutes: number): string {\n  const hours = Math.floor(minutes / 60);\n  const remainingMinutes = minutes % 60;\n\n  if (hours === 0) {\n    return `${remainingMinutes} phút`;\n  } else if (remainingMinutes === 0) {\n    return `${hours} giờ`;\n  } else {\n    return `${hours} giờ ${remainingMinutes} phút`;\n  }\n}\n\nexport function calculateProgress(completedLessons: number[], totalLessons: number): number {\n  if (totalLessons === 0) return 0;\n  return Math.round((completedLessons.length / totalLessons) * 100);\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2);\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '') // Remove diacritics\n    .replace(/[^a-z0-9 -]/g, '') // Remove special characters\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen\n    .trim();\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n\n  if (password.length < 6) {\n    errors.push('Mật khẩu phải có ít nhất 6 ký tự');\n  }\n\n  if (!/[A-Z]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 chữ hoa');\n  }\n\n  if (!/[a-z]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 chữ thường');\n  }\n\n  if (!/[0-9]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 số');\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\nexport function generateRandomString(length: number): string {\n  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += characters.charAt(Math.floor(Math.random() * characters.length));\n  }\n  return result;\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,mBAAmB,UAAU;IAEnC,IAAI,UAAU,GAAG;QACf,OAAO,GAAG,iBAAiB,KAAK,CAAC;IACnC,OAAO,IAAI,qBAAqB,GAAG;QACjC,OAAO,GAAG,MAAM,IAAI,CAAC;IACvB,OAAO;QACL,OAAO,GAAG,MAAM,KAAK,EAAE,iBAAiB,KAAK,CAAC;IAChD;AACF;AAEO,SAAS,kBAAkB,gBAA0B,EAAE,YAAoB;IAChF,IAAI,iBAAiB,GAAG,OAAO;IAC/B,OAAO,KAAK,KAAK,CAAC,AAAC,iBAAiB,MAAM,GAAG,eAAgB;AAC/D;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAAI,oBAAoB;KACpD,OAAO,CAAC,gBAAgB,IAAI,4BAA4B;KACxD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,OAAO,KAAK,8CAA8C;KAClE,IAAI;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,qBAAqB,MAAc;IACjD,MAAM,aAAa;IACnB,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,WAAW,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM;IAC1E;IACA,OAAO;AACT;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/app/about/page.tsx"], "sourcesContent": ["import { Users, Target, Award, BookOpen, Heart, Globe, Zap, Shield } from \"lucide-react\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport Header from \"@/components/layout/Header\";\n\nconst teamMembers = [\n  {\n    name: \"<PERSON><PERSON><PERSON><PERSON>\",\n    role: \"Founder & CEO\",\n    bio: \"10+ năm kinh nghiệm trong lĩnh vực giáo dục tiếng <PERSON>h. Tốt nghiệp Thạc sĩ <PERSON>ôn ngữ học tại Đại học Cambridge.\",\n    avatar: \"/api/placeholder/150/150\",\n    specialties: [\"IELTS\", \"TOEFL\", \"Business English\"]\n  },\n  {\n    name: \"<PERSON><PERSON><PERSON><PERSON>\",\n    role: \"Head of Curriculum\",\n    bio: \"Chuyên gia phát triển chương trình học với 8 năm kinh nghiệm. Từng làm việc tại British Council.\",\n    avatar: \"/api/placeholder/150/150\",\n    specialties: [\"Curriculum Design\", \"Assessment\", \"Teacher Training\"]\n  },\n  {\n    name: \"<PERSON><PERSON><PERSON>\",\n    role: \"Lead Teacher\",\n    bio: \"Giáo viên tiếng Anh xuất sắc với chứng chỉ CELTA và DELTA. Chuyên về giao tiếp và phát âm.\",\n    avatar: \"/api/placeholder/150/150\",\n    specialties: [\"Speaking\", \"Pronunciation\", \"Communication\"]\n  },\n  {\n    name: \"Phạm Văn Nam\",\n    role: \"Technology Director\",\n    bio: \"Chuyên gia công nghệ giáo dục với 12 năm kinh nghiệm phát triển các nền tảng học trực tuyến.\",\n    avatar: \"/api/placeholder/150/150\",\n    specialties: [\"EdTech\", \"AI in Education\", \"Platform Development\"]\n  }\n];\n\nconst values = [\n  {\n    icon: Target,\n    title: \"Chất lượng hàng đầu\",\n    description: \"Cam kết mang đến những khóa học chất lượng cao nhất với phương pháp giảng dạy hiện đại và hiệu quả.\"\n  },\n  {\n    icon: Heart,\n    title: \"Tận tâm với học viên\",\n    description: \"Đặt học viên làm trung tâm, luôn lắng nghe và hỗ trợ tối đa để giúp mọi người đạt được mục tiêu.\"\n  },\n  {\n    icon: Zap,\n    title: \"Đổi mới sáng tạo\",\n    description: \"Không ngừng cải tiến và ứng dụng công nghệ mới để tạo ra trải nghiệm học tập tốt nhất.\"\n  },\n  {\n    icon: Shield,\n    title: \"Uy tín và minh bạch\",\n    description: \"Xây dựng niềm tin thông qua sự minh bạch trong chương trình học và kết quả đào tạo.\"\n  }\n];\n\nconst achievements = [\n  {\n    number: \"10,000+\",\n    label: \"Học viên đã tin tưởng\",\n    description: \"Hơn 10,000 học viên đã học tập và đạt được mục tiêu tiếng Anh\"\n  },\n  {\n    number: \"95%\",\n    label: \"Tỷ lệ hài lòng\",\n    description: \"95% học viên hài lòng với chất lượng khóa học và dịch vụ\"\n  },\n  {\n    number: \"200+\",\n    label: \"Khóa học đa dạng\",\n    description: \"Hơn 200 khóa học từ cơ bản đến nâng cao cho mọi đối tượng\"\n  },\n  {\n    number: \"50+\",\n    label: \"Giáo viên chuyên nghiệp\",\n    description: \"Đội ngũ 50+ giáo viên có trình độ cao và kinh nghiệm phong phú\"\n  }\n];\n\nexport default function AboutPage() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Hero Section */}\n        <div className=\"text-center mb-16\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-6\">\n            Về English Learning Platform\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Chúng tôi là nền tảng học tiếng Anh trực tuyến hàng đầu Việt Nam, \n            cam kết mang đến trải nghiệm học tập chất lượng cao và hiệu quả nhất \n            cho mọi học viên.\n          </p>\n        </div>\n\n        {/* Mission & Vision */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Target className=\"h-6 w-6 mr-2 text-blue-600\" />\n                Sứ mệnh\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-gray-700 leading-relaxed\">\n                Democratize English education bằng cách cung cấp các khóa học tiếng Anh \n                chất lượng cao, dễ tiếp cận và phù hợp với mọi đối tượng học viên. \n                Chúng tôi tin rằng việc thành thạo tiếng Anh sẽ mở ra nhiều cơ hội \n                và thay đổi cuộc sống của mọi người.\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Globe className=\"h-6 w-6 mr-2 text-green-600\" />\n                Tầm nhìn\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-gray-700 leading-relaxed\">\n                Trở thành nền tảng học tiếng Anh trực tuyến số 1 tại Việt Nam và \n                khu vực Đông Nam Á. Chúng tôi hướng tới việc tạo ra một cộng đồng \n                học tập toàn cầu nơi mọi người có thể học tiếng Anh một cách hiệu quả \n                và thú vị.\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Core Values */}\n        <div className=\"mb-16\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Giá trị cốt lõi</h2>\n            <p className=\"text-lg text-gray-600\">\n              Những giá trị định hướng mọi hoạt động của chúng tôi\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {values.map((value, index) => (\n              <Card key={index} className=\"text-center\">\n                <CardHeader>\n                  <div className=\"mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\">\n                    <value.icon className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  <CardTitle className=\"text-lg\">{value.title}</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <p className=\"text-gray-600 text-sm\">{value.description}</p>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n\n        {/* Achievements */}\n        <div className=\"bg-blue-600 rounded-2xl p-8 mb-16\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-white mb-4\">Thành tựu của chúng tôi</h2>\n            <p className=\"text-blue-100\">\n              Những con số ấn tượng sau hành trình phát triển\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {achievements.map((achievement, index) => (\n              <div key={index} className=\"text-center text-white\">\n                <div className=\"text-4xl font-bold mb-2\">{achievement.number}</div>\n                <div className=\"text-xl font-semibold mb-2\">{achievement.label}</div>\n                <div className=\"text-blue-100 text-sm\">{achievement.description}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Team Section */}\n        <div className=\"mb-16\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Đội ngũ của chúng tôi</h2>\n            <p className=\"text-lg text-gray-600\">\n              Những chuyên gia tâm huyết và giàu kinh nghiệm\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {teamMembers.map((member, index) => (\n              <Card key={index} className=\"text-center\">\n                <CardHeader>\n                  <div className=\"w-24 h-24 bg-gray-300 rounded-full mx-auto mb-4\"></div>\n                  <CardTitle className=\"text-lg\">{member.name}</CardTitle>\n                  <CardDescription className=\"text-blue-600 font-medium\">\n                    {member.role}\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <p className=\"text-gray-600 text-sm\">{member.bio}</p>\n                  <div className=\"flex flex-wrap gap-1 justify-center\">\n                    {member.specialties.map((specialty, idx) => (\n                      <Badge key={idx} variant=\"secondary\" className=\"text-xs\">\n                        {specialty}\n                      </Badge>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n\n        {/* Story Section */}\n        <Card className=\"mb-16\">\n          <CardHeader>\n            <CardTitle className=\"text-2xl text-center\">Câu chuyện của chúng tôi</CardTitle>\n          </CardHeader>\n          <CardContent className=\"prose max-w-none\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center\">\n              <div>\n                <p className=\"text-gray-700 mb-4\">\n                  English Learning Platform được thành lập vào năm 2020 với mong muốn \n                  cách mạng hóa cách học tiếng Anh tại Việt Nam. Xuất phát từ nhận thức \n                  rằng nhiều người Việt gặp khó khăn trong việc tiếp cận các khóa học \n                  tiếng Anh chất lượng cao.\n                </p>\n                <p className=\"text-gray-700 mb-4\">\n                  Chúng tôi đã tập hợp đội ngũ các chuyên gia giáo dục và công nghệ \n                  để xây dựng một nền tảng học tập hiện đại, tương tác và hiệu quả. \n                  Với phương pháp giảng dạy dựa trên nghiên cứu khoa học và công nghệ \n                  AI tiên tiến.\n                </p>\n                <p className=\"text-gray-700\">\n                  Sau 4 năm phát triển, chúng tôi tự hào đã giúp hàng chục nghìn học viên \n                  đạt được mục tiêu tiếng Anh của mình và mở ra những cơ hội mới trong \n                  cuộc sống và sự nghiệp.\n                </p>\n              </div>\n              <div className=\"bg-gray-200 rounded-lg h-64 flex items-center justify-center\">\n                <p className=\"text-gray-500\">Hình ảnh về lịch sử phát triển</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Contact CTA */}\n        <div className=\"text-center bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white\">\n          <h2 className=\"text-3xl font-bold mb-4\">Sẵn sàng bắt đầu hành trình học tiếng Anh?</h2>\n          <p className=\"text-xl mb-6\">\n            Tham gia cùng hàng nghìn học viên đã tin tưởng chọn chúng tôi\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button className=\"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\">\n              Đăng ký ngay\n            </button>\n            <button className=\"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors\">\n              Liên hệ tư vấn\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,QAAQ;QACR,aAAa;YAAC;YAAS;YAAS;SAAmB;IACrD;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,QAAQ;QACR,aAAa;YAAC;YAAqB;YAAc;SAAmB;IACtE;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,QAAQ;QACR,aAAa;YAAC;YAAY;YAAiB;SAAgB;IAC7D;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,QAAQ;QACR,aAAa;YAAC;YAAU;YAAmB;SAAuB;IACpE;CACD;AAED,MAAM,SAAS;IACb;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,eAAe;IACnB;QACE,QAAQ;QACR,OAAO;QACP,aAAa;IACf;IACA;QACE,QAAQ;QACR,OAAO;QACP,aAAa;IACf;IACA;QACE,QAAQ;QACR,OAAO;QACP,aAAa;IACf;IACA;QACE,QAAQ;QACR,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAQzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;;;;;;kDAIrD,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;0CASjD,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAgC;;;;;;;;;;;;kDAIrD,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;kCAWnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,gIAAA,CAAA,OAAI;wCAAa,WAAU;;0DAC1B,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,MAAM,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAExB,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAW,MAAM,KAAK;;;;;;;;;;;;0DAE7C,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAE,WAAU;8DAAyB,MAAM,WAAW;;;;;;;;;;;;uCARhD;;;;;;;;;;;;;;;;kCAgBjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAK/B,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DAA2B,YAAY,MAAM;;;;;;0DAC5D,8OAAC;gDAAI,WAAU;0DAA8B,YAAY,KAAK;;;;;;0DAC9D,8OAAC;gDAAI,WAAU;0DAAyB,YAAY,WAAW;;;;;;;uCAHvD;;;;;;;;;;;;;;;;kCAUhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,gIAAA,CAAA,OAAI;wCAAa,WAAU;;0DAC1B,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAW,OAAO,IAAI;;;;;;kEAC3C,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEACxB,OAAO,IAAI;;;;;;;;;;;;0DAGhB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAE,WAAU;kEAAyB,OAAO,GAAG;;;;;;kEAChD,8OAAC;wDAAI,WAAU;kEACZ,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,WAAW,oBAClC,8OAAC,iIAAA,CAAA,QAAK;gEAAW,SAAQ;gEAAY,WAAU;0EAC5C;+DADS;;;;;;;;;;;;;;;;;uCAZT;;;;;;;;;;;;;;;;kCAwBjB,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAuB;;;;;;;;;;;0CAE9C,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAMlC,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAMlC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAM/B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAe;;;;;;0CAG5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAgG;;;;;;kDAGlH,8OAAC;wCAAO,WAAU;kDAA2H;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzJ", "debugId": null}}]}