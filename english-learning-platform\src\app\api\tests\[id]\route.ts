import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Test from '@/models/Test';
import { getAuthUser } from '@/lib/auth';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = params;

    // Find test
    const test = await Test.findById(id)
      .populate('createdBy', 'name email avatar');

    if (!test) {
      return NextResponse.json({
        success: false,
        message: '<PERSON>hông tìm thấy bài kiểm tra'
      }, { status: 404 });
    }

    // Check if test is published or user has permission
    const user = await getAuthUser(request);
    const canAccess = test.isPublished || 
                     (user && (user.role === 'admin' || user.userId === test.createdBy._id.toString()));

    if (!canAccess) {
      return NextResponse.json({
        success: false,
        message: '<PERSON><PERSON><PERSON> kiểm tra chưa đ<PERSON> xu<PERSON> bản'
      }, { status: 403 });
    }

    // For students taking the test, don't include correct answers
    let testData = test.toJSON();
    if (user && user.role === 'student') {
      testData.questions = testData.questions.map((question: any) => ({
        ...question,
        correctAnswer: undefined,
        explanation: undefined
      }));
    }

    return NextResponse.json({
      success: true,
      data: testData
    });

  } catch (error) {
    console.error('Get test error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi tải bài kiểm tra',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = params;
    
    // Check authentication and permission
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find test
    const test = await Test.findById(id);
    if (!test) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy bài kiểm tra'
      }, { status: 404 });
    }

    // Check permission
    const canEdit = user.role === 'admin' || user.userId === test.createdBy.toString();
    if (!canEdit) {
      return NextResponse.json({
        success: false,
        message: 'Forbidden - Bạn không có quyền chỉnh sửa bài kiểm tra này'
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      title,
      description,
      type,
      level,
      duration,
      passingScore,
      instructions,
      questions,
      tags,
      difficulty,
      isPublished
    } = body;

    // Update test
    const updateData: any = {};
    
    if (title !== undefined) updateData.title = title.trim();
    if (description !== undefined) updateData.description = description.trim();
    if (type !== undefined) updateData.type = type;
    if (level !== undefined) updateData.level = level;
    if (duration !== undefined) updateData.duration = duration;
    if (passingScore !== undefined) updateData.passingScore = passingScore;
    if (instructions !== undefined) updateData.instructions = instructions.trim();
    if (questions !== undefined) {
      updateData.questions = questions.map((q: any, index: number) => ({
        ...q,
        order: index + 1
      }));
    }
    if (tags !== undefined) updateData.tags = tags;
    if (difficulty !== undefined) updateData.difficulty = difficulty;
    if (isPublished !== undefined) updateData.isPublished = isPublished;

    const updatedTest = await Test.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('createdBy', 'name email avatar');

    return NextResponse.json({
      success: true,
      message: 'Cập nhật bài kiểm tra thành công',
      data: updatedTest
    });

  } catch (error) {
    console.error('Update test error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi cập nhật bài kiểm tra',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = params;
    
    // Check authentication and permission
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find test
    const test = await Test.findById(id);
    if (!test) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy bài kiểm tra'
      }, { status: 404 });
    }

    // Check permission
    const canDelete = user.role === 'admin' || user.userId === test.createdBy.toString();
    if (!canDelete) {
      return NextResponse.json({
        success: false,
        message: 'Forbidden - Bạn không có quyền xóa bài kiểm tra này'
      }, { status: 403 });
    }

    await Test.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Xóa bài kiểm tra thành công'
    });

  } catch (error) {
    console.error('Delete test error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi xóa bài kiểm tra',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
