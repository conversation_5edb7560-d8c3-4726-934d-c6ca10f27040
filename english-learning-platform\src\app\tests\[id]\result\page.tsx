"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  CheckCircle,
  XCircle,
  Clock,
  Target,
  TrendingUp,
  RotateCcw,
  Download,
  Share2,
  Award,
  BookOpen,
  Lightbulb,
  BarChart3,
  Brain
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { LoadingPage } from "@/components/ui/loading";
import { ErrorPage } from "@/components/ui/error";
import Header from "@/components/layout/Header";
import { formatDuration } from "@/lib/utils";

// Interface for test result data
interface TestResult {
  id: string;
  testId: string;
  testTitle: string;
  studentName: string;
  totalScore: number;
  maxScore: number;
  percentage: number;
  timeSpent: number;
  startedAt: string;
  completedAt: string;
  isPassed: boolean;
  passingScore: number;
  detailedAnalysis: {
    correctAnswers: number;
    incorrectAnswers: number;
    skippedAnswers: number;
    skillBreakdown: Array<{
      skill: string;
      score: number;
      maxScore: number;
      percentage: number;
    }>;
  };
  answers: Array<{
    questionOrder: number;
    question: string;
    userAnswer: string;
    correctAnswer: string;
    isCorrect: boolean;
    points: number;
    maxPoints: number;
    explanation?: string;
  }>;
  feedback?: string;
  recommendations?: string[];
}

interface TestResultPageProps {
  params: {
    id: string;
  };
}

export default function TestResultPage({ params }: TestResultPageProps) {
  const [result, setResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    loadTestResult();
  }, []);

  const loadTestResult = async () => {
    try {
      const { id } = await params;
      const response = await fetch(`/api/tests/${id}/result`);
      const data = await response.json();

      if (data.success) {
        setResult(data.data);
      } else {
        setError(data.message || 'Không thể tải kết quả bài kiểm tra');
      }
    } catch (error) {
      console.error('Load test result error:', error);
      setError('Lỗi kết nối. Vui lòng thử lại.');
    } finally {
      setIsLoading(false);
    }
  };

  const generateRecommendations = (result: TestResult) => {
    const recommendations = [];
    const weakSkills = result.detailedAnalysis.skillBreakdown
      .filter(skill => skill.percentage < 70)
      .sort((a, b) => a.percentage - b.percentage);

    if (weakSkills.length > 0) {
      const weakestSkill = weakSkills[0];
      recommendations.push({
        type: 'skill_improvement',
        title: `Cải thiện kỹ năng ${weakestSkill.skill}`,
        description: `Bạn đạt ${weakestSkill.percentage}% ở kỹ năng này. Hãy tập trung luyện tập thêm.`,
        action: 'Tìm khóa học liên quan',
        priority: 'high'
      });
    }

    if (result.percentage < 60) {
      recommendations.push({
        type: 'overall_improvement',
        title: 'Ôn tập kiến thức cơ bản',
        description: 'Điểm số tổng thể còn thấp. Bạn nên ôn lại kiến thức cơ bản.',
        action: 'Xem khóa học cơ bản',
        priority: 'high'
      });
    }

    if (result.timeSpent > result.maxScore * 2) {
      recommendations.push({
        type: 'time_management',
        title: 'Cải thiện quản lý thời gian',
        description: 'Bạn mất nhiều thời gian để hoàn thành bài thi. Hãy luyện tập để tăng tốc độ.',
        action: 'Luyện tập thêm',
        priority: 'medium'
      });
    }

    return recommendations;
  };

  const getSkillColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600 bg-green-100';
    if (percentage >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getPerformanceLevel = (percentage: number) => {
    if (percentage >= 90) return 'Xuất sắc';
    if (percentage >= 80) return 'Tốt';
    if (percentage >= 70) return 'Khá';
    if (percentage >= 60) return 'Trung bình';
    return 'Cần cải thiện';
  };

  const breadcrumbItems = [
    { label: "Luyện thi", href: "/tests" },
    { label: result.testTitle, href: `/tests/${result.testId}` },
    { label: "Kết quả", current: true }
  ];

  const getScoreColor = (percentage: number) => {
    if (percentage >= 80) return "text-green-600";
    if (percentage >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadge = (percentage: number) => {
    if (percentage >= 90) return { label: "Xuất sắc", variant: "default" as const };
    if (percentage >= 80) return { label: "Giỏi", variant: "default" as const };
    if (percentage >= 70) return { label: "Khá", variant: "secondary" as const };
    if (percentage >= 60) return { label: "Trung bình", variant: "secondary" as const };
    return { label: "Cần cải thiện", variant: "destructive" as const };
  };

  if (isLoading) {
    return <LoadingPage message="Đang tải kết quả..." />;
  }

  if (error) {
    return <ErrorPage title="Không thể tải kết quả" message={error} />;
  }

  if (!result) {
    return <ErrorPage title="Không tìm thấy kết quả" message="Kết quả bài kiểm tra không tồn tại" />;
  }

  const scoreBadge = getScoreBadge(result.percentage);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Breadcrumb items={breadcrumbItems} className="mb-6" />
        
        {/* Result Header */}
        <Card className="mb-8">
          <CardContent className="pt-6">
            <div className="text-center">
              {result.isPassed ? (
                <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
              ) : (
                <XCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
              )}
              
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {result.isPassed ? "Chúc mừng! Bạn đã đạt" : "Chưa đạt yêu cầu"}
              </h1>
              
              <div className={`text-6xl font-bold mb-4 ${getScoreColor(result.percentage)}`}>
                {result.percentage}%
              </div>
              
              <Badge variant={scoreBadge.variant} className="text-lg px-4 py-2 mb-4">
                {scoreBadge.label}
              </Badge>
              
              <p className="text-gray-600 mb-6">
                {result.totalScore}/{result.maxScore} điểm • 
                Thời gian: {formatDuration(Math.floor(result.timeSpent / 60))} • 
                Điểm đạt: {result.passingScore}%
              </p>
              
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button asChild>
                  <Link href={`/tests/${result.testId}`}>
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Làm lại bài thi
                  </Link>
                </Button>
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Tải kết quả
                </Button>
                <Button variant="outline">
                  <Share2 className="h-4 w-4 mr-2" />
                  Chia sẻ
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Results */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Tổng quan</TabsTrigger>
            <TabsTrigger value="analysis">Phân tích chi tiết</TabsTrigger>
            <TabsTrigger value="answers">Đáp án chi tiết</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Câu trả lời đúng</CardTitle>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {result.detailedAnalysis.correctAnswers}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    /{result.detailedAnalysis.correctAnswers + result.detailedAnalysis.incorrectAnswers + result.detailedAnalysis.skippedAnswers} câu
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Câu trả lời sai</CardTitle>
                  <XCircle className="h-4 w-4 text-red-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">
                    {result.detailedAnalysis.incorrectAnswers}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Cần xem lại
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Thời gian làm bài</CardTitle>
                  <Clock className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">
                    {formatDuration(Math.floor(result.timeSpent / 60))}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Hoàn thành đúng hạn
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Độ chính xác</CardTitle>
                  <Target className="h-4 w-4 text-purple-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-600">
                    {Math.round((result.detailedAnalysis.correctAnswers / (result.detailedAnalysis.correctAnswers + result.detailedAnalysis.incorrectAnswers)) * 100)}%
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Tỷ lệ trả lời đúng
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Feedback */}
            <Card>
              <CardHeader>
                <CardTitle>Nhận xét từ hệ thống</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 mb-4">{result.feedback}</p>
                
                <h4 className="font-medium text-gray-900 mb-3">Gợi ý cải thiện:</h4>
                <ul className="space-y-2">
                  {result.recommendations?.map((rec: string, index: number) => (
                    <li key={index} className="flex items-start">
                      <TrendingUp className="h-4 w-4 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{rec}</span>
                    </li>
                  )) || (
                    <li className="text-gray-500">Chưa có gợi ý cải thiện</li>
                  )}
                </ul>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analysis Tab */}
          <TabsContent value="analysis" className="space-y-6">
            {/* Skill Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Phân tích theo kỹ năng
                </CardTitle>
                <CardDescription>
                  Điểm số chi tiết theo từng kỹ năng được đánh giá
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {result.detailedAnalysis.skillBreakdown.map((skill, index) => (
                  <div key={index} className="space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-gray-900">{skill.skill}</span>
                        <Badge className={getSkillColor(skill.percentage)}>
                          {getPerformanceLevel(skill.percentage)}
                        </Badge>
                      </div>
                      <span className="text-sm text-gray-600">
                        {skill.score}/{skill.maxScore} điểm ({skill.percentage}%)
                      </span>
                    </div>
                    <Progress value={skill.percentage} className="h-3" />
                    {skill.percentage < 70 && (
                      <div className="text-sm text-orange-600 bg-orange-50 p-2 rounded">
                        💡 Kỹ năng này cần được cải thiện. Hãy tập trung luyện tập thêm.
                      </div>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Personalized Recommendations */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5" />
                  Gợi ý cá nhân hóa
                </CardTitle>
                <CardDescription>
                  Dựa trên kết quả của bạn, chúng tôi đề xuất những hướng cải thiện sau
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {generateRecommendations(result).map((rec, index) => (
                    <div key={index} className={`p-4 rounded-lg border-l-4 ${
                      rec.priority === 'high' ? 'border-red-500 bg-red-50' :
                      rec.priority === 'medium' ? 'border-yellow-500 bg-yellow-50' :
                      'border-blue-500 bg-blue-50'
                    }`}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 mb-1">{rec.title}</h4>
                          <p className="text-sm text-gray-600 mb-2">{rec.description}</p>
                          <Badge variant="outline" className="text-xs">
                            {rec.priority === 'high' ? 'Ưu tiên cao' :
                             rec.priority === 'medium' ? 'Ưu tiên trung bình' : 'Ưu tiên thấp'}
                          </Badge>
                        </div>
                        <Button variant="outline" size="sm">
                          {rec.action}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Performance Insights */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  Phân tích hiệu suất
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-900">Điểm mạnh</h4>
                    <div className="space-y-2">
                      {result.detailedAnalysis.skillBreakdown
                        .filter(skill => skill.percentage >= 80)
                        .map((skill, index) => (
                          <div key={index} className="flex items-center gap-2 text-green-600">
                            <CheckCircle className="h-4 w-4" />
                            <span className="text-sm">{skill.skill} ({skill.percentage}%)</span>
                          </div>
                        ))}
                      {result.detailedAnalysis.skillBreakdown.filter(skill => skill.percentage >= 80).length === 0 && (
                        <p className="text-sm text-gray-500">Chưa có kỹ năng nào đạt mức xuất sắc</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-900">Cần cải thiện</h4>
                    <div className="space-y-2">
                      {result.detailedAnalysis.skillBreakdown
                        .filter(skill => skill.percentage < 70)
                        .map((skill, index) => (
                          <div key={index} className="flex items-center gap-2 text-red-600">
                            <XCircle className="h-4 w-4" />
                            <span className="text-sm">{skill.skill} ({skill.percentage}%)</span>
                          </div>
                        ))}
                      {result.detailedAnalysis.skillBreakdown.filter(skill => skill.percentage < 70).length === 0 && (
                        <p className="text-sm text-gray-500">Tất cả kỹ năng đều ở mức tốt</p>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Answers Tab */}
          <TabsContent value="answers" className="space-y-6">
            <div className="space-y-4">
              {result.answers.map((answer, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Câu {answer.questionOrder}</CardTitle>
                      <div className="flex items-center gap-2">
                        {answer.isCorrect ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-600" />
                        )}
                        <Badge variant={answer.isCorrect ? "default" : "destructive"}>
                          {answer.points}/{answer.maxPoints} điểm
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Câu hỏi:</h4>
                      <p className="text-gray-700">{answer.question}</p>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Câu trả lời của bạn:</h4>
                        <p className={`p-3 rounded-lg ${
                          answer.isCorrect ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
                        }`}>
                          {answer.userAnswer}
                        </p>
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Đáp án đúng:</h4>
                        <p className="p-3 bg-blue-50 text-blue-800 rounded-lg">
                          {answer.correctAnswer}
                        </p>
                      </div>
                    </div>
                    
                    {answer.explanation && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Giải thích:</h4>
                        <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">
                          {answer.explanation}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Achievement */}
        {result.isPassed && (
          <Card className="mt-8 bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center text-center">
                <Award className="h-12 w-12 text-yellow-600 mr-4" />
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    Chúc mừng! Bạn đã nhận được huy hiệu
                  </h3>
                  <p className="text-gray-600">
                    "Chuyên gia từ vựng cơ bản" - Hoàn thành xuất sắc bài kiểm tra từ vựng
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
