import { QuestionType } from '@/types';

interface Question {
  _id: string;
  type: QuestionType;
  question: string;
  options?: string[];
  correctAnswer: string | string[];
  points: number;
  audioUrl?: string;
  passage?: string;
  wordLimit?: number;
  timeLimit?: number;
}

interface Answer {
  questionId: string;
  answer: any;
  timeSpent?: number;
}

interface ScoringResult {
  questionId: string;
  userAnswer: any;
  correctAnswer: string | string[];
  isCorrect: boolean;
  score: number;
  maxScore: number;
  feedback?: string;
  explanation?: string;
}

export class AutomaticScoring {
  
  static scoreMultipleChoice(question: Question, userAnswer: string): ScoringResult {
    const isCorrect = userAnswer === question.correctAnswer;
    
    return {
      questionId: question._id,
      userAnswer,
      correctAnswer: question.correctAnswer,
      isCorrect,
      score: isCorrect ? question.points : 0,
      maxScore: question.points,
      feedback: isCorrect ? 'Chính xác!' : 'Không chính xác.',
      explanation: `Đáp án đúng là: ${question.correctAnswer}`
    };
  }

  static scoreTrueFalse(question: Question, userAnswer: string): ScoringResult {
    const isCorrect = userAnswer === question.correctAnswer;
    
    return {
      questionId: question._id,
      userAnswer,
      correctAnswer: question.correctAnswer,
      isCorrect,
      score: isCorrect ? question.points : 0,
      maxScore: question.points,
      feedback: isCorrect ? 'Chính xác!' : 'Không chính xác.',
      explanation: `Đáp án đúng là: ${question.correctAnswer === 'True' ? 'Đúng' : 'Sai'}`
    };
  }

  static scoreFillInBlank(question: Question, userAnswer: string): ScoringResult {
    const correctAnswers = Array.isArray(question.correctAnswer) 
      ? question.correctAnswer 
      : [question.correctAnswer];
    
    const normalizedUserAnswer = userAnswer.toLowerCase().trim();
    const isCorrect = correctAnswers.some(answer => 
      answer.toLowerCase().trim() === normalizedUserAnswer
    );
    
    // Partial scoring for close answers
    let score = 0;
    if (isCorrect) {
      score = question.points;
    } else {
      // Check for partial matches (simple similarity)
      const bestMatch = correctAnswers.find(answer => {
        const similarity = this.calculateStringSimilarity(
          normalizedUserAnswer, 
          answer.toLowerCase().trim()
        );
        return similarity > 0.8;
      });
      
      if (bestMatch) {
        score = Math.round(question.points * 0.5); // 50% for close answers
      }
    }
    
    return {
      questionId: question._id,
      userAnswer,
      correctAnswer: question.correctAnswer,
      isCorrect,
      score,
      maxScore: question.points,
      feedback: isCorrect ? 'Chính xác!' : score > 0 ? 'Gần đúng!' : 'Không chính xác.',
      explanation: `Đáp án đúng: ${Array.isArray(question.correctAnswer) ? question.correctAnswer.join(', ') : question.correctAnswer}`
    };
  }

  static scoreEssay(question: Question, userAnswer: string): ScoringResult {
    // Basic essay scoring - in a real system, this would use AI/ML
    const wordCount = userAnswer.split(/\s+/).filter(word => word.length > 0).length;
    const minWords = question.wordLimit ? Math.floor(question.wordLimit * 0.7) : 50;
    const maxWords = question.wordLimit || 500;
    
    let score = 0;
    let feedback = '';
    
    if (wordCount < minWords) {
      score = Math.round(question.points * 0.3);
      feedback = `Bài viết quá ngắn (${wordCount} từ). Cần ít nhất ${minWords} từ.`;
    } else if (wordCount > maxWords) {
      score = Math.round(question.points * 0.8);
      feedback = `Bài viết quá dài (${wordCount} từ). Giới hạn ${maxWords} từ.`;
    } else {
      // Basic content scoring
      const hasIntroduction = userAnswer.toLowerCase().includes('introduction') || 
                             userAnswer.toLowerCase().includes('firstly') ||
                             userAnswer.toLowerCase().includes('to begin');
      const hasConclusion = userAnswer.toLowerCase().includes('conclusion') || 
                           userAnswer.toLowerCase().includes('finally') ||
                           userAnswer.toLowerCase().includes('in summary');
      const hasStructure = userAnswer.includes('\n') || userAnswer.split('.').length > 3;
      
      let contentScore = 0.6; // Base score
      if (hasIntroduction) contentScore += 0.15;
      if (hasConclusion) contentScore += 0.15;
      if (hasStructure) contentScore += 0.1;
      
      score = Math.round(question.points * contentScore);
      feedback = `Bài viết có ${wordCount} từ. `;
      
      if (contentScore >= 0.8) {
        feedback += 'Cấu trúc tốt với mở bài và kết luận rõ ràng.';
      } else if (contentScore >= 0.7) {
        feedback += 'Cấu trúc khá tốt, có thể cải thiện thêm.';
      } else {
        feedback += 'Cần cải thiện cấu trúc bài viết.';
      }
    }
    
    return {
      questionId: question._id,
      userAnswer,
      correctAnswer: 'Đánh giá dựa trên nội dung và cấu trúc',
      isCorrect: score >= question.points * 0.6,
      score,
      maxScore: question.points,
      feedback,
      explanation: 'Bài viết được đánh giá dựa trên độ dài, cấu trúc và nội dung.'
    };
  }

  static scoreAudioResponse(question: Question, audioBlob: Blob): ScoringResult {
    // In a real system, this would use speech recognition and pronunciation analysis
    // For now, we'll provide a basic scoring based on audio duration
    
    const expectedDuration = question.timeLimit || 60; // seconds
    const audioDuration = audioBlob.size / 1000; // rough estimate
    
    let score = 0;
    let feedback = '';
    
    if (audioDuration < expectedDuration * 0.5) {
      score = Math.round(question.points * 0.4);
      feedback = 'Câu trả lời quá ngắn. Hãy nói đầy đủ hơn.';
    } else if (audioDuration > expectedDuration * 1.5) {
      score = Math.round(question.points * 0.7);
      feedback = 'Câu trả lời hơi dài. Hãy cô đọng hơn.';
    } else {
      score = Math.round(question.points * 0.8); // Base score for appropriate length
      feedback = 'Độ dài câu trả lời phù hợp. Cần đánh giá phát âm để có điểm chính xác.';
    }
    
    return {
      questionId: question._id,
      userAnswer: 'Audio response',
      correctAnswer: 'Đánh giá dựa trên phát âm và nội dung',
      isCorrect: score >= question.points * 0.6,
      score,
      maxScore: question.points,
      feedback,
      explanation: 'Câu trả lời âm thanh cần được đánh giá bởi giáo viên để có điểm chính xác.'
    };
  }

  static scoreListening(question: Question, userAnswer: string): ScoringResult {
    // Listening questions are typically multiple choice or fill-in-blank
    if (question.options) {
      return this.scoreMultipleChoice(question, userAnswer);
    } else {
      return this.scoreFillInBlank(question, userAnswer);
    }
  }

  static scoreReading(question: Question, userAnswer: string): ScoringResult {
    // Reading questions are typically multiple choice or fill-in-blank
    if (question.options) {
      return this.scoreMultipleChoice(question, userAnswer);
    } else {
      return this.scoreFillInBlank(question, userAnswer);
    }
  }

  static scoreQuestion(question: Question, answer: Answer): ScoringResult {
    switch (question.type) {
      case QuestionType.MULTIPLE_CHOICE:
        return this.scoreMultipleChoice(question, answer.answer);
      
      case QuestionType.TRUE_FALSE:
        return this.scoreTrueFalse(question, answer.answer);
      
      case QuestionType.FILL_IN_BLANK:
        return this.scoreFillInBlank(question, answer.answer);
      
      case QuestionType.ESSAY:
        return this.scoreEssay(question, answer.answer);
      
      case QuestionType.AUDIO_RESPONSE:
        return this.scoreAudioResponse(question, answer.answer);
      
      case QuestionType.LISTENING:
        return this.scoreListening(question, answer.answer);
      
      case QuestionType.READING:
        return this.scoreReading(question, answer.answer);
      
      default:
        return {
          questionId: question._id,
          userAnswer: answer.answer,
          correctAnswer: question.correctAnswer,
          isCorrect: false,
          score: 0,
          maxScore: question.points,
          feedback: 'Loại câu hỏi không được hỗ trợ',
          explanation: 'Cần đánh giá thủ công'
        };
    }
  }

  static calculateOverallScore(results: ScoringResult[]): {
    totalScore: number;
    maxScore: number;
    percentage: number;
    correctAnswers: number;
    incorrectAnswers: number;
    skillBreakdown: Array<{
      skill: string;
      score: number;
      maxScore: number;
      percentage: number;
    }>;
  } {
    const totalScore = results.reduce((sum, result) => sum + result.score, 0);
    const maxScore = results.reduce((sum, result) => sum + result.maxScore, 0);
    const percentage = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0;
    
    const correctAnswers = results.filter(r => r.isCorrect).length;
    const incorrectAnswers = results.length - correctAnswers;
    
    // Group by skill (this would be more sophisticated in a real system)
    const skillBreakdown = [
      {
        skill: 'Tổng thể',
        score: totalScore,
        maxScore: maxScore,
        percentage: percentage
      }
    ];
    
    return {
      totalScore,
      maxScore,
      percentage,
      correctAnswers,
      incorrectAnswers,
      skillBreakdown
    };
  }

  private static calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  private static levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }
}
