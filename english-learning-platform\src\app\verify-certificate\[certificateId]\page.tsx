"use client";

import { useState, useEffect } from "react";
import { CheckCircle, XCircle, Award, Calendar, User, BookOpen, Shield } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { LoadingPage } from "@/components/ui/loading";
import { ErrorPage } from "@/components/ui/error";
import Header from "@/components/layout/Header";

interface CertificateVerificationProps {
  params: {
    certificateId: string;
  };
}

// Mock certificate data
const mockCertificate = {
  id: "CERT-1234567890-ABC123",
  studentName: "Nguyễn Văn A",
  studentEmail: "<EMAIL>",
  courseName: "Tiếng <PERSON>h cơ bản cho người mới bắt đầu",
  instructorName: "Nguyễn Thị B",
  completedAt: "2024-01-15T10:30:00Z",
  issuedAt: "2024-01-15T10:30:00Z",
  duration: 40,
  totalTimeSpent: 45,
  isValid: true,
  verificationDate: new Date().toISOString()
};

export default function CertificateVerificationPage({ params }: CertificateVerificationProps) {
  const [certificate, setCertificate] = useState(mockCertificate);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    verifyCertificate();
  }, [params.certificateId]);

  const verifyCertificate = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // TODO: Call API to verify certificate
      // const response = await fetch(`/api/verify-certificate/${params.certificateId}`);
      // const data = await response.json();

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Check if certificate ID matches expected format
      if (!params.certificateId.startsWith('CERT-')) {
        throw new Error('Mã chứng chỉ không hợp lệ');
      }

      // Mock verification result
      setCertificate(mockCertificate);
    } catch (error) {
      console.error('Certificate verification error:', error);
      setError(error instanceof Error ? error.message : 'Lỗi khi xác thực chứng chỉ');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return <LoadingPage message="Đang xác thực chứng chỉ..." />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <XCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-red-900 mb-2">
                  Chứng chỉ không hợp lệ
                </h1>
                <p className="text-red-700 mb-4">{error}</p>
                <div className="bg-red-100 p-4 rounded-lg">
                  <p className="text-sm text-red-800">
                    <strong>Mã chứng chỉ:</strong> {params.certificateId}
                  </p>
                  <p className="text-sm text-red-800 mt-1">
                    <strong>Thời gian kiểm tra:</strong> {formatDate(new Date().toISOString())}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Verification Status */}
        <Card className="border-green-200 bg-green-50 mb-8">
          <CardContent className="pt-6">
            <div className="text-center">
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
              <h1 className="text-3xl font-bold text-green-900 mb-2">
                Chứng chỉ hợp lệ
              </h1>
              <p className="text-green-700 mb-4">
                Chứng chỉ này đã được xác thực và có hiệu lực
              </p>
              <Badge variant="default" className="bg-green-600">
                <Shield className="h-3 w-3 mr-1" />
                Đã xác thực
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Certificate Details */}
        <Card className="mb-8">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Award className="h-12 w-12 text-yellow-500" />
            </div>
            <CardTitle className="text-2xl">Chứng chỉ hoàn thành khóa học</CardTitle>
            <CardDescription>English Learning Platform</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Main Certificate Info */}
            <div className="text-center space-y-4 py-6 border-y">
              <p className="text-lg text-gray-700">Chứng nhận rằng</p>
              <h2 className="text-3xl font-bold text-blue-900">
                {certificate.studentName}
              </h2>
              <p className="text-lg text-gray-700">đã hoàn thành xuất sắc khóa học</p>
              <h3 className="text-xl font-semibold text-gray-800 max-w-2xl mx-auto">
                {certificate.courseName}
              </h3>
            </div>

            {/* Certificate Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <User className="h-5 w-5 text-gray-500" />
                  <div>
                    <label className="text-sm font-medium text-gray-600">Người nhận</label>
                    <p className="text-gray-900">{certificate.studentName}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <User className="h-5 w-5 text-gray-500" />
                  <div>
                    <label className="text-sm font-medium text-gray-600">Giảng viên</label>
                    <p className="text-gray-900">{certificate.instructorName}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <BookOpen className="h-5 w-5 text-gray-500" />
                  <div>
                    <label className="text-sm font-medium text-gray-600">Thời lượng khóa học</label>
                    <p className="text-gray-900">{certificate.duration} giờ</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-gray-500" />
                  <div>
                    <label className="text-sm font-medium text-gray-600">Ngày hoàn thành</label>
                    <p className="text-gray-900">{formatDate(certificate.completedAt)}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-gray-500" />
                  <div>
                    <label className="text-sm font-medium text-gray-600">Ngày cấp chứng chỉ</label>
                    <p className="text-gray-900">{formatDate(certificate.issuedAt)}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <BookOpen className="h-5 w-5 text-gray-500" />
                  <div>
                    <label className="text-sm font-medium text-gray-600">Thời gian học thực tế</label>
                    <p className="text-gray-900">{certificate.totalTimeSpent} giờ</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Verification Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2" />
              Thông tin xác thực
            </CardTitle>
            <CardDescription>
              Chi tiết về quá trình xác thực chứng chỉ
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Mã chứng chỉ</label>
                <p className="text-gray-900 font-mono text-sm bg-gray-100 p-2 rounded">
                  {certificate.id}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-600">Thời gian xác thực</label>
                <p className="text-gray-900">{formatDate(certificate.verificationDate)}</p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-600">Trạng thái</label>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-green-700 font-medium">Hợp lệ</span>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-600">Cơ quan cấp</label>
                <p className="text-gray-900">English Learning Platform</p>
              </div>
            </div>

            {/* Security Notice */}
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-900 mb-2">Lưu ý bảo mật</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Chứng chỉ này được bảo vệ bằng công nghệ mã hóa</li>
                <li>• Mọi thông tin trên chứng chỉ đều được xác thực từ cơ sở dữ liệu</li>
                <li>• Chứng chỉ có thể được xác thực bất cứ lúc nào thông qua mã chứng chỉ</li>
                <li>• Nếu có nghi ngờ về tính xác thực, vui lòng liên hệ với chúng tôi</li>
              </ul>
            </div>

            {/* Contact Info */}
            <div className="text-center pt-4 border-t">
              <p className="text-sm text-gray-600">
                Để biết thêm thông tin hoặc xác thực chứng chỉ, vui lòng liên hệ:
              </p>
              <p className="text-sm text-gray-900 font-medium">
                Email: <EMAIL> | Hotline: 1900-1234
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
