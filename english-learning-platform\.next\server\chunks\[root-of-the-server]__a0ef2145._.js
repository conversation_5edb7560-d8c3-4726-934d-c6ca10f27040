module.exports = {

"[project]/.next-internal/server/app/api/tests/[id]/submit/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Vui lòng định nghĩa biến môi trường MONGODB_URI trong .env.local');
}
/**
 * Global được sử dụng để duy trì kết nối cached trong môi trường development.
 * Điều này ngăn chặn việc tạo quá nhiều kết nối trong quá trình hot reloading.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            console.log('✅ Kết nối MongoDB thành công');
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        console.error('❌ Lỗi kết nối MongoDB:', e);
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[project]/src/types/index.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Enum types
__turbopack_context__.s({
    "CourseCategory": (()=>CourseCategory),
    "CourseLevel": (()=>CourseLevel),
    "PaymentStatus": (()=>PaymentStatus),
    "QuestionType": (()=>QuestionType),
    "TestType": (()=>TestType),
    "UserRole": (()=>UserRole)
});
var UserRole = /*#__PURE__*/ function(UserRole) {
    UserRole["ADMIN"] = "admin";
    UserRole["TEACHER"] = "teacher";
    UserRole["STUDENT"] = "student";
    return UserRole;
}({});
var CourseCategory = /*#__PURE__*/ function(CourseCategory) {
    CourseCategory["LISTENING"] = "listening";
    CourseCategory["SPEAKING"] = "speaking";
    CourseCategory["READING"] = "reading";
    CourseCategory["WRITING"] = "writing";
    CourseCategory["COMPREHENSIVE"] = "comprehensive";
    return CourseCategory;
}({});
var CourseLevel = /*#__PURE__*/ function(CourseLevel) {
    CourseLevel["BEGINNER"] = "beginner";
    CourseLevel["INTERMEDIATE"] = "intermediate";
    CourseLevel["ADVANCED"] = "advanced";
    return CourseLevel;
}({});
var TestType = /*#__PURE__*/ function(TestType) {
    TestType["VOCABULARY"] = "vocabulary";
    TestType["GRAMMAR"] = "grammar";
    TestType["LISTENING"] = "listening";
    TestType["SPEAKING"] = "speaking";
    TestType["READING"] = "reading";
    TestType["WRITING"] = "writing";
    TestType["COMPREHENSIVE"] = "comprehensive";
    TestType["PRACTICE"] = "practice";
    return TestType;
}({});
var QuestionType = /*#__PURE__*/ function(QuestionType) {
    QuestionType["MULTIPLE_CHOICE"] = "multiple_choice";
    QuestionType["FILL_IN_BLANK"] = "fill_in_blank";
    QuestionType["TRUE_FALSE"] = "true_false";
    QuestionType["ESSAY"] = "essay";
    QuestionType["AUDIO_RESPONSE"] = "audio_response";
    QuestionType["DRAG_DROP"] = "drag_drop";
    QuestionType["MATCHING"] = "matching";
    return QuestionType;
}({});
var PaymentStatus = /*#__PURE__*/ function(PaymentStatus) {
    PaymentStatus["PENDING"] = "pending";
    PaymentStatus["COMPLETED"] = "completed";
    PaymentStatus["FAILED"] = "failed";
    return PaymentStatus;
}({});
}}),
"[project]/src/models/Test.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-route] (ecmascript)");
;
;
const QuestionSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    type: {
        type: String,
        enum: Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["QuestionType"]),
        required: [
            true,
            'Loại câu hỏi là bắt buộc'
        ]
    },
    question: {
        type: String,
        required: [
            true,
            'Nội dung câu hỏi là bắt buộc'
        ],
        maxlength: [
            2000,
            'Câu hỏi không được vượt quá 2000 ký tự'
        ]
    },
    options: [
        {
            type: String,
            maxlength: [
                500,
                'Tùy chọn không được vượt quá 500 ký tự'
            ]
        }
    ],
    correctAnswer: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.Mixed,
        required: [
            true,
            'Đáp án đúng là bắt buộc'
        ]
    },
    points: {
        type: Number,
        required: [
            true,
            'Điểm số là bắt buộc'
        ],
        min: [
            0,
            'Điểm số không được âm'
        ],
        max: [
            100,
            'Điểm số không được vượt quá 100'
        ]
    },
    explanation: {
        type: String,
        maxlength: [
            1000,
            'Giải thích không được vượt quá 1000 ký tự'
        ]
    },
    audioUrl: {
        type: String,
        validate: {
            validator: function(v) {
                return !v || /^https?:\/\/.+/.test(v);
            },
            message: 'URL audio không hợp lệ'
        }
    },
    imageUrl: {
        type: String,
        validate: {
            validator: function(v) {
                return !v || /^https?:\/\/.+/.test(v);
            },
            message: 'URL hình ảnh không hợp lệ'
        }
    },
    timeLimit: {
        type: Number,
        min: [
            10,
            'Thời gian tối thiểu là 10 giây'
        ]
    },
    order: {
        type: Number,
        required: [
            true,
            'Thứ tự câu hỏi là bắt buộc'
        ],
        min: [
            1,
            'Thứ tự phải bắt đầu từ 1'
        ]
    }
});
const TestSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    title: {
        type: String,
        required: [
            true,
            'Tiêu đề bài kiểm tra là bắt buộc'
        ],
        trim: true,
        maxlength: [
            200,
            'Tiêu đề không được vượt quá 200 ký tự'
        ]
    },
    description: {
        type: String,
        required: [
            true,
            'Mô tả bài kiểm tra là bắt buộc'
        ],
        maxlength: [
            2000,
            'Mô tả không được vượt quá 2000 ký tự'
        ]
    },
    type: {
        type: String,
        enum: Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TestType"]),
        required: [
            true,
            'Loại bài kiểm tra là bắt buộc'
        ]
    },
    level: {
        type: String,
        enum: Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CourseLevel"]),
        required: [
            true,
            'Trình độ bài kiểm tra là bắt buộc'
        ]
    },
    duration: {
        type: Number,
        required: [
            true,
            'Thời lượng bài kiểm tra là bắt buộc'
        ],
        min: [
            5,
            'Thời lượng tối thiểu là 5 phút'
        ],
        max: [
            300,
            'Thời lượng tối đa là 300 phút'
        ]
    },
    totalQuestions: {
        type: Number,
        required: [
            true,
            'Tổng số câu hỏi là bắt buộc'
        ],
        min: [
            1,
            'Phải có ít nhất 1 câu hỏi'
        ]
    },
    passingScore: {
        type: Number,
        required: [
            true,
            'Điểm đạt là bắt buộc'
        ],
        min: [
            0,
            'Điểm đạt không được âm'
        ],
        max: [
            100,
            'Điểm đạt không được vượt quá 100%'
        ]
    },
    instructions: {
        type: String,
        required: [
            true,
            'Hướng dẫn làm bài là bắt buộc'
        ],
        maxlength: [
            3000,
            'Hướng dẫn không được vượt quá 3000 ký tự'
        ]
    },
    questions: [
        QuestionSchema
    ],
    createdBy: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
        ref: 'User',
        required: [
            true,
            'Người tạo bài kiểm tra là bắt buộc'
        ]
    },
    isPublished: {
        type: Boolean,
        default: false
    },
    tags: [
        {
            type: String,
            trim: true,
            maxlength: [
                50,
                'Tag không được vượt quá 50 ký tự'
            ]
        }
    ],
    difficulty: {
        type: String,
        enum: [
            'easy',
            'medium',
            'hard'
        ],
        default: 'medium'
    }
}, {
    timestamps: true
});
// Indexes cho tìm kiếm và hiệu suất
TestSchema.index({
    title: 'text',
    description: 'text',
    tags: 'text'
});
TestSchema.index({
    type: 1,
    level: 1
});
TestSchema.index({
    createdBy: 1
});
TestSchema.index({
    isPublished: 1
});
TestSchema.index({
    difficulty: 1
});
TestSchema.index({
    createdAt: -1
});
// Middleware để cập nhật totalQuestions
TestSchema.pre('save', function(next) {
    this.totalQuestions = this.questions.length;
    next();
});
// Virtual để tính điểm tối đa
TestSchema.virtual('maxScore').get(function() {
    return this.questions.reduce((total, question)=>total + question.points, 0);
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Test || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Test', TestSchema);
}}),
"[project]/src/models/TestResult.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const AnswerSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    questionId: {
        type: String,
        required: [
            true,
            'ID câu hỏi là bắt buộc'
        ]
    },
    questionOrder: {
        type: Number,
        required: [
            true,
            'Thứ tự câu hỏi là bắt buộc'
        ]
    },
    userAnswer: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.Mixed,
        default: null
    },
    correctAnswer: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.Mixed,
        required: [
            true,
            'Đáp án đúng là bắt buộc'
        ]
    },
    isCorrect: {
        type: Boolean,
        default: false
    },
    points: {
        type: Number,
        default: 0,
        min: [
            0,
            'Điểm không được âm'
        ]
    },
    maxPoints: {
        type: Number,
        required: [
            true,
            'Điểm tối đa là bắt buộc'
        ],
        min: [
            0,
            'Điểm tối đa không được âm'
        ]
    },
    timeSpent: {
        type: Number,
        default: 0,
        min: [
            0,
            'Thời gian không được âm'
        ]
    },
    isSkipped: {
        type: Boolean,
        default: false
    },
    needsManualGrading: {
        type: Boolean,
        default: false
    },
    manualScore: {
        type: Number,
        min: [
            0,
            'Điểm thủ công không được âm'
        ]
    },
    feedback: {
        type: String,
        maxlength: [
            1000,
            'Phản hồi không được vượt quá 1000 ký tự'
        ]
    }
});
const SkillBreakdownSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    skill: {
        type: String,
        required: [
            true,
            'Tên kỹ năng là bắt buộc'
        ]
    },
    score: {
        type: Number,
        required: [
            true,
            'Điểm kỹ năng là bắt buộc'
        ],
        min: [
            0,
            'Điểm không được âm'
        ]
    },
    maxScore: {
        type: Number,
        required: [
            true,
            'Điểm tối đa kỹ năng là bắt buộc'
        ],
        min: [
            0,
            'Điểm tối đa không được âm'
        ]
    },
    percentage: {
        type: Number,
        required: [
            true,
            'Phần trăm kỹ năng là bắt buộc'
        ],
        min: [
            0,
            'Phần trăm không được âm'
        ],
        max: [
            100,
            'Phần trăm không được vượt quá 100'
        ]
    }
});
const TestResultSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    studentId: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
        ref: 'User',
        required: [
            true,
            'ID học viên là bắt buộc'
        ]
    },
    testId: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
        ref: 'Test',
        required: [
            true,
            'ID bài kiểm tra là bắt buộc'
        ]
    },
    answers: [
        AnswerSchema
    ],
    totalScore: {
        type: Number,
        required: [
            true,
            'Tổng điểm là bắt buộc'
        ],
        min: [
            0,
            'Tổng điểm không được âm'
        ]
    },
    maxScore: {
        type: Number,
        required: [
            true,
            'Điểm tối đa là bắt buộc'
        ],
        min: [
            0,
            'Điểm tối đa không được âm'
        ]
    },
    percentage: {
        type: Number,
        required: [
            true,
            'Phần trăm điểm là bắt buộc'
        ],
        min: [
            0,
            'Phần trăm không được âm'
        ],
        max: [
            100,
            'Phần trăm không được vượt quá 100'
        ]
    },
    timeSpent: {
        type: Number,
        required: [
            true,
            'Thời gian làm bài là bắt buộc'
        ],
        min: [
            0,
            'Thời gian không được âm'
        ]
    },
    startedAt: {
        type: Date,
        required: [
            true,
            'Thời gian bắt đầu là bắt buộc'
        ]
    },
    completedAt: {
        type: Date,
        required: [
            true,
            'Thời gian hoàn thành là bắt buộc'
        ]
    },
    isPassed: {
        type: Boolean,
        required: [
            true,
            'Trạng thái đạt/không đạt là bắt buộc'
        ]
    },
    feedback: {
        type: String,
        maxlength: [
            2000,
            'Phản hồi không được vượt quá 2000 ký tự'
        ]
    },
    detailedAnalysis: {
        correctAnswers: {
            type: Number,
            required: [
                true,
                'Số câu đúng là bắt buộc'
            ],
            min: [
                0,
                'Số câu đúng không được âm'
            ]
        },
        incorrectAnswers: {
            type: Number,
            required: [
                true,
                'Số câu sai là bắt buộc'
            ],
            min: [
                0,
                'Số câu sai không được âm'
            ]
        },
        skippedAnswers: {
            type: Number,
            required: [
                true,
                'Số câu bỏ qua là bắt buộc'
            ],
            min: [
                0,
                'Số câu bỏ qua không được âm'
            ]
        },
        skillBreakdown: [
            SkillBreakdownSchema
        ]
    },
    gradingStatus: {
        type: String,
        enum: [
            'auto',
            'pending',
            'manual',
            'completed'
        ],
        default: 'auto'
    },
    gradedBy: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
        ref: 'User'
    },
    gradedAt: {
        type: Date
    }
}, {
    timestamps: true
});
// Indexes cho tìm kiếm và hiệu suất
TestResultSchema.index({
    studentId: 1,
    testId: 1
});
TestResultSchema.index({
    studentId: 1,
    completedAt: -1
});
TestResultSchema.index({
    testId: 1,
    completedAt: -1
});
TestResultSchema.index({
    gradingStatus: 1
});
TestResultSchema.index({
    isPassed: 1
});
// Middleware để tính toán các giá trị
TestResultSchema.pre('save', function(next) {
    // Tính phần trăm
    if (this.maxScore > 0) {
        this.percentage = Math.round(this.totalScore / this.maxScore * 100);
    }
    // Cập nhật thời gian chấm điểm nếu trạng thái thay đổi
    if (this.isModified('gradingStatus') && this.gradingStatus === 'completed') {
        this.gradedAt = new Date();
    }
    next();
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.TestResult || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('TestResult', TestResultSchema);
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/models/User.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-route] (ecmascript)");
;
;
;
const UserSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    email: {
        type: String,
        required: [
            true,
            'Email là bắt buộc'
        ],
        unique: true,
        lowercase: true,
        trim: true,
        match: [
            /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
            'Email không hợp lệ'
        ]
    },
    password: {
        type: String,
        required: [
            true,
            'Mật khẩu là bắt buộc'
        ],
        minlength: [
            6,
            'Mật khẩu phải có ít nhất 6 ký tự'
        ]
    },
    name: {
        type: String,
        required: [
            true,
            'Tên là bắt buộc'
        ],
        trim: true,
        maxlength: [
            100,
            'Tên không được vượt quá 100 ký tự'
        ]
    },
    role: {
        type: String,
        enum: Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserRole"]),
        default: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserRole"].STUDENT
    },
    avatar: {
        type: String,
        default: null
    },
    phone: {
        type: String,
        match: [
            /^[0-9]{10,11}$/,
            'Số điện thoại không hợp lệ'
        ]
    },
    dateOfBirth: {
        type: Date
    },
    isEmailVerified: {
        type: Boolean,
        default: false
    },
    profile: {
        bio: {
            type: String,
            maxlength: [
                500,
                'Bio không được vượt quá 500 ký tự'
            ]
        },
        experience: {
            type: String,
            maxlength: [
                1000,
                'Kinh nghiệm không được vượt quá 1000 ký tự'
            ]
        },
        education: {
            type: String,
            maxlength: [
                500,
                'Học vấn không được vượt quá 500 ký tự'
            ]
        },
        skills: [
            {
                type: String,
                maxlength: [
                    50,
                    'Kỹ năng không được vượt quá 50 ký tự'
                ]
            }
        ]
    }
}, {
    timestamps: true
});
// Index cho tìm kiếm
UserSchema.index({
    email: 1
});
UserSchema.index({
    role: 1
});
// Hash password trước khi lưu
UserSchema.pre('save', async function(next) {
    if (!this.isModified('password')) return next();
    try {
        const salt = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].genSalt(12);
        this.password = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(this.password, salt);
        next();
    } catch (error) {
        next(error);
    }
});
// Method để so sánh password
UserSchema.methods.comparePassword = async function(candidatePassword) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(candidatePassword, this.password);
};
// Loại bỏ password khỏi JSON response
UserSchema.methods.toJSON = function() {
    const userObject = this.toObject();
    delete userObject.password;
    return userObject;
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.User || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('User', UserSchema);
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateToken": (()=>generateToken),
    "getAuthUser": (()=>getAuthUser),
    "getCurrentUser": (()=>getCurrentUser),
    "hashPassword": (()=>hashPassword),
    "requireAuth": (()=>requireAuth),
    "requireRole": (()=>requireRole),
    "verifyToken": (()=>verifyToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.ts [app-route] (ecmascript)");
;
;
;
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
async function verifyToken(token) {
    try {
        const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, JWT_SECRET);
        return decoded;
    } catch (error) {
        console.error('Token verification failed:', error);
        return null;
    }
}
async function getAuthUser(request) {
    try {
        // Try to get token from cookie first
        let token = request.cookies.get('auth-token')?.value;
        // If no cookie, try Authorization header
        if (!token) {
            const authHeader = request.headers.get('authorization');
            if (authHeader && authHeader.startsWith('Bearer ')) {
                token = authHeader.substring(7);
            }
        }
        if (!token) {
            return null;
        }
        return await verifyToken(token);
    } catch (error) {
        console.error('Get auth user failed:', error);
        return null;
    }
}
async function requireAuth(request) {
    const user = await getAuthUser(request);
    if (!user) {
        return {
            error: new Response(JSON.stringify({
                success: false,
                message: 'Unauthorized - Token required'
            }), {
                status: 401,
                headers: {
                    'Content-Type': 'application/json'
                }
            })
        };
    }
    return {
        user
    };
}
async function requireRole(request, allowedRoles) {
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
        return authResult;
    }
    if (!allowedRoles.includes(authResult.user.role)) {
        return {
            error: new Response(JSON.stringify({
                success: false,
                message: 'Forbidden - Insufficient permissions'
            }), {
                status: 403,
                headers: {
                    'Content-Type': 'application/json'
                }
            })
        };
    }
    return authResult;
}
async function getCurrentUser(userId) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(userId).select('-password');
        return user;
    } catch (error) {
        console.error('Get current user failed:', error);
        return null;
    }
}
function generateToken(payload) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign(payload, JWT_SECRET, {
        expiresIn: '7d'
    });
}
async function hashPassword(password) {
    const bcrypt = __turbopack_context__.r("[project]/node_modules/bcryptjs/umd/index.js [app-route] (ecmascript)");
    const salt = await bcrypt.genSalt(12);
    return bcrypt.hash(password, salt);
}
}}),
"[project]/src/app/api/tests/[id]/submit/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Test$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Test.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$TestResult$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/TestResult.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-route] (ecmascript)");
;
;
;
;
;
;
async function POST(request, { params }) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { id } = params;
        // Check authentication
        const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAuthUser"])(request);
        if (!user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Unauthorized - Vui lòng đăng nhập'
            }, {
                status: 401
            });
        }
        // Find test
        const test = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Test$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(id);
        if (!test) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Không tìm thấy bài kiểm tra'
            }, {
                status: 404
            });
        }
        if (!test.isPublished) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Bài kiểm tra chưa được xuất bản'
            }, {
                status: 403
            });
        }
        const body = await request.json();
        const { answers, timeSpent, startedAt } = body;
        // Validation
        if (!answers || typeof answers !== 'object') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Dữ liệu câu trả lời không hợp lệ'
            }, {
                status: 400
            });
        }
        if (!timeSpent || timeSpent < 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Thời gian làm bài không hợp lệ'
            }, {
                status: 400
            });
        }
        // Check if user already submitted this test
        const existingResult = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$TestResult$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            studentId: user.userId,
            testId: id
        });
        if (existingResult) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Bạn đã làm bài kiểm tra này rồi'
            }, {
                status: 409
            });
        }
        // Grade the test
        const gradingResult = await gradeTest(test, answers);
        // Create test result
        const testResult = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$TestResult$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
            studentId: user.userId,
            testId: id,
            answers: gradingResult.answers,
            totalScore: gradingResult.totalScore,
            maxScore: gradingResult.maxScore,
            percentage: gradingResult.percentage,
            timeSpent,
            startedAt: new Date(startedAt),
            completedAt: new Date(),
            isPassed: gradingResult.percentage >= test.passingScore,
            detailedAnalysis: gradingResult.detailedAnalysis,
            gradingStatus: gradingResult.needsManualGrading ? 'pending' : 'completed'
        });
        await testResult.save();
        // Populate student and test info
        await testResult.populate([
            {
                path: 'studentId',
                select: 'name email'
            },
            {
                path: 'testId',
                select: 'title description type level'
            }
        ]);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Nộp bài thành công',
            data: testResult
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Submit test error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: 'Lỗi khi nộp bài kiểm tra',
            error: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function gradeTest(test, userAnswers) {
    let totalScore = 0;
    let maxScore = 0;
    let correctAnswers = 0;
    let incorrectAnswers = 0;
    let skippedAnswers = 0;
    let needsManualGrading = false;
    const answers = [];
    const skillScores = {};
    for (const question of test.questions){
        const userAnswer = userAnswers[question.id] || null;
        const isSkipped = !userAnswer;
        let isCorrect = false;
        let points = 0;
        let needsManual = false;
        maxScore += question.points;
        if (isSkipped) {
            skippedAnswers++;
        } else {
            // Grade based on question type
            switch(question.type){
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["QuestionType"].MULTIPLE_CHOICE:
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["QuestionType"].TRUE_FALSE:
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["QuestionType"].FILL_IN_BLANK:
                    isCorrect = normalizeAnswer(userAnswer) === normalizeAnswer(question.correctAnswer);
                    if (isCorrect) {
                        points = question.points;
                        correctAnswers++;
                    } else {
                        incorrectAnswers++;
                    }
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["QuestionType"].ESSAY:
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["QuestionType"].AUDIO_RESPONSE:
                    // These need manual grading
                    needsManual = true;
                    needsManualGrading = true;
                    // For now, give partial credit
                    points = Math.floor(question.points * 0.5);
                    break;
                default:
                    // Unknown question type
                    incorrectAnswers++;
                    break;
            }
        }
        totalScore += points;
        // Track skill scores
        const skill = getQuestionSkill(question.type, test.type);
        if (!skillScores[skill]) {
            skillScores[skill] = {
                score: 0,
                maxScore: 0
            };
        }
        skillScores[skill].score += points;
        skillScores[skill].maxScore += question.points;
        answers.push({
            questionId: question.id,
            questionOrder: question.order,
            userAnswer,
            correctAnswer: question.correctAnswer,
            isCorrect,
            points,
            maxPoints: question.points,
            timeSpent: 0,
            isSkipped,
            needsManualGrading: needsManual
        });
    }
    // Calculate skill breakdown
    const skillBreakdown = Object.entries(skillScores).map(([skill, scores])=>({
            skill,
            score: scores.score,
            maxScore: scores.maxScore,
            percentage: scores.maxScore > 0 ? Math.round(scores.score / scores.maxScore * 100) : 0
        }));
    const percentage = maxScore > 0 ? Math.round(totalScore / maxScore * 100) : 0;
    return {
        answers,
        totalScore,
        maxScore,
        percentage,
        needsManualGrading,
        detailedAnalysis: {
            correctAnswers,
            incorrectAnswers,
            skippedAnswers,
            skillBreakdown
        }
    };
}
function normalizeAnswer(answer) {
    if (typeof answer !== 'string') return '';
    return answer.toLowerCase().trim().replace(/\s+/g, ' ');
}
function getQuestionSkill(questionType, testType) {
    switch(questionType){
        case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["QuestionType"].MULTIPLE_CHOICE:
        case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["QuestionType"].FILL_IN_BLANK:
            return testType === 'vocabulary' ? 'Từ vựng' : 'Ngữ pháp';
        case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["QuestionType"].ESSAY:
            return 'Viết';
        case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["QuestionType"].AUDIO_RESPONSE:
            return 'Nói';
        case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["QuestionType"].TRUE_FALSE:
            return 'Đọc hiểu';
        default:
            return 'Tổng hợp';
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__a0ef2145._.js.map