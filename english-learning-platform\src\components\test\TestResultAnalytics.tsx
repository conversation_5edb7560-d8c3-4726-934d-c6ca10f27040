"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import { CheckCircle, XCircle, Clock, Target, TrendingUp, Award } from "lucide-react";

interface TestResultAnalyticsProps {
  result: {
    totalScore: number;
    maxScore: number;
    percentage: number;
    timeSpent: number;
    answers: Array<{
      questionId: string;
      questionIndex: number;
      answer: string | string[];
      isCorrect: boolean;
      points: number;
      maxPoints: number;
      feedback?: string;
      questionType: string;
    }>;
    skillBreakdown: {
      listening: { score: number; maxScore: number };
      speaking: { score: number; maxScore: number };
      reading: { score: number; maxScore: number };
      writing: { score: number; maxScore: number };
    };
  };
  test: {
    title: string;
    duration: number;
    passingScore: number;
    totalQuestions: number;
  };
}

export default function TestResultAnalytics({ result, test }: TestResultAnalyticsProps) {
  const isPassed = result.percentage >= test.passingScore;
  
  // Prepare data for charts
  const skillData = Object.entries(result.skillBreakdown).map(([skill, data]) => ({
    skill: skill === 'listening' ? 'Nghe' : 
           skill === 'speaking' ? 'Nói' : 
           skill === 'reading' ? 'Đọc' : 'Viết',
    score: data.score,
    maxScore: data.maxScore,
    percentage: data.maxScore > 0 ? Math.round((data.score / data.maxScore) * 100) : 0
  })).filter(item => item.maxScore > 0);

  const questionTypeData = result.answers.reduce((acc, answer) => {
    const type = answer.questionType;
    if (!acc[type]) {
      acc[type] = { correct: 0, total: 0 };
    }
    acc[type].total++;
    if (answer.isCorrect) {
      acc[type].correct++;
    }
    return acc;
  }, {} as Record<string, { correct: number; total: number }>);

  const questionTypeChartData = Object.entries(questionTypeData).map(([type, data]) => ({
    type: type === 'multiple_choice' ? 'Trắc nghiệm' :
          type === 'listening_multiple_choice' ? 'Nghe - Trắc nghiệm' :
          type === 'listening_fill_blank' ? 'Nghe - Điền từ' :
          type === 'speaking_record' ? 'Nói' :
          type === 'reading_comprehension' ? 'Đọc hiểu' :
          type === 'writing_essay' ? 'Viết luận' : type,
    correct: data.correct,
    total: data.total,
    percentage: Math.round((data.correct / data.total) * 100)
  }));

  const timeData = [
    {
      metric: 'Thời gian sử dụng',
      value: result.timeSpent,
      max: test.duration,
      percentage: Math.round((result.timeSpent / test.duration) * 100)
    }
  ];

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  const getGradeLevel = (percentage: number) => {
    if (percentage >= 90) return { grade: 'A', color: 'bg-green-500', label: 'Xuất sắc' };
    if (percentage >= 80) return { grade: 'B', color: 'bg-blue-500', label: 'Giỏi' };
    if (percentage >= 70) return { grade: 'C', color: 'bg-yellow-500', label: 'Khá' };
    if (percentage >= 60) return { grade: 'D', color: 'bg-orange-500', label: 'Trung bình' };
    return { grade: 'F', color: 'bg-red-500', label: 'Yếu' };
  };

  const gradeInfo = getGradeLevel(result.percentage);

  return (
    <div className="space-y-6">
      {/* Overall Result */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Kết quả tổng quan</span>
            <Badge className={`${gradeInfo.color} text-white`}>
              {gradeInfo.grade} - {gradeInfo.label}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className={`text-4xl font-bold ${isPassed ? 'text-green-600' : 'text-red-600'}`}>
                {result.percentage}%
              </div>
              <p className="text-sm text-gray-600">Điểm tổng</p>
              <div className="mt-2">
                {isPassed ? (
                  <CheckCircle className="h-6 w-6 text-green-600 mx-auto" />
                ) : (
                  <XCircle className="h-6 w-6 text-red-600 mx-auto" />
                )}
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {result.totalScore}/{result.maxScore}
              </div>
              <p className="text-sm text-gray-600">Điểm số</p>
              <Progress value={result.percentage} className="mt-2" />
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {Math.floor(result.timeSpent / 60)}:{(result.timeSpent % 60).toString().padStart(2, '0')}
              </div>
              <p className="text-sm text-gray-600">Thời gian</p>
              <div className="flex items-center justify-center mt-2">
                <Clock className="h-4 w-4 text-gray-500 mr-1" />
                <span className="text-xs text-gray-500">
                  {Math.round((result.timeSpent / (test.duration * 60)) * 100)}% thời gian
                </span>
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {result.answers.filter(a => a.isCorrect).length}/{result.answers.length}
              </div>
              <p className="text-sm text-gray-600">Câu đúng</p>
              <div className="flex items-center justify-center mt-2">
                <Target className="h-4 w-4 text-gray-500 mr-1" />
                <span className="text-xs text-gray-500">
                  {Math.round((result.answers.filter(a => a.isCorrect).length / result.answers.length) * 100)}% chính xác
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Analytics */}
      <Tabs defaultValue="skills" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="skills">Kỹ năng</TabsTrigger>
          <TabsTrigger value="questions">Câu hỏi</TabsTrigger>
          <TabsTrigger value="performance">Hiệu suất</TabsTrigger>
          <TabsTrigger value="recommendations">Gợi ý</TabsTrigger>
        </TabsList>

        {/* Skills Breakdown */}
        <TabsContent value="skills" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Phân tích theo kỹ năng</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Skills Bar Chart */}
                <div>
                  <h4 className="text-lg font-medium mb-4">Điểm số theo kỹ năng</h4>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={skillData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="skill" />
                      <YAxis />
                      <Tooltip 
                        formatter={(value, name) => [
                          `${value}%`, 
                          name === 'percentage' ? 'Phần trăm' : 'Điểm'
                        ]}
                      />
                      <Bar dataKey="percentage" fill="#3B82F6" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>

                {/* Skills Radar Chart */}
                <div>
                  <h4 className="text-lg font-medium mb-4">Biểu đồ radar kỹ năng</h4>
                  <ResponsiveContainer width="100%" height={300}>
                    <RadarChart data={skillData}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="skill" />
                      <PolarRadiusAxis angle={90} domain={[0, 100]} />
                      <Radar
                        name="Điểm"
                        dataKey="percentage"
                        stroke="#3B82F6"
                        fill="#3B82F6"
                        fillOpacity={0.3}
                      />
                      <Tooltip />
                    </RadarChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Skills Details */}
              <div className="mt-6 space-y-4">
                {skillData.map((skill, index) => (
                  <div key={skill.skill} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className={`w-4 h-4 rounded-full`} style={{ backgroundColor: COLORS[index] }} />
                      <div>
                        <h5 className="font-medium">{skill.skill}</h5>
                        <p className="text-sm text-gray-600">
                          {skill.score}/{skill.maxScore} điểm
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold">{skill.percentage}%</div>
                      <Progress value={skill.percentage} className="w-24" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Question Analysis */}
        <TabsContent value="questions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Phân tích theo loại câu hỏi</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Question Type Chart */}
                <div>
                  <h4 className="text-lg font-medium mb-4">Tỷ lệ đúng theo loại câu hỏi</h4>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={questionTypeChartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="type" angle={-45} textAnchor="end" height={100} />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="percentage" fill="#10B981" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>

                {/* Question Details */}
                <div>
                  <h4 className="text-lg font-medium mb-4">Chi tiết câu hỏi</h4>
                  <div className="space-y-3">
                    {questionTypeChartData.map((item, index) => (
                      <div key={item.type} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          <span className="font-medium">{item.type}</span>
                          <Badge variant="outline">
                            {item.correct}/{item.total}
                          </Badge>
                        </div>
                        <Progress value={item.percentage} />
                        <p className="text-sm text-gray-600 mt-1">
                          {item.percentage}% chính xác
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Analysis */}
        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Phân tích hiệu suất</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-6 bg-blue-50 rounded-lg">
                  <TrendingUp className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-blue-600">
                    {Math.round((result.timeSpent / (test.duration * 60)) * 100)}%
                  </div>
                  <p className="text-sm text-gray-600">Thời gian sử dụng</p>
                </div>
                
                <div className="text-center p-6 bg-green-50 rounded-lg">
                  <Award className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-green-600">
                    {result.answers.filter(a => a.isCorrect).length}
                  </div>
                  <p className="text-sm text-gray-600">Câu trả lời đúng</p>
                </div>
                
                <div className="text-center p-6 bg-purple-50 rounded-lg">
                  <Target className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-purple-600">
                    {Math.round((result.totalScore / result.maxScore) * 100)}%
                  </div>
                  <p className="text-sm text-gray-600">Hiệu quả làm bài</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Recommendations */}
        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Gợi ý cải thiện</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {skillData.map((skill) => {
                  if (skill.percentage < 70) {
                    return (
                      <div key={skill.skill} className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <h5 className="font-medium text-yellow-800 mb-2">
                          Cần cải thiện: {skill.skill}
                        </h5>
                        <p className="text-sm text-yellow-700">
                          Điểm của bạn ở kỹ năng này là {skill.percentage}%. 
                          Hãy dành thêm thời gian luyện tập để cải thiện.
                        </p>
                      </div>
                    );
                  }
                  return null;
                })}
                
                {result.percentage >= test.passingScore && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h5 className="font-medium text-green-800 mb-2">
                      🎉 Chúc mừng! Bạn đã vượt qua bài kiểm tra
                    </h5>
                    <p className="text-sm text-green-700">
                      Tiếp tục duy trì và phát triển các kỹ năng của bạn.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
