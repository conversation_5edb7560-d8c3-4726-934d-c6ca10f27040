import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return authResult.error;
    }

    const { user } = authResult;
    await connectDB();

    // Get user data
    const userDoc = await User.findById(user.userId);

    // For now, return mock enrolled courses - in a real app you'd track enrollments
    const enrolledCourses = [
      {
        _id: '1',
        title: 'Tiế<PERSON><PERSON> cho <PERSON>ờ<PERSON>ớ<PERSON>',
        thumbnail: '/api/placeholder/300/200',
        progress: 75,
        totalLessons: 25,
        completedLessons: 19,
        lastAccessed: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
        instructor: {
          name: '<PERSON><PERSON>'
        },
        nextLesson: '<PERSON><PERSON>i 20: <PERSON><PERSON><PERSON> hiện tại hoàn thành'
      },
      {
        _id: '2',
        title: '<PERSON><PERSON><PERSON><PERSON> IELTS 6.5+',
        thumbnail: '/api/placeholder/300/200',
        progress: 40,
        totalLessons: 20,
        completedLessons: 8,
        lastAccessed: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(), // 4 days ago
        instructor: {
          name: 'Thầy Đức Minh'
        },
        nextLesson: 'Bài 9: Listening Part 2 - Maps'
      }
    ];

    return NextResponse.json({
      success: true,
      data: enrolledCourses
    });

  } catch (error) {
    console.error('Get dashboard courses error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi hệ thống, vui lòng thử lại sau',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
