import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Course from '@/models/Course';
import Enrollment from '@/models/Enrollment';

interface RouteParams {
  params: {
    certificateId: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { certificateId } = await params;

    // Find enrollment with certificate
    const enrollment = await Enrollment.findOne({
      certificateId: certificateId,
      certificateIssued: true
    }).populate([
      { path: 'studentId', select: 'name email' },
      { path: 'courseId', select: 'title description duration' }
    ]);

    if (!enrollment) {
      return NextResponse.json({
        success: false,
        message: 'Chứng chỉ không tồn tại hoặc không hợp lệ'
      }, { status: 404 });
    }

    // Get course and instructor info
    const course = await Course.findById(enrollment.courseId).populate('teacherId', 'name');

    // Return certificate verification data
    return NextResponse.json({
      success: true,
      data: {
        isValid: true,
        certificateId: certificateId,
        studentName: (enrollment.studentId as any).name,
        courseName: (enrollment.courseId as any).title,
        courseDescription: (enrollment.courseId as any).description,
        instructorName: course?.teacherId?.name || 'Giảng viên',
        completedAt: enrollment.completedAt,
        issuedAt: enrollment.completedAt,
        duration: (enrollment.courseId as any).duration,
        totalTimeSpent: Math.floor(enrollment.totalTimeSpent / 3600),
        progress: enrollment.progress,
        enrolledAt: enrollment.enrolledAt,
        verificationDate: new Date()
      }
    });

  } catch (error) {
    console.error('Certificate verification error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi xác thực chứng chỉ',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
