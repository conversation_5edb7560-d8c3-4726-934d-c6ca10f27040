{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI!;\n\nif (!MONGODB_URI) {\n  throw new Error('<PERSON>ui lòng định nghĩa biến môi trường MONGODB_URI trong .env.local');\n}\n\n/**\n * Global được sử dụng để duy trì kết nối cached trong môi trường development.\n * Điều này ngăn chặn việc tạo quá nhiều kết nối trong quá trình hot reloading.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Kết nối MongoDB thành công');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    console.error('❌ Lỗi kết nối MongoDB:', e);\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;CAGC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/types/index.ts"], "sourcesContent": ["// Enum types\nexport enum UserRole {\n  ADMIN = 'admin',\n  TEACHER = 'teacher',\n  STUDENT = 'student'\n}\n\nexport enum CourseCategory {\n  LISTENING = 'listening',\n  SPEAKING = 'speaking',\n  READING = 'reading',\n  WRITING = 'writing',\n  COMPREHENSIVE = 'comprehensive'\n}\n\nexport enum CourseLevel {\n  BEGINNER = 'beginner',\n  INTERMEDIATE = 'intermediate',\n  ADVANCED = 'advanced'\n}\n\nexport enum TestType {\n  VOCABULARY = 'vocabulary',\n  GRAMMAR = 'grammar',\n  LISTENING = 'listening',\n  SPEAKING = 'speaking',\n  READING = 'reading',\n  WRITING = 'writing',\n  COMPREHENSIVE = 'comprehensive',\n  PRACTICE = 'practice'\n}\n\nexport enum QuestionType {\n  MULTIPLE_CHOICE = 'multiple_choice',\n  FILL_IN_BLANK = 'fill_in_blank',\n  TRUE_FALSE = 'true_false',\n  ESSAY = 'essay',\n  AUDIO_RESPONSE = 'audio_response',\n  DRAG_DROP = 'drag_drop',\n  MATCHING = 'matching'\n}\n\nexport enum PaymentStatus {\n  PENDING = 'pending',\n  COMPLETED = 'completed',\n  FAILED = 'failed'\n}\n\n// User types\nexport interface User {\n  _id: string;\n  email: string;\n  name: string;\n  role: UserRole;\n  avatar?: string;\n  phone?: string;\n  dateOfBirth?: Date;\n  isEmailVerified: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n  profile?: UserProfile;\n}\n\nexport interface UserProfile {\n  bio?: string;\n  experience?: string; // for teachers\n  education?: string;\n  skills?: string[];\n}\n\n// Course types\nexport interface Course {\n  _id: string;\n  title: string;\n  description: string;\n  shortDescription: string;\n  teacherId: string;\n  teacher?: User;\n  category: CourseCategory;\n  level: CourseLevel;\n  price: number;\n  duration: number; // in hours\n  thumbnail?: string;\n  videoIntro?: string;\n  isPublished: boolean;\n  curriculum: Lesson[];\n  ratings: Rating[];\n  averageRating: number;\n  totalStudents: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Lesson {\n  title: string;\n  description: string;\n  videoUrl?: string;\n  materials: string[];\n  duration: number; // in minutes\n}\n\nexport interface Rating {\n  userId: string;\n  user?: User;\n  rating: number;\n  comment: string;\n  createdAt: Date;\n}\n\n// Enrollment types\nexport interface Enrollment {\n  _id: string;\n  studentId: string;\n  student?: User;\n  courseId: string;\n  course?: Course;\n  enrolledAt: Date;\n  progress: number; // percentage\n  completedLessons: number[];\n  lastAccessedAt: Date;\n  paymentStatus: PaymentStatus;\n  paymentId?: string;\n}\n\n// Test types\nexport interface Test {\n  _id: string;\n  title: string;\n  description: string;\n  type: TestType;\n  level: CourseLevel;\n  duration: number; // in minutes\n  totalQuestions: number;\n  passingScore: number;\n  questions: Question[];\n  createdBy: string;\n  creator?: User;\n  isPublished: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Question {\n  type: QuestionType;\n  question: string;\n  options?: string[]; // for multiple choice\n  correctAnswer: string;\n  points: number;\n  audioUrl?: string; // for listening questions\n  imageUrl?: string;\n}\n\nexport interface TestResult {\n  _id: string;\n  studentId: string;\n  student?: User;\n  testId: string;\n  test?: Test;\n  answers: Answer[];\n  totalScore: number;\n  percentage: number;\n  timeSpent: number; // in minutes\n  startedAt: Date;\n  completedAt: Date;\n  feedback?: string;\n}\n\nexport interface Answer {\n  questionIndex: number;\n  answer: string;\n  isCorrect: boolean;\n  points: number;\n}\n\n// API Response types\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\n// Form types\nexport interface LoginForm {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterForm {\n  name: string;\n  email: string;\n  password: string;\n  confirmPassword: string;\n  role?: UserRole;\n}\n\nexport interface CourseForm {\n  title: string;\n  description: string;\n  shortDescription: string;\n  category: CourseCategory;\n  level: CourseLevel;\n  price: number;\n  duration: number;\n  thumbnail?: string;\n  videoIntro?: string;\n  curriculum: Lesson[];\n}\n\n// Dashboard types\nexport interface DashboardStats {\n  totalCourses: number;\n  totalStudents: number;\n  totalRevenue: number;\n  totalTests: number;\n}\n\nexport interface StudentDashboard {\n  enrolledCourses: Course[];\n  recentTests: TestResult[];\n  progress: {\n    courseId: string;\n    courseName: string;\n    progress: number;\n  }[];\n  stats: {\n    totalCourses: number;\n    completedCourses: number;\n    averageScore: number;\n    totalTestsTaken: number;\n  };\n}\n"], "names": [], "mappings": "AAAA,aAAa;;;;;;;;;AACN,IAAA,AAAK,kCAAA;;;;WAAA;;AAML,IAAA,AAAK,wCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,qCAAA;;;;WAAA;;AAML,IAAA,AAAK,kCAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,sCAAA;;;;;;;;WAAA;;AAUL,IAAA,AAAK,uCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/models/Test.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { TestType, CourseLevel, QuestionType } from '@/types';\n\nexport interface ITest extends Document {\n  title: string;\n  description: string;\n  type: TestType;\n  level: CourseLevel;\n  duration: number; // in minutes\n  totalQuestions: number;\n  passingScore: number;\n  instructions: string;\n  questions: IQuestion[];\n  createdBy: mongoose.Types.ObjectId;\n  isPublished: boolean;\n  tags: string[];\n  difficulty: 'easy' | 'medium' | 'hard';\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface IQuestion {\n  type: QuestionType;\n  question: string;\n  options?: string[]; // for multiple choice\n  correctAnswer: string | string[]; // can be array for multiple correct answers\n  points: number;\n  explanation?: string;\n  audioUrl?: string; // for listening questions\n  imageUrl?: string;\n  timeLimit?: number; // specific time limit for this question\n  order: number;\n}\n\nconst QuestionSchema = new Schema({\n  type: {\n    type: String,\n    enum: Object.values(QuestionType),\n    required: [true, '<PERSON><PERSON><PERSON> câu hỏi là bắt buộc']\n  },\n  question: {\n    type: String,\n    required: [true, 'Nội dung câu hỏi là bắt buộc'],\n    maxlength: [2000, 'Câu hỏi không được vượt quá 2000 ký tự']\n  },\n  options: [{\n    type: String,\n    maxlength: [500, 'Tùy chọn không được vượt quá 500 ký tự']\n  }],\n  correctAnswer: {\n    type: Schema.Types.Mixed, // Can be string or array\n    required: [true, 'Đáp án đúng là bắt buộc']\n  },\n  points: {\n    type: Number,\n    required: [true, 'Điểm số là bắt buộc'],\n    min: [0, 'Điểm số không được âm'],\n    max: [100, 'Điểm số không được vượt quá 100']\n  },\n  explanation: {\n    type: String,\n    maxlength: [1000, 'Giải thích không được vượt quá 1000 ký tự']\n  },\n  audioUrl: {\n    type: String,\n    validate: {\n      validator: function(v: string) {\n        return !v || /^https?:\\/\\/.+/.test(v);\n      },\n      message: 'URL audio không hợp lệ'\n    }\n  },\n  imageUrl: {\n    type: String,\n    validate: {\n      validator: function(v: string) {\n        return !v || /^https?:\\/\\/.+/.test(v);\n      },\n      message: 'URL hình ảnh không hợp lệ'\n    }\n  },\n  timeLimit: {\n    type: Number,\n    min: [10, 'Thời gian tối thiểu là 10 giây']\n  },\n  order: {\n    type: Number,\n    required: [true, 'Thứ tự câu hỏi là bắt buộc'],\n    min: [1, 'Thứ tự phải bắt đầu từ 1']\n  }\n});\n\nconst TestSchema = new Schema<ITest>({\n  title: {\n    type: String,\n    required: [true, 'Tiêu đề bài kiểm tra là bắt buộc'],\n    trim: true,\n    maxlength: [200, 'Tiêu đề không được vượt quá 200 ký tự']\n  },\n  description: {\n    type: String,\n    required: [true, 'Mô tả bài kiểm tra là bắt buộc'],\n    maxlength: [2000, 'Mô tả không được vượt quá 2000 ký tự']\n  },\n  type: {\n    type: String,\n    enum: Object.values(TestType),\n    required: [true, 'Loại bài kiểm tra là bắt buộc']\n  },\n  level: {\n    type: String,\n    enum: Object.values(CourseLevel),\n    required: [true, 'Trình độ bài kiểm tra là bắt buộc']\n  },\n  duration: {\n    type: Number,\n    required: [true, 'Thời lượng bài kiểm tra là bắt buộc'],\n    min: [5, 'Thời lượng tối thiểu là 5 phút'],\n    max: [300, 'Thời lượng tối đa là 300 phút']\n  },\n  totalQuestions: {\n    type: Number,\n    required: [true, 'Tổng số câu hỏi là bắt buộc'],\n    min: [1, 'Phải có ít nhất 1 câu hỏi']\n  },\n  passingScore: {\n    type: Number,\n    required: [true, 'Điểm đạt là bắt buộc'],\n    min: [0, 'Điểm đạt không được âm'],\n    max: [100, 'Điểm đạt không được vượt quá 100%']\n  },\n  instructions: {\n    type: String,\n    required: [true, 'Hướng dẫn làm bài là bắt buộc'],\n    maxlength: [3000, 'Hướng dẫn không được vượt quá 3000 ký tự']\n  },\n  questions: [QuestionSchema],\n  createdBy: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: [true, 'Người tạo bài kiểm tra là bắt buộc']\n  },\n  isPublished: {\n    type: Boolean,\n    default: false\n  },\n  tags: [{\n    type: String,\n    trim: true,\n    maxlength: [50, 'Tag không được vượt quá 50 ký tự']\n  }],\n  difficulty: {\n    type: String,\n    enum: ['easy', 'medium', 'hard'],\n    default: 'medium'\n  }\n}, {\n  timestamps: true\n});\n\n// Indexes cho tìm kiếm và hiệu suất\nTestSchema.index({ title: 'text', description: 'text', tags: 'text' });\nTestSchema.index({ type: 1, level: 1 });\nTestSchema.index({ createdBy: 1 });\nTestSchema.index({ isPublished: 1 });\nTestSchema.index({ difficulty: 1 });\nTestSchema.index({ createdAt: -1 });\n\n// Middleware để cập nhật totalQuestions\nTestSchema.pre('save', function(next) {\n  this.totalQuestions = this.questions.length;\n  next();\n});\n\n// Virtual để tính điểm tối đa\nTestSchema.virtual('maxScore').get(function() {\n  return this.questions.reduce((total, question) => total + question.points, 0);\n});\n\nexport default mongoose.models.Test || mongoose.model<ITest>('Test', TestSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAiCA,MAAM,iBAAiB,IAAI,yGAAA,CAAA,SAAM,CAAC;IAChC,MAAM;QACJ,MAAM;QACN,MAAM,OAAO,MAAM,CAAC,uHAAA,CAAA,eAAY;QAChC,UAAU;YAAC;YAAM;SAA2B;IAC9C;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAA+B;QAChD,WAAW;YAAC;YAAM;SAAyC;IAC7D;IACA,SAAS;QAAC;YACR,MAAM;YACN,WAAW;gBAAC;gBAAK;aAAyC;QAC5D;KAAE;IACF,eAAe;QACb,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK;QACxB,UAAU;YAAC;YAAM;SAA0B;IAC7C;IACA,QAAQ;QACN,MAAM;QACN,UAAU;YAAC;YAAM;SAAsB;QACvC,KAAK;YAAC;YAAG;SAAwB;QACjC,KAAK;YAAC;YAAK;SAAkC;IAC/C;IACA,aAAa;QACX,MAAM;QACN,WAAW;YAAC;YAAM;SAA4C;IAChE;IACA,UAAU;QACR,MAAM;QACN,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,CAAC,KAAK,iBAAiB,IAAI,CAAC;YACrC;YACA,SAAS;QACX;IACF;IACA,UAAU;QACR,MAAM;QACN,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,CAAC,KAAK,iBAAiB,IAAI,CAAC;YACrC;YACA,SAAS;QACX;IACF;IACA,WAAW;QACT,MAAM;QACN,KAAK;YAAC;YAAI;SAAiC;IAC7C;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAA6B;QAC9C,KAAK;YAAC;YAAG;SAA2B;IACtC;AACF;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAQ;IACnC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAmC;QACpD,MAAM;QACN,WAAW;YAAC;YAAK;SAAwC;IAC3D;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAAiC;QAClD,WAAW;YAAC;YAAM;SAAuC;IAC3D;IACA,MAAM;QACJ,MAAM;QACN,MAAM,OAAO,MAAM,CAAC,uHAAA,CAAA,WAAQ;QAC5B,UAAU;YAAC;YAAM;SAAgC;IACnD;IACA,OAAO;QACL,MAAM;QACN,MAAM,OAAO,MAAM,CAAC,uHAAA,CAAA,cAAW;QAC/B,UAAU;YAAC;YAAM;SAAoC;IACvD;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAsC;QACvD,KAAK;YAAC;YAAG;SAAiC;QAC1C,KAAK;YAAC;YAAK;SAAgC;IAC7C;IACA,gBAAgB;QACd,MAAM;QACN,UAAU;YAAC;YAAM;SAA8B;QAC/C,KAAK;YAAC;YAAG;SAA4B;IACvC;IACA,cAAc;QACZ,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,KAAK;YAAC;YAAG;SAAyB;QAClC,KAAK;YAAC;YAAK;SAAoC;IACjD;IACA,cAAc;QACZ,MAAM;QACN,UAAU;YAAC;YAAM;SAAgC;QACjD,WAAW;YAAC;YAAM;SAA2C;IAC/D;IACA,WAAW;QAAC;KAAe;IAC3B,WAAW;QACT,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;YAAC;YAAM;SAAqC;IACxD;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;gBAAC;gBAAI;aAAmC;QACrD;KAAE;IACF,YAAY;QACV,MAAM;QACN,MAAM;YAAC;YAAQ;YAAU;SAAO;QAChC,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,oCAAoC;AACpC,WAAW,KAAK,CAAC;IAAE,OAAO;IAAQ,aAAa;IAAQ,MAAM;AAAO;AACpE,WAAW,KAAK,CAAC;IAAE,MAAM;IAAG,OAAO;AAAE;AACrC,WAAW,KAAK,CAAC;IAAE,WAAW;AAAE;AAChC,WAAW,KAAK,CAAC;IAAE,aAAa;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,YAAY;AAAE;AACjC,WAAW,KAAK,CAAC;IAAE,WAAW,CAAC;AAAE;AAEjC,wCAAwC;AACxC,WAAW,GAAG,CAAC,QAAQ,SAAS,IAAI;IAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM;IAC3C;AACF;AAEA,8BAA8B;AAC9B,WAAW,OAAO,CAAC,YAAY,GAAG,CAAC;IACjC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,WAAa,QAAQ,SAAS,MAAM,EAAE;AAC7E;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/models/TestResult.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface ITestResult extends Document {\n  studentId: mongoose.Types.ObjectId;\n  testId: mongoose.Types.ObjectId;\n  answers: IAnswer[];\n  totalScore: number;\n  maxScore: number;\n  percentage: number;\n  timeSpent: number; // in seconds\n  startedAt: Date;\n  completedAt: Date;\n  isPassed: boolean;\n  feedback?: string;\n  detailedAnalysis: {\n    correctAnswers: number;\n    incorrectAnswers: number;\n    skippedAnswers: number;\n    skillBreakdown: {\n      skill: string;\n      score: number;\n      maxScore: number;\n      percentage: number;\n    }[];\n  };\n  gradingStatus: 'auto' | 'pending' | 'manual' | 'completed';\n  gradedBy?: mongoose.Types.ObjectId;\n  gradedAt?: Date;\n}\n\nexport interface IAnswer {\n  questionId: string;\n  questionOrder: number;\n  userAnswer: string | string[];\n  correctAnswer: string | string[];\n  isCorrect: boolean;\n  points: number;\n  maxPoints: number;\n  timeSpent: number; // in seconds\n  isSkipped: boolean;\n  needsManualGrading: boolean; // for essay questions\n  manualScore?: number;\n  feedback?: string;\n}\n\nconst AnswerSchema = new Schema({\n  questionId: {\n    type: String,\n    required: [true, 'ID câu hỏi là bắt buộc']\n  },\n  questionOrder: {\n    type: Number,\n    required: [true, 'Thứ tự câu hỏi là bắt buộc']\n  },\n  userAnswer: {\n    type: Schema.Types.Mixed, // Can be string or array\n    default: null\n  },\n  correctAnswer: {\n    type: Schema.Types.Mixed,\n    required: [true, 'Đáp án đúng là bắt buộc']\n  },\n  isCorrect: {\n    type: Boolean,\n    default: false\n  },\n  points: {\n    type: Number,\n    default: 0,\n    min: [0, 'Điểm không được âm']\n  },\n  maxPoints: {\n    type: Number,\n    required: [true, 'Điểm tối đa là bắt buộc'],\n    min: [0, 'Điểm tối đa không được âm']\n  },\n  timeSpent: {\n    type: Number,\n    default: 0,\n    min: [0, 'Thời gian không được âm']\n  },\n  isSkipped: {\n    type: Boolean,\n    default: false\n  },\n  needsManualGrading: {\n    type: Boolean,\n    default: false\n  },\n  manualScore: {\n    type: Number,\n    min: [0, 'Điểm thủ công không được âm']\n  },\n  feedback: {\n    type: String,\n    maxlength: [1000, 'Phản hồi không được vượt quá 1000 ký tự']\n  }\n});\n\nconst SkillBreakdownSchema = new Schema({\n  skill: {\n    type: String,\n    required: [true, 'Tên kỹ năng là bắt buộc']\n  },\n  score: {\n    type: Number,\n    required: [true, 'Điểm kỹ năng là bắt buộc'],\n    min: [0, 'Điểm không được âm']\n  },\n  maxScore: {\n    type: Number,\n    required: [true, 'Điểm tối đa kỹ năng là bắt buộc'],\n    min: [0, 'Điểm tối đa không được âm']\n  },\n  percentage: {\n    type: Number,\n    required: [true, 'Phần trăm kỹ năng là bắt buộc'],\n    min: [0, 'Phần trăm không được âm'],\n    max: [100, 'Phần trăm không được vượt quá 100']\n  }\n});\n\nconst TestResultSchema = new Schema<ITestResult>({\n  studentId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: [true, 'ID học viên là bắt buộc']\n  },\n  testId: {\n    type: Schema.Types.ObjectId,\n    ref: 'Test',\n    required: [true, 'ID bài kiểm tra là bắt buộc']\n  },\n  answers: [AnswerSchema],\n  totalScore: {\n    type: Number,\n    required: [true, 'Tổng điểm là bắt buộc'],\n    min: [0, 'Tổng điểm không được âm']\n  },\n  maxScore: {\n    type: Number,\n    required: [true, 'Điểm tối đa là bắt buộc'],\n    min: [0, 'Điểm tối đa không được âm']\n  },\n  percentage: {\n    type: Number,\n    required: [true, 'Phần trăm điểm là bắt buộc'],\n    min: [0, 'Phần trăm không được âm'],\n    max: [100, 'Phần trăm không được vượt quá 100']\n  },\n  timeSpent: {\n    type: Number,\n    required: [true, 'Thời gian làm bài là bắt buộc'],\n    min: [0, 'Thời gian không được âm']\n  },\n  startedAt: {\n    type: Date,\n    required: [true, 'Thời gian bắt đầu là bắt buộc']\n  },\n  completedAt: {\n    type: Date,\n    required: [true, 'Thời gian hoàn thành là bắt buộc']\n  },\n  isPassed: {\n    type: Boolean,\n    required: [true, 'Trạng thái đạt/không đạt là bắt buộc']\n  },\n  feedback: {\n    type: String,\n    maxlength: [2000, 'Phản hồi không được vượt quá 2000 ký tự']\n  },\n  detailedAnalysis: {\n    correctAnswers: {\n      type: Number,\n      required: [true, 'Số câu đúng là bắt buộc'],\n      min: [0, 'Số câu đúng không được âm']\n    },\n    incorrectAnswers: {\n      type: Number,\n      required: [true, 'Số câu sai là bắt buộc'],\n      min: [0, 'Số câu sai không được âm']\n    },\n    skippedAnswers: {\n      type: Number,\n      required: [true, 'Số câu bỏ qua là bắt buộc'],\n      min: [0, 'Số câu bỏ qua không được âm']\n    },\n    skillBreakdown: [SkillBreakdownSchema]\n  },\n  gradingStatus: {\n    type: String,\n    enum: ['auto', 'pending', 'manual', 'completed'],\n    default: 'auto'\n  },\n  gradedBy: {\n    type: Schema.Types.ObjectId,\n    ref: 'User'\n  },\n  gradedAt: {\n    type: Date\n  }\n}, {\n  timestamps: true\n});\n\n// Indexes cho tìm kiếm và hiệu suất\nTestResultSchema.index({ studentId: 1, testId: 1 });\nTestResultSchema.index({ studentId: 1, completedAt: -1 });\nTestResultSchema.index({ testId: 1, completedAt: -1 });\nTestResultSchema.index({ gradingStatus: 1 });\nTestResultSchema.index({ isPassed: 1 });\n\n// Middleware để tính toán các giá trị\nTestResultSchema.pre('save', function(next) {\n  // Tính phần trăm\n  if (this.maxScore > 0) {\n    this.percentage = Math.round((this.totalScore / this.maxScore) * 100);\n  }\n  \n  // Cập nhật thời gian chấm điểm nếu trạng thái thay đổi\n  if (this.isModified('gradingStatus') && this.gradingStatus === 'completed') {\n    this.gradedAt = new Date();\n  }\n  \n  next();\n});\n\nexport default mongoose.models.TestResult || mongoose.model<ITestResult>('TestResult', TestResultSchema);\n"], "names": [], "mappings": ";;;AAAA;;AA6CA,MAAM,eAAe,IAAI,yGAAA,CAAA,SAAM,CAAC;IAC9B,YAAY;QACV,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;IAC5C;IACA,eAAe;QACb,MAAM;QACN,UAAU;YAAC;YAAM;SAA6B;IAChD;IACA,YAAY;QACV,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK;QACxB,SAAS;IACX;IACA,eAAe;QACb,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK;QACxB,UAAU;YAAC;YAAM;SAA0B;IAC7C;IACA,WAAW;QACT,MAAM;QACN,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,SAAS;QACT,KAAK;YAAC;YAAG;SAAqB;IAChC;IACA,WAAW;QACT,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,KAAK;YAAC;YAAG;SAA4B;IACvC;IACA,WAAW;QACT,MAAM;QACN,SAAS;QACT,KAAK;YAAC;YAAG;SAA0B;IACrC;IACA,WAAW;QACT,MAAM;QACN,SAAS;IACX;IACA,oBAAoB;QAClB,MAAM;QACN,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,KAAK;YAAC;YAAG;SAA8B;IACzC;IACA,UAAU;QACR,MAAM;QACN,WAAW;YAAC;YAAM;SAA0C;IAC9D;AACF;AAEA,MAAM,uBAAuB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACtC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;IAC7C;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,KAAK;YAAC;YAAG;SAAqB;IAChC;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAkC;QACnD,KAAK;YAAC;YAAG;SAA4B;IACvC;IACA,YAAY;QACV,MAAM;QACN,UAAU;YAAC;YAAM;SAAgC;QACjD,KAAK;YAAC;YAAG;SAA0B;QACnC,KAAK;YAAC;YAAK;SAAoC;IACjD;AACF;AAEA,MAAM,mBAAmB,IAAI,yGAAA,CAAA,SAAM,CAAc;IAC/C,WAAW;QACT,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;YAAC;YAAM;SAA0B;IAC7C;IACA,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;YAAC;YAAM;SAA8B;IACjD;IACA,SAAS;QAAC;KAAa;IACvB,YAAY;QACV,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,KAAK;YAAC;YAAG;SAA0B;IACrC;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,KAAK;YAAC;YAAG;SAA4B;IACvC;IACA,YAAY;QACV,MAAM;QACN,UAAU;YAAC;YAAM;SAA6B;QAC9C,KAAK;YAAC;YAAG;SAA0B;QACnC,KAAK;YAAC;YAAK;SAAoC;IACjD;IACA,WAAW;QACT,MAAM;QACN,UAAU;YAAC;YAAM;SAAgC;QACjD,KAAK;YAAC;YAAG;SAA0B;IACrC;IACA,WAAW;QACT,MAAM;QACN,UAAU;YAAC;YAAM;SAAgC;IACnD;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAAmC;IACtD;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuC;IAC1D;IACA,UAAU;QACR,MAAM;QACN,WAAW;YAAC;YAAM;SAA0C;IAC9D;IACA,kBAAkB;QAChB,gBAAgB;YACd,MAAM;YACN,UAAU;gBAAC;gBAAM;aAA0B;YAC3C,KAAK;gBAAC;gBAAG;aAA4B;QACvC;QACA,kBAAkB;YAChB,MAAM;YACN,UAAU;gBAAC;gBAAM;aAAyB;YAC1C,KAAK;gBAAC;gBAAG;aAA2B;QACtC;QACA,gBAAgB;YACd,MAAM;YACN,UAAU;gBAAC;gBAAM;aAA4B;YAC7C,KAAK;gBAAC;gBAAG;aAA8B;QACzC;QACA,gBAAgB;YAAC;SAAqB;IACxC;IACA,eAAe;QACb,MAAM;QACN,MAAM;YAAC;YAAQ;YAAW;YAAU;SAAY;QAChD,SAAS;IACX;IACA,UAAU;QACR,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;IACP;IACA,UAAU;QACR,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,oCAAoC;AACpC,iBAAiB,KAAK,CAAC;IAAE,WAAW;IAAG,QAAQ;AAAE;AACjD,iBAAiB,KAAK,CAAC;IAAE,WAAW;IAAG,aAAa,CAAC;AAAE;AACvD,iBAAiB,KAAK,CAAC;IAAE,QAAQ;IAAG,aAAa,CAAC;AAAE;AACpD,iBAAiB,KAAK,CAAC;IAAE,eAAe;AAAE;AAC1C,iBAAiB,KAAK,CAAC;IAAE,UAAU;AAAE;AAErC,sCAAsC;AACtC,iBAAiB,GAAG,CAAC,QAAQ,SAAS,IAAI;IACxC,iBAAiB;IACjB,IAAI,IAAI,CAAC,QAAQ,GAAG,GAAG;QACrB,IAAI,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC,AAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,GAAI;IACnE;IAEA,uDAAuD;IACvD,IAAI,IAAI,CAAC,UAAU,CAAC,oBAAoB,IAAI,CAAC,aAAa,KAAK,aAAa;QAC1E,IAAI,CAAC,QAAQ,GAAG,IAAI;IACtB;IAEA;AACF;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,UAAU,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAc,cAAc", "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\nimport { UserRole } from '@/types';\n\nexport interface IUser extends Document {\n  email: string;\n  password: string;\n  name: string;\n  role: UserRole;\n  avatar?: string;\n  phone?: string;\n  dateOfBirth?: Date;\n  isEmailVerified: boolean;\n  profile?: {\n    bio?: string;\n    experience?: string;\n    education?: string;\n    skills?: string[];\n  };\n  createdAt: Date;\n  updatedAt: Date;\n  comparePassword(candidatePassword: string): Promise<boolean>;\n}\n\nconst UserSchema = new Schema<IUser>({\n  email: {\n    type: String,\n    required: [true, 'Email là bắt buộc'],\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: [/^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/, 'Email không hợp lệ']\n  },\n  password: {\n    type: String,\n    required: [true, '<PERSON>ật khẩu là bắt buộc'],\n    minlength: [6, 'Mật khẩu phải có ít nhất 6 ký tự']\n  },\n  name: {\n    type: String,\n    required: [true, 'Tên là bắt buộc'],\n    trim: true,\n    maxlength: [100, 'Tên không được vượt quá 100 ký tự']\n  },\n  role: {\n    type: String,\n    enum: Object.values(UserRole),\n    default: UserRole.STUDENT\n  },\n  avatar: {\n    type: String,\n    default: null\n  },\n  phone: {\n    type: String,\n    match: [/^[0-9]{10,11}$/, 'Số điện thoại không hợp lệ']\n  },\n  dateOfBirth: {\n    type: Date\n  },\n  isEmailVerified: {\n    type: Boolean,\n    default: false\n  },\n  profile: {\n    bio: {\n      type: String,\n      maxlength: [500, 'Bio không được vượt quá 500 ký tự']\n    },\n    experience: {\n      type: String,\n      maxlength: [1000, 'Kinh nghiệm không được vượt quá 1000 ký tự']\n    },\n    education: {\n      type: String,\n      maxlength: [500, 'Học vấn không được vượt quá 500 ký tự']\n    },\n    skills: [{\n      type: String,\n      maxlength: [50, 'Kỹ năng không được vượt quá 50 ký tự']\n    }]\n  }\n}, {\n  timestamps: true\n});\n\n// Index cho tìm kiếm\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\n\n// Hash password trước khi lưu\nUserSchema.pre('save', async function(next) {\n  if (!this.isModified('password')) return next();\n  \n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error as Error);\n  }\n});\n\n// Method để so sánh password\nUserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Loại bỏ password khỏi JSON response\nUserSchema.methods.toJSON = function() {\n  const userObject = this.toObject();\n  delete userObject.password;\n  return userObject;\n};\n\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAsBA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAQ;IACnC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;YAAC;YAA+C;SAAqB;IAC9E;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAAmC;IACpD;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAkB;QACnC,MAAM;QACN,WAAW;YAAC;YAAK;SAAoC;IACvD;IACA,MAAM;QACJ,MAAM;QACN,MAAM,OAAO,MAAM,CAAC,uHAAA,CAAA,WAAQ;QAC5B,SAAS,uHAAA,CAAA,WAAQ,CAAC,OAAO;IAC3B;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,OAAO;YAAC;YAAkB;SAA6B;IACzD;IACA,aAAa;QACX,MAAM;IACR;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,SAAS;QACP,KAAK;YACH,MAAM;YACN,WAAW;gBAAC;gBAAK;aAAoC;QACvD;QACA,YAAY;YACV,MAAM;YACN,WAAW;gBAAC;gBAAM;aAA6C;QACjE;QACA,WAAW;YACT,MAAM;YACN,WAAW;gBAAC;gBAAK;aAAwC;QAC3D;QACA,QAAQ;YAAC;gBACP,MAAM;gBACN,WAAW;oBAAC;oBAAI;iBAAuC;YACzD;SAAE;IACJ;AACF,GAAG;IACD,YAAY;AACd;AAEA,qBAAqB;AACrB,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAE3B,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAe,IAAI;IACxC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,6BAA6B;AAC7B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAyB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,sCAAsC;AACtC,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO;AACT;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 942, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport User from '@/models/User';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';\n\nexport interface AuthUser {\n  userId: string;\n  email: string;\n  role: string;\n}\n\nexport async function verifyToken(token: string): Promise<AuthUser | null> {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET) as AuthUser;\n    return decoded;\n  } catch (error) {\n    console.error('Token verification failed:', error);\n    return null;\n  }\n}\n\nexport async function getAuthUser(request: NextRequest): Promise<AuthUser | null> {\n  try {\n    // Try to get token from cookie first\n    let token = request.cookies.get('auth-token')?.value;\n    \n    // If no cookie, try Authorization header\n    if (!token) {\n      const authHeader = request.headers.get('authorization');\n      if (authHeader && authHeader.startsWith('Bearer ')) {\n        token = authHeader.substring(7);\n      }\n    }\n\n    if (!token) {\n      return null;\n    }\n\n    return await verifyToken(token);\n  } catch (error) {\n    console.error('Get auth user failed:', error);\n    return null;\n  }\n}\n\nexport async function requireAuth(request: NextRequest): Promise<{ user: AuthUser } | { error: Response }> {\n  const user = await getAuthUser(request);\n  \n  if (!user) {\n    return {\n      error: new Response(\n        JSON.stringify({\n          success: false,\n          message: 'Unauthorized - Token required'\n        }),\n        { \n          status: 401,\n          headers: { 'Content-Type': 'application/json' }\n        }\n      )\n    };\n  }\n\n  return { user };\n}\n\nexport async function requireRole(request: NextRequest, allowedRoles: string[]): Promise<{ user: AuthUser } | { error: Response }> {\n  const authResult = await requireAuth(request);\n  \n  if ('error' in authResult) {\n    return authResult;\n  }\n\n  if (!allowedRoles.includes(authResult.user.role)) {\n    return {\n      error: new Response(\n        JSON.stringify({\n          success: false,\n          message: 'Forbidden - Insufficient permissions'\n        }),\n        { \n          status: 403,\n          headers: { 'Content-Type': 'application/json' }\n        }\n      )\n    };\n  }\n\n  return authResult;\n}\n\nexport async function getCurrentUser(userId: string) {\n  try {\n    await connectDB();\n    const user = await User.findById(userId).select('-password');\n    return user;\n  } catch (error) {\n    console.error('Get current user failed:', error);\n    return null;\n  }\n}\n\n// Helper function to generate JWT token\nexport function generateToken(payload: { userId: string; email: string; role: string }): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });\n}\n\n// Helper function to hash password (for password reset, etc.)\nexport async function hashPassword(password: string): Promise<string> {\n  const bcrypt = require('bcryptjs');\n  const salt = await bcrypt.genSalt(12);\n  return bcrypt.hash(password, salt);\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;AACA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAQtC,eAAe,YAAY,KAAa;IAC7C,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAEO,eAAe,YAAY,OAAoB;IACpD,IAAI;QACF,qCAAqC;QACrC,IAAI,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAE/C,yCAAyC;QACzC,IAAI,CAAC,OAAO;YACV,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;YACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;gBAClD,QAAQ,WAAW,SAAS,CAAC;YAC/B;QACF;QAEA,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,OAAO,MAAM,YAAY;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF;AAEO,eAAe,YAAY,OAAoB;IACpD,MAAM,OAAO,MAAM,YAAY;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;YACL,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBACb,SAAS;gBACT,SAAS;YACX,IACA;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;IACF;IAEA,OAAO;QAAE;IAAK;AAChB;AAEO,eAAe,YAAY,OAAoB,EAAE,YAAsB;IAC5E,MAAM,aAAa,MAAM,YAAY;IAErC,IAAI,WAAW,YAAY;QACzB,OAAO;IACT;IAEA,IAAI,CAAC,aAAa,QAAQ,CAAC,WAAW,IAAI,CAAC,IAAI,GAAG;QAChD,OAAO;YACL,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBACb,SAAS;gBACT,SAAS;YACX,IACA;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;IACF;IAEA,OAAO;AACT;AAEO,eAAe,eAAe,MAAc;IACjD,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAAQ,MAAM,CAAC;QAChD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAGO,SAAS,cAAc,OAAwD;IACpF,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAGO,eAAe,aAAa,QAAgB;IACjD,MAAM;IACN,MAAM,OAAO,MAAM,OAAO,OAAO,CAAC;IAClC,OAAO,OAAO,IAAI,CAAC,UAAU;AAC/B", "debugId": null}}, {"offset": {"line": 1052, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/app/api/tests/%5Bid%5D/submit/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport Test from '@/models/Test';\nimport TestResult from '@/models/TestResult';\nimport { getAuthUser } from '@/lib/auth';\nimport { QuestionType } from '@/types';\n\ninterface RouteParams {\n  params: {\n    id: string;\n  };\n}\n\nexport async function POST(request: NextRequest, { params }: RouteParams) {\n  try {\n    await connectDB();\n\n    const { id } = params;\n    \n    // Check authentication\n    const user = await getAuthUser(request);\n    if (!user) {\n      return NextResponse.json({\n        success: false,\n        message: 'Unauthorized - Vui lòng đăng nhập'\n      }, { status: 401 });\n    }\n\n    // Find test\n    const test = await Test.findById(id);\n    if (!test) {\n      return NextResponse.json({\n        success: false,\n        message: 'Không tìm thấy bài kiểm tra'\n      }, { status: 404 });\n    }\n\n    if (!test.isPublished) {\n      return NextResponse.json({\n        success: false,\n        message: '<PERSON><PERSON><PERSON> kiểm tra chưa được xuất bản'\n      }, { status: 403 });\n    }\n\n    const body = await request.json();\n    const { answers, timeSpent, startedAt } = body;\n\n    // Validation\n    if (!answers || typeof answers !== 'object') {\n      return NextResponse.json({\n        success: false,\n        message: 'Dữ liệu câu trả lời không hợp lệ'\n      }, { status: 400 });\n    }\n\n    if (!timeSpent || timeSpent < 0) {\n      return NextResponse.json({\n        success: false,\n        message: 'Thời gian làm bài không hợp lệ'\n      }, { status: 400 });\n    }\n\n    // Check if user already submitted this test\n    const existingResult = await TestResult.findOne({\n      studentId: user.userId,\n      testId: id\n    });\n\n    if (existingResult) {\n      return NextResponse.json({\n        success: false,\n        message: 'Bạn đã làm bài kiểm tra này rồi'\n      }, { status: 409 });\n    }\n\n    // Grade the test\n    const gradingResult = await gradeTest(test, answers);\n    \n    // Create test result\n    const testResult = new TestResult({\n      studentId: user.userId,\n      testId: id,\n      answers: gradingResult.answers,\n      totalScore: gradingResult.totalScore,\n      maxScore: gradingResult.maxScore,\n      percentage: gradingResult.percentage,\n      timeSpent,\n      startedAt: new Date(startedAt),\n      completedAt: new Date(),\n      isPassed: gradingResult.percentage >= test.passingScore,\n      detailedAnalysis: gradingResult.detailedAnalysis,\n      gradingStatus: gradingResult.needsManualGrading ? 'pending' : 'completed'\n    });\n\n    await testResult.save();\n\n    // Populate student and test info\n    await testResult.populate([\n      { path: 'studentId', select: 'name email' },\n      { path: 'testId', select: 'title description type level' }\n    ]);\n\n    return NextResponse.json({\n      success: true,\n      message: 'Nộp bài thành công',\n      data: testResult\n    }, { status: 201 });\n\n  } catch (error) {\n    console.error('Submit test error:', error);\n    \n    return NextResponse.json({\n      success: false,\n      message: 'Lỗi khi nộp bài kiểm tra',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n\nasync function gradeTest(test: any, userAnswers: Record<string, any>) {\n  let totalScore = 0;\n  let maxScore = 0;\n  let correctAnswers = 0;\n  let incorrectAnswers = 0;\n  let skippedAnswers = 0;\n  let needsManualGrading = false;\n\n  const answers = [];\n  const skillScores: Record<string, { score: number; maxScore: number }> = {};\n\n  for (const question of test.questions) {\n    const userAnswer = userAnswers[question.id] || null;\n    const isSkipped = !userAnswer;\n    let isCorrect = false;\n    let points = 0;\n    let needsManual = false;\n\n    maxScore += question.points;\n\n    if (isSkipped) {\n      skippedAnswers++;\n    } else {\n      // Grade based on question type\n      switch (question.type) {\n        case QuestionType.MULTIPLE_CHOICE:\n        case QuestionType.TRUE_FALSE:\n        case QuestionType.FILL_IN_BLANK:\n          isCorrect = normalizeAnswer(userAnswer) === normalizeAnswer(question.correctAnswer);\n          if (isCorrect) {\n            points = question.points;\n            correctAnswers++;\n          } else {\n            incorrectAnswers++;\n          }\n          break;\n\n        case QuestionType.ESSAY:\n        case QuestionType.AUDIO_RESPONSE:\n          // These need manual grading\n          needsManual = true;\n          needsManualGrading = true;\n          // For now, give partial credit\n          points = Math.floor(question.points * 0.5);\n          break;\n\n        default:\n          // Unknown question type\n          incorrectAnswers++;\n          break;\n      }\n    }\n\n    totalScore += points;\n\n    // Track skill scores\n    const skill = getQuestionSkill(question.type, test.type);\n    if (!skillScores[skill]) {\n      skillScores[skill] = { score: 0, maxScore: 0 };\n    }\n    skillScores[skill].score += points;\n    skillScores[skill].maxScore += question.points;\n\n    answers.push({\n      questionId: question.id,\n      questionOrder: question.order,\n      userAnswer,\n      correctAnswer: question.correctAnswer,\n      isCorrect,\n      points,\n      maxPoints: question.points,\n      timeSpent: 0, // TODO: Track individual question time\n      isSkipped,\n      needsManualGrading: needsManual\n    });\n  }\n\n  // Calculate skill breakdown\n  const skillBreakdown = Object.entries(skillScores).map(([skill, scores]) => ({\n    skill,\n    score: scores.score,\n    maxScore: scores.maxScore,\n    percentage: scores.maxScore > 0 ? Math.round((scores.score / scores.maxScore) * 100) : 0\n  }));\n\n  const percentage = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0;\n\n  return {\n    answers,\n    totalScore,\n    maxScore,\n    percentage,\n    needsManualGrading,\n    detailedAnalysis: {\n      correctAnswers,\n      incorrectAnswers,\n      skippedAnswers,\n      skillBreakdown\n    }\n  };\n}\n\nfunction normalizeAnswer(answer: string): string {\n  if (typeof answer !== 'string') return '';\n  return answer.toLowerCase().trim().replace(/\\s+/g, ' ');\n}\n\nfunction getQuestionSkill(questionType: QuestionType, testType: string): string {\n  switch (questionType) {\n    case QuestionType.MULTIPLE_CHOICE:\n    case QuestionType.FILL_IN_BLANK:\n      return testType === 'vocabulary' ? 'Từ vựng' : 'Ngữ pháp';\n    case QuestionType.ESSAY:\n      return 'Viết';\n    case QuestionType.AUDIO_RESPONSE:\n      return 'Nói';\n    case QuestionType.TRUE_FALSE:\n      return 'Đọc hiểu';\n    default:\n      return 'Tổng hợp';\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAQO,eAAe,KAAK,OAAoB,EAAE,EAAE,MAAM,EAAe;IACtE,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,EAAE,EAAE,GAAG;QAEf,uBAAuB;QACvB,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,YAAY;QACZ,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,IAAI,CAAC,KAAK,WAAW,EAAE;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG;QAE1C,aAAa;QACb,IAAI,CAAC,WAAW,OAAO,YAAY,UAAU;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,IAAI,CAAC,aAAa,YAAY,GAAG;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,4CAA4C;QAC5C,MAAM,iBAAiB,MAAM,6HAAA,CAAA,UAAU,CAAC,OAAO,CAAC;YAC9C,WAAW,KAAK,MAAM;YACtB,QAAQ;QACV;QAEA,IAAI,gBAAgB;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,iBAAiB;QACjB,MAAM,gBAAgB,MAAM,UAAU,MAAM;QAE5C,qBAAqB;QACrB,MAAM,aAAa,IAAI,6HAAA,CAAA,UAAU,CAAC;YAChC,WAAW,KAAK,MAAM;YACtB,QAAQ;YACR,SAAS,cAAc,OAAO;YAC9B,YAAY,cAAc,UAAU;YACpC,UAAU,cAAc,QAAQ;YAChC,YAAY,cAAc,UAAU;YACpC;YACA,WAAW,IAAI,KAAK;YACpB,aAAa,IAAI;YACjB,UAAU,cAAc,UAAU,IAAI,KAAK,YAAY;YACvD,kBAAkB,cAAc,gBAAgB;YAChD,eAAe,cAAc,kBAAkB,GAAG,YAAY;QAChE;QAEA,MAAM,WAAW,IAAI;QAErB,iCAAiC;QACjC,MAAM,WAAW,QAAQ,CAAC;YACxB;gBAAE,MAAM;gBAAa,QAAQ;YAAa;YAC1C;gBAAE,MAAM;gBAAU,QAAQ;YAA+B;SAC1D;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;QACR,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QAEpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEA,eAAe,UAAU,IAAS,EAAE,WAAgC;IAClE,IAAI,aAAa;IACjB,IAAI,WAAW;IACf,IAAI,iBAAiB;IACrB,IAAI,mBAAmB;IACvB,IAAI,iBAAiB;IACrB,IAAI,qBAAqB;IAEzB,MAAM,UAAU,EAAE;IAClB,MAAM,cAAmE,CAAC;IAE1E,KAAK,MAAM,YAAY,KAAK,SAAS,CAAE;QACrC,MAAM,aAAa,WAAW,CAAC,SAAS,EAAE,CAAC,IAAI;QAC/C,MAAM,YAAY,CAAC;QACnB,IAAI,YAAY;QAChB,IAAI,SAAS;QACb,IAAI,cAAc;QAElB,YAAY,SAAS,MAAM;QAE3B,IAAI,WAAW;YACb;QACF,OAAO;YACL,+BAA+B;YAC/B,OAAQ,SAAS,IAAI;gBACnB,KAAK,uHAAA,CAAA,eAAY,CAAC,eAAe;gBACjC,KAAK,uHAAA,CAAA,eAAY,CAAC,UAAU;gBAC5B,KAAK,uHAAA,CAAA,eAAY,CAAC,aAAa;oBAC7B,YAAY,gBAAgB,gBAAgB,gBAAgB,SAAS,aAAa;oBAClF,IAAI,WAAW;wBACb,SAAS,SAAS,MAAM;wBACxB;oBACF,OAAO;wBACL;oBACF;oBACA;gBAEF,KAAK,uHAAA,CAAA,eAAY,CAAC,KAAK;gBACvB,KAAK,uHAAA,CAAA,eAAY,CAAC,cAAc;oBAC9B,4BAA4B;oBAC5B,cAAc;oBACd,qBAAqB;oBACrB,+BAA+B;oBAC/B,SAAS,KAAK,KAAK,CAAC,SAAS,MAAM,GAAG;oBACtC;gBAEF;oBACE,wBAAwB;oBACxB;oBACA;YACJ;QACF;QAEA,cAAc;QAEd,qBAAqB;QACrB,MAAM,QAAQ,iBAAiB,SAAS,IAAI,EAAE,KAAK,IAAI;QACvD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACvB,WAAW,CAAC,MAAM,GAAG;gBAAE,OAAO;gBAAG,UAAU;YAAE;QAC/C;QACA,WAAW,CAAC,MAAM,CAAC,KAAK,IAAI;QAC5B,WAAW,CAAC,MAAM,CAAC,QAAQ,IAAI,SAAS,MAAM;QAE9C,QAAQ,IAAI,CAAC;YACX,YAAY,SAAS,EAAE;YACvB,eAAe,SAAS,KAAK;YAC7B;YACA,eAAe,SAAS,aAAa;YACrC;YACA;YACA,WAAW,SAAS,MAAM;YAC1B,WAAW;YACX;YACA,oBAAoB;QACtB;IACF;IAEA,4BAA4B;IAC5B,MAAM,iBAAiB,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,OAAO,OAAO,GAAK,CAAC;YAC3E;YACA,OAAO,OAAO,KAAK;YACnB,UAAU,OAAO,QAAQ;YACzB,YAAY,OAAO,QAAQ,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,OAAO,KAAK,GAAG,OAAO,QAAQ,GAAI,OAAO;QACzF,CAAC;IAED,MAAM,aAAa,WAAW,IAAI,KAAK,KAAK,CAAC,AAAC,aAAa,WAAY,OAAO;IAE9E,OAAO;QACL;QACA;QACA;QACA;QACA;QACA,kBAAkB;YAChB;YACA;YACA;YACA;QACF;IACF;AACF;AAEA,SAAS,gBAAgB,MAAc;IACrC,IAAI,OAAO,WAAW,UAAU,OAAO;IACvC,OAAO,OAAO,WAAW,GAAG,IAAI,GAAG,OAAO,CAAC,QAAQ;AACrD;AAEA,SAAS,iBAAiB,YAA0B,EAAE,QAAgB;IACpE,OAAQ;QACN,KAAK,uHAAA,CAAA,eAAY,CAAC,eAAe;QACjC,KAAK,uHAAA,CAAA,eAAY,CAAC,aAAa;YAC7B,OAAO,aAAa,eAAe,YAAY;QACjD,KAAK,uHAAA,CAAA,eAAY,CAAC,KAAK;YACrB,OAAO;QACT,KAAK,uHAAA,CAAA,eAAY,CAAC,cAAc;YAC9B,OAAO;QACT,KAAK,uHAAA,CAAA,eAAY,CAAC,UAAU;YAC1B,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}]}