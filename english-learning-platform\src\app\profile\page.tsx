"use client";

import { useState } from "react";
import { Camera, Edit, Save, X, Mail, Phone, Calendar, MapPin, Award, BookOpen } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import Header from "@/components/layout/Header";
import { toast } from "sonner";

// Mock user data
const mockUser = {
  id: "1",
  name: "<PERSON>uyễn Văn <PERSON>",
  email: "<EMAIL>",
  phone: "0123456789",
  dateOfBirth: "1995-05-15",
  address: "<PERSON><PERSON>, Việt Nam",
  avatar: "",
  role: "student",
  joinDate: "2024-01-15",
  bio: "Tô<PERSON> là một sinh viên đam mê học tiếng Anh. Mục tiêu của tôi là đạt được trình độ IELTS 7.0 trong năm nay.",
  skills: ["Listening", "Reading", "Grammar"],
  achievements: [
    {
      title: "Hoàn thành khóa học Tiếng Anh Cơ Bản",
      date: "2024-01-20",
      type: "course"
    },
    {
      title: "Đạt 85% bài kiểm tra từ vựng",
      date: "2024-01-18",
      type: "test"
    },
    {
      title: "Học viên xuất sắc tháng 1",
      date: "2024-01-31",
      type: "award"
    }
  ],
  stats: {
    coursesCompleted: 2,
    totalHours: 45,
    averageScore: 85,
    certificates: 2
  }
};

export default function ProfilePage() {
  const [user, setUser] = useState(mockUser);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    name: user.name,
    phone: user.phone,
    dateOfBirth: user.dateOfBirth,
    address: user.address,
    bio: user.bio
  });

  const handleEdit = () => {
    setIsEditing(true);
    setEditForm({
      name: user.name,
      phone: user.phone,
      dateOfBirth: user.dateOfBirth,
      address: user.address,
      bio: user.bio
    });
  };

  const handleSave = () => {
    setUser(prev => ({
      ...prev,
      ...editForm
    }));
    setIsEditing(false);
    toast.success("Cập nhật thông tin thành công!");
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditForm({
      name: user.name,
      phone: user.phone,
      dateOfBirth: user.dateOfBirth,
      address: user.address,
      bio: user.bio
    });
  };

  const handleInputChange = (field: string, value: string) => {
    setEditForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Profile Header */}
        <Card className="mb-8">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row items-center md:items-start space-y-4 md:space-y-0 md:space-x-6">
              {/* Avatar */}
              <div className="relative">
                <Avatar className="w-32 h-32">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback className="text-2xl">
                    {user.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <Button
                  size="sm"
                  className="absolute bottom-0 right-0 rounded-full w-8 h-8 p-0"
                  onClick={() => toast.info("Chức năng đổi ảnh đại diện sẽ được cập nhật sớm")}
                >
                  <Camera className="h-4 w-4" />
                </Button>
              </div>

              {/* User Info */}
              <div className="flex-1 text-center md:text-left">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900">{user.name}</h1>
                    <p className="text-lg text-gray-600">{user.email}</p>
                    <Badge variant="secondary" className="mt-2">
                      {user.role === 'student' ? 'Học viên' : user.role === 'teacher' ? 'Giáo viên' : 'Admin'}
                    </Badge>
                  </div>
                  <div className="mt-4 md:mt-0">
                    {!isEditing ? (
                      <Button onClick={handleEdit}>
                        <Edit className="h-4 w-4 mr-2" />
                        Chỉnh sửa
                      </Button>
                    ) : (
                      <div className="flex gap-2">
                        <Button onClick={handleSave}>
                          <Save className="h-4 w-4 mr-2" />
                          Lưu
                        </Button>
                        <Button variant="outline" onClick={handleCancel}>
                          <X className="h-4 w-4 mr-2" />
                          Hủy
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{user.stats.coursesCompleted}</div>
                    <div className="text-sm text-gray-500">Khóa học hoàn thành</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{user.stats.totalHours}h</div>
                    <div className="text-sm text-gray-500">Tổng thời gian học</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{user.stats.averageScore}%</div>
                    <div className="text-sm text-gray-500">Điểm trung bình</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{user.stats.certificates}</div>
                    <div className="text-sm text-gray-500">Chứng chỉ</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Profile Content */}
        <Tabs defaultValue="info" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="info">Thông tin cá nhân</TabsTrigger>
            <TabsTrigger value="achievements">Thành tích</TabsTrigger>
            <TabsTrigger value="settings">Cài đặt</TabsTrigger>
          </TabsList>

          {/* Personal Info Tab */}
          <TabsContent value="info" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Thông tin cá nhân</CardTitle>
                <CardDescription>
                  Quản lý thông tin cá nhân và liên hệ của bạn
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Họ và tên</label>
                    {isEditing ? (
                      <Input
                        value={editForm.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                      />
                    ) : (
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-900">{user.name}</span>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Email</label>
                    <div className="flex items-center space-x-2">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-900">{user.email}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Số điện thoại</label>
                    {isEditing ? (
                      <Input
                        value={editForm.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                      />
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-900">{user.phone}</span>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Ngày sinh</label>
                    {isEditing ? (
                      <Input
                        type="date"
                        value={editForm.dateOfBirth}
                        onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                      />
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-900">
                          {new Date(user.dateOfBirth).toLocaleDateString('vi-VN')}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <label className="text-sm font-medium text-gray-700">Địa chỉ</label>
                    {isEditing ? (
                      <Input
                        value={editForm.address}
                        onChange={(e) => handleInputChange('address', e.target.value)}
                      />
                    ) : (
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-900">{user.address}</span>
                      </div>
                    )}
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Giới thiệu bản thân</label>
                  {isEditing ? (
                    <textarea
                      className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={4}
                      value={editForm.bio}
                      onChange={(e) => handleInputChange('bio', e.target.value)}
                      placeholder="Viết vài dòng giới thiệu về bản thân..."
                    />
                  ) : (
                    <p className="text-gray-900">{user.bio}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Kỹ năng</label>
                  <div className="flex flex-wrap gap-2">
                    {user.skills.map((skill, index) => (
                      <Badge key={index} variant="secondary">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Achievements Tab */}
          <TabsContent value="achievements" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Thành tích và chứng chỉ</CardTitle>
                <CardDescription>
                  Các thành tích và chứng chỉ bạn đã đạt được
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {user.achievements.map((achievement, index) => (
                    <div key={index} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <div className="flex-shrink-0">
                        {achievement.type === 'course' && <BookOpen className="h-8 w-8 text-blue-600" />}
                        {achievement.type === 'test' && <Award className="h-8 w-8 text-green-600" />}
                        {achievement.type === 'award' && <Award className="h-8 w-8 text-purple-600" />}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">{achievement.title}</h3>
                        <p className="text-sm text-gray-500">
                          {new Date(achievement.date).toLocaleDateString('vi-VN')}
                        </p>
                      </div>
                      <Badge variant={
                        achievement.type === 'course' ? 'default' :
                        achievement.type === 'test' ? 'secondary' : 'outline'
                      }>
                        {achievement.type === 'course' ? 'Khóa học' :
                         achievement.type === 'test' ? 'Bài kiểm tra' : 'Giải thưởng'}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Cài đặt tài khoản</CardTitle>
                <CardDescription>
                  Quản lý cài đặt bảo mật và thông báo
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Thông báo email</h3>
                      <p className="text-sm text-gray-500">Nhận thông báo về khóa học và bài kiểm tra</p>
                    </div>
                    <input type="checkbox" className="rounded" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Thông báo push</h3>
                      <p className="text-sm text-gray-500">Nhận thông báo trên trình duyệt</p>
                    </div>
                    <input type="checkbox" className="rounded" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Hiển thị công khai</h3>
                      <p className="text-sm text-gray-500">Cho phép người khác xem hồ sơ của bạn</p>
                    </div>
                    <input type="checkbox" className="rounded" defaultChecked />
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h3 className="font-medium text-red-600">Vùng nguy hiểm</h3>
                  <div className="space-y-2">
                    <Button variant="outline" className="text-red-600 border-red-600 hover:bg-red-50">
                      Đổi mật khẩu
                    </Button>
                    <Button variant="outline" className="text-red-600 border-red-600 hover:bg-red-50">
                      Xóa tài khoản
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
