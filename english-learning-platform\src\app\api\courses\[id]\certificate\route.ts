import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Course from '@/models/Course';
import Enrollment from '@/models/Enrollment';
import User from '@/models/User';
import { getAuthUser } from '@/lib/auth';
import { generateRandomString } from '@/lib/utils';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user || user.role !== 'student') {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Chỉ học viên mới có thể tạo chứng chỉ'
      }, { status: 401 });
    }

    // Find enrollment
    const enrollment = await Enrollment.findOne({
      studentId: user.userId,
      courseId: id
    }).populate([
      { path: 'studentId', select: 'name email' },
      { path: 'courseId', select: 'title description instructor duration' }
    ]);

    if (!enrollment) {
      return NextResponse.json({
        success: false,
        message: '<PERSON>ạn chưa đăng ký khóa học này'
      }, { status: 404 });
    }

    // Check if course is completed
    if (enrollment.progress < 100) {
      return NextResponse.json({
        success: false,
        message: 'Bạn cần hoàn thành 100% khóa học để nhận chứng chỉ'
      }, { status: 403 });
    }

    // Check if certificate already issued
    if (enrollment.certificateIssued && enrollment.certificateId) {
      return NextResponse.json({
        success: true,
        message: 'Chứng chỉ đã được cấp trước đó',
        data: {
          certificateId: enrollment.certificateId,
          issuedAt: enrollment.completedAt,
          downloadUrl: `/api/courses/${id}/certificate/${enrollment.certificateId}/download`
        }
      });
    }

    // Generate certificate ID
    const certificateId = `CERT-${Date.now()}-${generateRandomString(8)}`;

    // Update enrollment with certificate info
    enrollment.certificateIssued = true;
    enrollment.certificateId = certificateId;
    if (!enrollment.completedAt) {
      enrollment.completedAt = new Date();
    }
    await enrollment.save();

    // Get instructor info
    const course = await Course.findById(id).populate('instructor', 'name');

    // Create certificate data
    const certificateData = {
      id: certificateId,
      studentName: (enrollment.studentId as any).name,
      studentEmail: (enrollment.studentId as any).email,
      courseName: (enrollment.courseId as any).title,
      instructorName: course?.instructor?.name || 'Unknown',
      completedAt: enrollment.completedAt,
      issuedAt: new Date(),
      duration: (enrollment.courseId as any).duration,
      totalTimeSpent: Math.floor(enrollment.totalTimeSpent / 3600), // Convert to hours
      verificationUrl: `${process.env.NEXT_PUBLIC_APP_URL}/verify-certificate/${certificateId}`
    };

    // TODO: Generate PDF certificate
    // For now, we'll return the certificate data
    
    return NextResponse.json({
      success: true,
      message: 'Chứng chỉ đã được tạo thành công!',
      data: {
        certificateId,
        issuedAt: certificateData.issuedAt,
        downloadUrl: `/api/courses/${id}/certificate/${certificateId}/download`,
        verificationUrl: certificateData.verificationUrl,
        certificateData
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Generate certificate error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi tạo chứng chỉ',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find enrollment
    const enrollment = await Enrollment.findOne({
      studentId: user.userId,
      courseId: id
    }).populate([
      { path: 'studentId', select: 'name email' },
      { path: 'courseId', select: 'title description instructor duration' }
    ]);

    if (!enrollment) {
      return NextResponse.json({
        success: false,
        message: 'Bạn chưa đăng ký khóa học này'
      }, { status: 404 });
    }

    // Check if certificate exists
    if (!enrollment.certificateIssued || !enrollment.certificateId) {
      return NextResponse.json({
        success: false,
        message: 'Chứng chỉ chưa được cấp'
      }, { status: 404 });
    }

    // Get instructor info
    const course = await Course.findById(id).populate('instructor', 'name');

    // Return certificate info
    const certificateData = {
      id: enrollment.certificateId,
      studentName: (enrollment.studentId as any).name,
      studentEmail: (enrollment.studentId as any).email,
      courseName: (enrollment.courseId as any).title,
      instructorName: course?.instructor?.name || 'Unknown',
      completedAt: enrollment.completedAt,
      issuedAt: enrollment.completedAt, // Assuming issued when completed
      duration: (enrollment.courseId as any).duration,
      totalTimeSpent: Math.floor(enrollment.totalTimeSpent / 3600),
      verificationUrl: `${process.env.NEXT_PUBLIC_APP_URL}/verify-certificate/${enrollment.certificateId}`
    };

    return NextResponse.json({
      success: true,
      data: {
        certificateId: enrollment.certificateId,
        issuedAt: enrollment.completedAt,
        downloadUrl: `/api/courses/${id}/certificate/${enrollment.certificateId}/download`,
        verificationUrl: certificateData.verificationUrl,
        certificateData
      }
    });

  } catch (error) {
    console.error('Get certificate error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi tải thông tin chứng chỉ',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
