{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('vi-VN', {\n    style: 'currency',\n    currency: 'VND'\n  }).format(price);\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('vi-VN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  }).format(dateObj);\n}\n\nexport function formatDuration(minutes: number): string {\n  const hours = Math.floor(minutes / 60);\n  const remainingMinutes = minutes % 60;\n\n  if (hours === 0) {\n    return `${remainingMinutes} phút`;\n  } else if (remainingMinutes === 0) {\n    return `${hours} giờ`;\n  } else {\n    return `${hours} giờ ${remainingMinutes} phút`;\n  }\n}\n\nexport function calculateProgress(completedLessons: number[], totalLessons: number): number {\n  if (totalLessons === 0) return 0;\n  return Math.round((completedLessons.length / totalLessons) * 100);\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2);\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '') // Remove diacritics\n    .replace(/[^a-z0-9 -]/g, '') // Remove special characters\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen\n    .trim();\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n\n  if (password.length < 6) {\n    errors.push('Mật khẩu phải có ít nhất 6 ký tự');\n  }\n\n  if (!/[A-Z]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 chữ hoa');\n  }\n\n  if (!/[a-z]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 chữ thường');\n  }\n\n  if (!/[0-9]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 số');\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\nexport function generateRandomString(length: number): string {\n  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += characters.charAt(Math.floor(Math.random() * characters.length));\n  }\n  return result;\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,mBAAmB,UAAU;IAEnC,IAAI,UAAU,GAAG;QACf,OAAO,GAAG,iBAAiB,KAAK,CAAC;IACnC,OAAO,IAAI,qBAAqB,GAAG;QACjC,OAAO,GAAG,MAAM,IAAI,CAAC;IACvB,OAAO;QACL,OAAO,GAAG,MAAM,KAAK,EAAE,iBAAiB,KAAK,CAAC;IAChD;AACF;AAEO,SAAS,kBAAkB,gBAA0B,EAAE,YAAoB;IAChF,IAAI,iBAAiB,GAAG,OAAO;IAC/B,OAAO,KAAK,KAAK,CAAC,AAAC,iBAAiB,MAAM,GAAG,eAAgB;AAC/D;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAAI,oBAAoB;KACpD,OAAO,CAAC,gBAAgB,IAAI,4BAA4B;KACxD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,OAAO,KAAK,8CAA8C;KAClE,IAAI;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,qBAAqB,MAAc;IACjD,MAAM,aAAa;IACnB,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,WAAW,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM;IAC1E;IACA,OAAO;AACT;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/app/courses/%5Bid%5D/page.tsx"], "sourcesContent": ["import { notFound } from \"next/navigation\";\nimport { Star, Clock, Users, BookOpen, Play, CheckCircle } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport Header from \"@/components/layout/Header\";\nimport { formatPrice } from \"@/lib/utils\";\n\n// Mock data for course detail\nconst mockCourse = {\n  id: \"1\",\n  title: \"Tiếng Anh Cơ Bản cho Người Mới <PERSON>\",\n  description: `<PERSON>h<PERSON>a học này được thiết kế dành riêng cho những người chưa có kiến thức về tiếng Anh hoặc có kiến thức rất cơ bản. \n  \n  Bạn sẽ học được:\n  • Bảng chữ cái và cách phát âm chuẩn\n  • Từ vựng cơ bản trong cuộc sống hàng ngày\n  • <PERSON><PERSON> pháp cơ bản và cách sử dụng\n  • <PERSON><PERSON> năng giao tiếp đơn giản\n  • <PERSON><PERSON><PERSON> đọc và viết những câu đơn giản\n  \n  Phương pháp giảng dạy:\n  • Học qua hình ảnh và âm thanh sinh động\n  • Thực hành qua các tình huống thực tế\n  • Bài tập tương tác và trò chơi học tập\n  • Hỗ trợ 1-1 từ giáo viên`,\n  shortDescription: \"Học tiếng Anh từ con số 0 với phương pháp dễ hiểu và thực tế.\",\n  category: \"comprehensive\",\n  level: \"beginner\",\n  price: 299000,\n  originalPrice: 399000,\n  duration: 40,\n  thumbnail: \"/api/placeholder/800/400\",\n  videoIntro: \"/api/placeholder/video\",\n  teacher: {\n    name: \"Cô Minh Anh\",\n    avatar: \"/api/placeholder/80/80\",\n    bio: \"Giáo viên tiếng Anh với 8 năm kinh nghiệm, tốt nghiệp Đại học Ngoại ngữ Hà Nội. Chuyên gia trong việc giảng dạy tiếng Anh cho người mới bắt đầu.\",\n    experience: \"8 năm kinh nghiệm\",\n    students: 5000\n  },\n  averageRating: 4.8,\n  totalStudents: 1250,\n  totalLessons: 25,\n  curriculum: [\n    {\n      title: \"Giới thiệu và Bảng chữ cái\",\n      lessons: [\n        { title: \"Chào hỏi cơ bản\", duration: 15, isPreview: true },\n        { title: \"Bảng chữ cái A-M\", duration: 20, isPreview: false },\n        { title: \"Bảng chữ cái N-Z\", duration: 20, isPreview: false },\n        { title: \"Phát âm cơ bản\", duration: 25, isPreview: false }\n      ]\n    },\n    {\n      title: \"Từ vựng cơ bản\",\n      lessons: [\n        { title: \"Số đếm 1-100\", duration: 18, isPreview: false },\n        { title: \"Màu sắc và hình dạng\", duration: 22, isPreview: false },\n        { title: \"Gia đình và người thân\", duration: 25, isPreview: false },\n        { title: \"Đồ vật trong nhà\", duration: 20, isPreview: false }\n      ]\n    },\n    {\n      title: \"Ngữ pháp cơ bản\",\n      lessons: [\n        { title: \"Động từ TO BE\", duration: 30, isPreview: false },\n        { title: \"Câu khẳng định và phủ định\", duration: 25, isPreview: false },\n        { title: \"Câu hỏi Yes/No\", duration: 28, isPreview: false },\n        { title: \"Câu hỏi WH-\", duration: 32, isPreview: false }\n      ]\n    }\n  ],\n  ratings: [\n    {\n      user: \"Nguyễn Văn A\",\n      rating: 5,\n      comment: \"Khóa học rất hay, giáo viên giảng dễ hiểu. Tôi đã học được rất nhiều kiến thức bổ ích.\",\n      date: \"2024-01-15\"\n    },\n    {\n      user: \"Trần Thị B\",\n      rating: 5,\n      comment: \"Phương pháp giảng dạy rất thú vị, không hề nhàm chán. Recommend cho mọi người!\",\n      date: \"2024-01-10\"\n    },\n    {\n      user: \"Lê Văn C\",\n      rating: 4,\n      comment: \"Khóa học tốt, nội dung phong phú. Chỉ mong có thêm nhiều bài tập thực hành hơn.\",\n      date: \"2024-01-05\"\n    }\n  ]\n};\n\ninterface CourseDetailPageProps {\n  params: {\n    id: string;\n  };\n}\n\nexport default function CourseDetailPage({ params }: CourseDetailPageProps) {\n  // In a real app, you would fetch the course data based on the ID\n  if (params.id !== \"1\") {\n    notFound();\n  }\n\n  const course = mockCourse;\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2\">\n            {/* Course Header */}\n            <div className=\"bg-white rounded-lg shadow-sm p-6 mb-6\">\n              <div className=\"flex items-center gap-2 mb-4\">\n                <span className=\"text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded\">\n                  {course.level === 'beginner' ? 'Cơ bản' : \n                   course.level === 'intermediate' ? 'Trung cấp' : 'Nâng cao'}\n                </span>\n                <span className=\"text-xs font-medium text-green-600 bg-green-50 px-2 py-1 rounded\">\n                  {course.category === 'comprehensive' ? 'Tổng hợp' : \n                   course.category === 'listening' ? 'Nghe' :\n                   course.category === 'speaking' ? 'Nói' :\n                   course.category === 'reading' ? 'Đọc' : 'Viết'}\n                </span>\n              </div>\n              \n              <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">{course.title}</h1>\n              <p className=\"text-lg text-gray-600 mb-6\">{course.shortDescription}</p>\n              \n              <div className=\"flex items-center gap-6 text-sm text-gray-500\">\n                <div className=\"flex items-center\">\n                  <Star className=\"h-4 w-4 text-yellow-400 fill-current mr-1\" />\n                  <span className=\"font-medium text-gray-900\">{course.averageRating}</span>\n                  <span className=\"ml-1\">({course.totalStudents} đánh giá)</span>\n                </div>\n                <div className=\"flex items-center\">\n                  <Users className=\"h-4 w-4 mr-1\" />\n                  {course.totalStudents} học viên\n                </div>\n                <div className=\"flex items-center\">\n                  <Clock className=\"h-4 w-4 mr-1\" />\n                  {course.duration} giờ\n                </div>\n                <div className=\"flex items-center\">\n                  <BookOpen className=\"h-4 w-4 mr-1\" />\n                  {course.totalLessons} bài học\n                </div>\n              </div>\n            </div>\n\n            {/* Course Video */}\n            <div className=\"bg-white rounded-lg shadow-sm p-6 mb-6\">\n              <div className=\"aspect-video bg-gray-900 rounded-lg flex items-center justify-center\">\n                <Button size=\"lg\" className=\"bg-white/20 hover:bg-white/30\">\n                  <Play className=\"h-8 w-8 mr-2\" />\n                  Xem video giới thiệu\n                </Button>\n              </div>\n            </div>\n\n            {/* Course Description */}\n            <div className=\"bg-white rounded-lg shadow-sm p-6 mb-6\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Mô tả khóa học</h2>\n              <div className=\"prose max-w-none\">\n                <p className=\"whitespace-pre-line text-gray-700\">{course.description}</p>\n              </div>\n            </div>\n\n            {/* Curriculum */}\n            <div className=\"bg-white rounded-lg shadow-sm p-6 mb-6\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Nội dung khóa học</h2>\n              <div className=\"space-y-4\">\n                {course.curriculum.map((section, sectionIndex) => (\n                  <div key={sectionIndex} className=\"border border-gray-200 rounded-lg\">\n                    <div className=\"p-4 bg-gray-50 border-b border-gray-200\">\n                      <h3 className=\"font-semibold text-gray-900\">{section.title}</h3>\n                      <p className=\"text-sm text-gray-500\">{section.lessons.length} bài học</p>\n                    </div>\n                    <div className=\"divide-y divide-gray-200\">\n                      {section.lessons.map((lesson, lessonIndex) => (\n                        <div key={lessonIndex} className=\"p-4 flex items-center justify-between\">\n                          <div className=\"flex items-center\">\n                            <Play className=\"h-4 w-4 text-gray-400 mr-3\" />\n                            <div>\n                              <p className=\"font-medium text-gray-900\">{lesson.title}</p>\n                              <p className=\"text-sm text-gray-500\">{lesson.duration} phút</p>\n                            </div>\n                          </div>\n                          {lesson.isPreview && (\n                            <Button variant=\"outline\" size=\"sm\">\n                              Xem trước\n                            </Button>\n                          )}\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Instructor */}\n            <div className=\"bg-white rounded-lg shadow-sm p-6 mb-6\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Giáo viên</h2>\n              <div className=\"flex items-start gap-4\">\n                <div className=\"w-20 h-20 bg-gray-300 rounded-full flex-shrink-0\"></div>\n                <div>\n                  <h3 className=\"text-xl font-semibold text-gray-900\">{course.teacher.name}</h3>\n                  <p className=\"text-gray-600 mb-2\">{course.teacher.experience} • {course.teacher.students} học viên</p>\n                  <p className=\"text-gray-700\">{course.teacher.bio}</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Reviews */}\n            <div className=\"bg-white rounded-lg shadow-sm p-6\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Đánh giá từ học viên</h2>\n              <div className=\"space-y-4\">\n                {course.ratings.map((rating, index) => (\n                  <div key={index} className=\"border-b border-gray-200 pb-4 last:border-b-0\">\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      <div className=\"flex\">\n                        {[...Array(5)].map((_, i) => (\n                          <Star\n                            key={i}\n                            className={`h-4 w-4 ${\n                              i < rating.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'\n                            }`}\n                          />\n                        ))}\n                      </div>\n                      <span className=\"font-medium text-gray-900\">{rating.user}</span>\n                      <span className=\"text-sm text-gray-500\">{rating.date}</span>\n                    </div>\n                    <p className=\"text-gray-700\">{rating.comment}</p>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"sticky top-8\">\n              <Card>\n                <CardHeader>\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <div className=\"text-3xl font-bold text-blue-600\">\n                        {formatPrice(course.price)}\n                      </div>\n                      {course.originalPrice && (\n                        <div className=\"text-lg text-gray-500 line-through\">\n                          {formatPrice(course.originalPrice)}\n                        </div>\n                      )}\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-sm text-green-600 font-medium\">\n                        Giảm {Math.round(((course.originalPrice! - course.price) / course.originalPrice!) * 100)}%\n                      </div>\n                    </div>\n                  </div>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <Button className=\"w-full\" size=\"lg\">\n                    Đăng ký ngay\n                  </Button>\n                  <Button variant=\"outline\" className=\"w-full\">\n                    Thêm vào giỏ hàng\n                  </Button>\n                  \n                  <div className=\"text-center text-sm text-gray-500\">\n                    Đảm bảo hoàn tiền trong 30 ngày\n                  </div>\n                  \n                  <div className=\"space-y-3 pt-4 border-t border-gray-200\">\n                    <h3 className=\"font-semibold text-gray-900\">Khóa học bao gồm:</h3>\n                    <div className=\"space-y-2 text-sm text-gray-600\">\n                      <div className=\"flex items-center\">\n                        <CheckCircle className=\"h-4 w-4 text-green-500 mr-2\" />\n                        {course.duration} giờ video học\n                      </div>\n                      <div className=\"flex items-center\">\n                        <CheckCircle className=\"h-4 w-4 text-green-500 mr-2\" />\n                        {course.totalLessons} bài học\n                      </div>\n                      <div className=\"flex items-center\">\n                        <CheckCircle className=\"h-4 w-4 text-green-500 mr-2\" />\n                        Tài liệu học tập\n                      </div>\n                      <div className=\"flex items-center\">\n                        <CheckCircle className=\"h-4 w-4 text-green-500 mr-2\" />\n                        Chứng chỉ hoàn thành\n                      </div>\n                      <div className=\"flex items-center\">\n                        <CheckCircle className=\"h-4 w-4 text-green-500 mr-2\" />\n                        Truy cập trọn đời\n                      </div>\n                      <div className=\"flex items-center\">\n                        <CheckCircle className=\"h-4 w-4 text-green-500 mr-2\" />\n                        Hỗ trợ từ giáo viên\n                      </div>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,8BAA8B;AAC9B,MAAM,aAAa;IACjB,IAAI;IACJ,OAAO;IACP,aAAa,CAAC;;;;;;;;;;;;;2BAaW,CAAC;IAC1B,kBAAkB;IAClB,UAAU;IACV,OAAO;IACP,OAAO;IACP,eAAe;IACf,UAAU;IACV,WAAW;IACX,YAAY;IACZ,SAAS;QACP,MAAM;QACN,QAAQ;QACR,KAAK;QACL,YAAY;QACZ,UAAU;IACZ;IACA,eAAe;IACf,eAAe;IACf,cAAc;IACd,YAAY;QACV;YACE,OAAO;YACP,SAAS;gBACP;oBAAE,OAAO;oBAAmB,UAAU;oBAAI,WAAW;gBAAK;gBAC1D;oBAAE,OAAO;oBAAoB,UAAU;oBAAI,WAAW;gBAAM;gBAC5D;oBAAE,OAAO;oBAAoB,UAAU;oBAAI,WAAW;gBAAM;gBAC5D;oBAAE,OAAO;oBAAkB,UAAU;oBAAI,WAAW;gBAAM;aAC3D;QACH;QACA;YACE,OAAO;YACP,SAAS;gBACP;oBAAE,OAAO;oBAAgB,UAAU;oBAAI,WAAW;gBAAM;gBACxD;oBAAE,OAAO;oBAAwB,UAAU;oBAAI,WAAW;gBAAM;gBAChE;oBAAE,OAAO;oBAA0B,UAAU;oBAAI,WAAW;gBAAM;gBAClE;oBAAE,OAAO;oBAAoB,UAAU;oBAAI,WAAW;gBAAM;aAC7D;QACH;QACA;YACE,OAAO;YACP,SAAS;gBACP;oBAAE,OAAO;oBAAiB,UAAU;oBAAI,WAAW;gBAAM;gBACzD;oBAAE,OAAO;oBAA8B,UAAU;oBAAI,WAAW;gBAAM;gBACtE;oBAAE,OAAO;oBAAkB,UAAU;oBAAI,WAAW;gBAAM;gBAC1D;oBAAE,OAAO;oBAAe,UAAU;oBAAI,WAAW;gBAAM;aACxD;QACH;KACD;IACD,SAAS;QACP;YACE,MAAM;YACN,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QACA;YACE,MAAM;YACN,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QACA;YACE,MAAM;YACN,QAAQ;YACR,SAAS;YACT,MAAM;QACR;KACD;AACH;AAQe,SAAS,iBAAiB,EAAE,MAAM,EAAyB;IACxE,iEAAiE;IACjE,IAAI,OAAO,EAAE,KAAK,KAAK;QACrB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,SAAS;IAEf,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,OAAO,KAAK,KAAK,aAAa,WAC9B,OAAO,KAAK,KAAK,iBAAiB,cAAc;;;;;;8DAEnD,8OAAC;oDAAK,WAAU;8DACb,OAAO,QAAQ,KAAK,kBAAkB,aACtC,OAAO,QAAQ,KAAK,cAAc,SAClC,OAAO,QAAQ,KAAK,aAAa,QACjC,OAAO,QAAQ,KAAK,YAAY,QAAQ;;;;;;;;;;;;sDAI7C,8OAAC;4CAAG,WAAU;sDAAyC,OAAO,KAAK;;;;;;sDACnE,8OAAC;4CAAE,WAAU;sDAA8B,OAAO,gBAAgB;;;;;;sDAElE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAA6B,OAAO,aAAa;;;;;;sEACjE,8OAAC;4DAAK,WAAU;;gEAAO;gEAAE,OAAO,aAAa;gEAAC;;;;;;;;;;;;;8DAEhD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,OAAO,aAAa;wDAAC;;;;;;;8DAExB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,OAAO,QAAQ;wDAAC;;;;;;;8DAEnB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,OAAO,YAAY;wDAAC;;;;;;;;;;;;;;;;;;;8CAM3B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;;8DAC1B,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;8CAOvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAqC,OAAO,WAAW;;;;;;;;;;;;;;;;;8CAKxE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDACZ,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC/B,8OAAC;oDAAuB,WAAU;;sEAChC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA+B,QAAQ,KAAK;;;;;;8EAC1D,8OAAC;oEAAE,WAAU;;wEAAyB,QAAQ,OAAO,CAAC,MAAM;wEAAC;;;;;;;;;;;;;sEAE/D,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BAC5B,8OAAC;oEAAsB,WAAU;;sFAC/B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,8OAAC;;sGACC,8OAAC;4FAAE,WAAU;sGAA6B,OAAO,KAAK;;;;;;sGACtD,8OAAC;4FAAE,WAAU;;gGAAyB,OAAO,QAAQ;gGAAC;;;;;;;;;;;;;;;;;;;wEAGzD,OAAO,SAAS,kBACf,8OAAC,kIAAA,CAAA,SAAM;4EAAC,SAAQ;4EAAU,MAAK;sFAAK;;;;;;;mEAT9B;;;;;;;;;;;mDAPN;;;;;;;;;;;;;;;;8CA6BhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAuC,OAAO,OAAO,CAAC,IAAI;;;;;;sEACxE,8OAAC;4DAAE,WAAU;;gEAAsB,OAAO,OAAO,CAAC,UAAU;gEAAC;gEAAI,OAAO,OAAO,CAAC,QAAQ;gEAAC;;;;;;;sEACzF,8OAAC;4DAAE,WAAU;sEAAiB,OAAO,OAAO,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;8CAMtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ;2EAAI,MAAM;qEAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;4EAEH,WAAW,CAAC,QAAQ,EAClB,IAAI,OAAO,MAAM,GAAG,iCAAiC,iBACrD;2EAHG;;;;;;;;;;8EAOX,8OAAC;oEAAK,WAAU;8EAA6B,OAAO,IAAI;;;;;;8EACxD,8OAAC;oEAAK,WAAU;8EAAyB,OAAO,IAAI;;;;;;;;;;;;sEAEtD,8OAAC;4DAAE,WAAU;sEAAiB,OAAO,OAAO;;;;;;;mDAfpC;;;;;;;;;;;;;;;;;;;;;;sCAuBlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EACZ,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,OAAO,KAAK;;;;;;4DAE1B,OAAO,aAAa,kBACnB,8OAAC;gEAAI,WAAU;0EACZ,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,OAAO,aAAa;;;;;;;;;;;;kEAIvC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;gEAAqC;gEAC5C,KAAK,KAAK,CAAC,AAAC,CAAC,OAAO,aAAa,GAAI,OAAO,KAAK,IAAI,OAAO,aAAa,GAAK;gEAAK;;;;;;;;;;;;;;;;;;;;;;;sDAKjG,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;oDAAS,MAAK;8DAAK;;;;;;8DAGrC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,WAAU;8DAAS;;;;;;8DAI7C,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;8DAInD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA8B;;;;;;sEAC5C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;wEACtB,OAAO,QAAQ;wEAAC;;;;;;;8EAEnB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;wEACtB,OAAO,YAAY;wEAAC;;;;;;;8EAEvB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;wEAAgC;;;;;;;8EAGzD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;wEAAgC;;;;;;;8EAGzD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;wEAAgC;;;;;;;8EAGzD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;wEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa/E", "debugId": null}}]}