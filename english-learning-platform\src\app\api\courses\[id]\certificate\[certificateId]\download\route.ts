import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Course from '@/models/Course';
import Enrollment from '@/models/Enrollment';
import { getAuthUser } from '@/lib/auth';

interface RouteParams {
  params: {
    id: string;
    certificateId: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id: courseId, certificateId } = await params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find enrollment with certificate
    const enrollment = await Enrollment.findOne({
      studentId: user.userId,
      courseId: courseId,
      certificateId: certificateId,
      certificateIssued: true
    }).populate([
      { path: 'studentId', select: 'name email' },
      { path: 'courseId', select: 'title description duration' }
    ]);

    if (!enrollment) {
      return NextResponse.json({
        success: false,
        message: '<PERSON>hông tìm thấy chứng chỉ hoặc bạn không có quyền truy cập'
      }, { status: 404 });
    }

    // Get course and instructor info
    const course = await Course.findById(courseId).populate('teacherId', 'name');

    // Generate certificate data
    const certificateData = {
      id: certificateId,
      studentName: (enrollment.studentId as any).name,
      studentEmail: (enrollment.studentId as any).email,
      courseName: (enrollment.courseId as any).title,
      instructorName: course?.teacherId?.name || 'Giảng viên',
      completedAt: enrollment.completedAt,
      issuedAt: enrollment.completedAt,
      duration: (enrollment.courseId as any).duration,
      totalTimeSpent: Math.floor(enrollment.totalTimeSpent / 3600),
      verificationUrl: `${process.env.NEXT_PUBLIC_APP_URL}/verify-certificate/${certificateId}`,
      progress: enrollment.progress,
      enrolledAt: enrollment.enrolledAt
    };

    // Generate PDF certificate
    const pdfBuffer = await generateCertificatePDF(certificateData);

    // Return PDF as download
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="chung-chi-${certificateData.courseName.replace(/[^a-zA-Z0-9]/g, '-')}-${certificateId}.pdf"`,
        'Content-Length': pdfBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('Download certificate error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi tải chứng chỉ',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

async function generateCertificatePDF(data: any): Promise<Buffer> {
  // For now, we'll create a simple HTML-based certificate
  // In a production environment, you might want to use libraries like puppeteer or jsPDF
  
  const certificateHTML = `
    <!DOCTYPE html>
    <html lang="vi">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Chứng chỉ hoàn thành khóa học</title>
      <style>
        @page {
          size: A4 landscape;
          margin: 0;
        }
        
        body {
          font-family: 'Times New Roman', serif;
          margin: 0;
          padding: 40px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: #333;
          height: 100vh;
          box-sizing: border-box;
        }
        
        .certificate {
          background: white;
          border: 8px solid #2c5aa0;
          border-radius: 20px;
          padding: 60px;
          text-align: center;
          height: calc(100% - 120px);
          position: relative;
          box-shadow: 0 0 30px rgba(0,0,0,0.3);
        }
        
        .certificate::before {
          content: '';
          position: absolute;
          top: 20px;
          left: 20px;
          right: 20px;
          bottom: 20px;
          border: 2px solid #2c5aa0;
          border-radius: 10px;
        }
        
        .header {
          margin-bottom: 40px;
        }
        
        .logo {
          font-size: 32px;
          font-weight: bold;
          color: #2c5aa0;
          margin-bottom: 10px;
        }
        
        .title {
          font-size: 48px;
          font-weight: bold;
          color: #2c5aa0;
          margin: 30px 0;
          text-transform: uppercase;
          letter-spacing: 3px;
        }
        
        .subtitle {
          font-size: 24px;
          color: #666;
          margin-bottom: 40px;
        }
        
        .student-name {
          font-size: 36px;
          font-weight: bold;
          color: #2c5aa0;
          margin: 30px 0;
          text-decoration: underline;
        }
        
        .course-info {
          font-size: 20px;
          margin: 20px 0;
          line-height: 1.6;
        }
        
        .course-name {
          font-size: 28px;
          font-weight: bold;
          color: #2c5aa0;
          margin: 20px 0;
        }
        
        .details {
          display: flex;
          justify-content: space-between;
          margin-top: 50px;
          font-size: 16px;
        }
        
        .signature-section {
          text-align: center;
        }
        
        .signature-line {
          border-top: 2px solid #333;
          width: 200px;
          margin: 40px auto 10px;
        }
        
        .verification {
          position: absolute;
          bottom: 20px;
          right: 30px;
          font-size: 12px;
          color: #666;
        }
        
        .qr-placeholder {
          width: 80px;
          height: 80px;
          border: 2px solid #666;
          margin: 10px auto;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
        }
      </style>
    </head>
    <body>
      <div class="certificate">
        <div class="header">
          <div class="logo">🎓 ENGLISH LEARNING PLATFORM</div>
          <div class="title">Chứng Chỉ Hoàn Thành</div>
          <div class="subtitle">Certificate of Completion</div>
        </div>
        
        <div class="course-info">
          <p>Chứng nhận rằng</p>
          <p><em>This is to certify that</em></p>
        </div>
        
        <div class="student-name">${data.studentName}</div>
        
        <div class="course-info">
          <p>đã hoàn thành xuất sắc khóa học</p>
          <p><em>has successfully completed the course</em></p>
        </div>
        
        <div class="course-name">"${data.courseName}"</div>
        
        <div class="course-info">
          <p>Thời lượng: ${data.duration} giờ | Thời gian học: ${data.totalTimeSpent} giờ</p>
          <p>Ngày hoàn thành: ${new Date(data.completedAt).toLocaleDateString('vi-VN')}</p>
          <p>Tiến độ hoàn thành: ${data.progress}%</p>
        </div>
        
        <div class="details">
          <div class="signature-section">
            <div class="signature-line"></div>
            <p><strong>${data.instructorName}</strong></p>
            <p>Giảng viên</p>
            <p><em>Instructor</em></p>
          </div>
          
          <div class="signature-section">
            <div class="signature-line"></div>
            <p><strong>Ban Giám Đốc</strong></p>
            <p>English Learning Platform</p>
            <p><em>Management Board</em></p>
          </div>
        </div>
        
        <div class="verification">
          <div class="qr-placeholder">QR Code</div>
          <p>Mã chứng chỉ: ${data.id}</p>
          <p>Xác thực tại: ${data.verificationUrl}</p>
        </div>
      </div>
    </body>
    </html>
  `;

  // For a simple implementation, we'll return the HTML as a "PDF"
  // In production, you would use puppeteer or similar to generate actual PDF
  try {
    // If puppeteer is available, use it to generate PDF
    const puppeteer = require('puppeteer');
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    const page = await browser.newPage();
    await page.setContent(certificateHTML, { waitUntil: 'networkidle0' });
    const pdfBuffer = await page.pdf({
      format: 'A4',
      landscape: true,
      printBackground: true,
      margin: { top: 0, right: 0, bottom: 0, left: 0 }
    });
    await browser.close();
    return pdfBuffer;
  } catch (error) {
    console.warn('Puppeteer not available, falling back to HTML response');
    // Fallback: return HTML as buffer (browser will handle as download)
    return Buffer.from(certificateHTML, 'utf-8');
  }
}
