{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/node_modules_mongoose_dist_browser_umd_52326c2f.js", "server/edge/chunks/node_modules_5bd72c6c._.js", "server/edge/chunks/[root-of-the-server]__e129f18a._.js", "server/edge/chunks/edge-wrapper_1c72be67.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "1Y3419KFkbSo1vQH8UX3M6Y6HligbPVfKUVdVR+sSdo=", "__NEXT_PREVIEW_MODE_ID": "9cb3dd1e4497fbf0d8873d9fbf9dab8f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3ba444dd656827d5c05cd9adccaf12828bd42e958f9419858e5ecea4f6d6895a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4c9f7908875a6f41d4233ca77e8a52e88504050930102804847e58e45eeab4c1"}}}, "instrumentation": null, "functions": {}}