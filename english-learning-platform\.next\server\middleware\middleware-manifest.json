{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/node_modules_mongoose_dist_browser_umd_52326c2f.js", "server/edge/chunks/node_modules_5bd72c6c._.js", "server/edge/chunks/[root-of-the-server]__e129f18a._.js", "server/edge/chunks/edge-wrapper_1c72be67.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "1Y3419KFkbSo1vQH8UX3M6Y6HligbPVfKUVdVR+sSdo=", "__NEXT_PREVIEW_MODE_ID": "7cbf6ba644ffcee15ac531debfbeb64a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e66235655094df01b5ea22678c74751e0cc76320102584d43ff8b698b2423bf8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6af96fb6bea2b5a23278eec2a458884042ff8a5003c59d35ff13349247a27ba6"}}}, "instrumentation": null, "functions": {}}