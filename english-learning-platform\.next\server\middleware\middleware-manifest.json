{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/node_modules_mongoose_dist_browser_umd_52326c2f.js", "server/edge/chunks/node_modules_5bd72c6c._.js", "server/edge/chunks/[root-of-the-server]__e129f18a._.js", "server/edge/chunks/edge-wrapper_1c72be67.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "1Y3419KFkbSo1vQH8UX3M6Y6HligbPVfKUVdVR+sSdo=", "__NEXT_PREVIEW_MODE_ID": "a89d1525034f2f3667729db01fe72cbe", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5ba80a0bc0f13537438da14e265fce701ce7edaf4287d0f594a726898293e03d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1f2fbe58d70f7e85c9f97605433d2612ee287bdb7d48af26adfad486876e49e5"}}}, "instrumentation": null, "functions": {}}