import mongoose, { Document, Schema } from 'mongoose';
import { CourseCategory, CourseLevel } from '@/types';

export interface ICourse extends Document {
  title: string;
  description: string;
  shortDescription: string;
  teacherId: mongoose.Types.ObjectId;
  category: CourseCategory;
  level: CourseLevel;
  price: number;
  duration: number;
  thumbnail?: string;
  videoIntro?: string;
  isPublished: boolean;
  curriculum: {
    _id?: mongoose.Types.ObjectId;
    title: string;
    description: string;
    videoUrl?: string;
    materials: string[];
    duration: number;
    order: number;
    isPreview: boolean;
    transcript?: string;
    objectives?: string[];
    quiz?: {
      questions: Array<{
        question: string;
        options: string[];
        correctAnswer: string;
        explanation?: string;
      }>;
    };
  }[];
  ratings: {
    userId: mongoose.Types.ObjectId;
    rating: number;
    comment: string;
    createdAt: Date;
  }[];
  averageRating: number;
  totalStudents: number;
  tags?: string[];
  requirements?: string[];
  whatYouWillLearn?: string[];
  language: string;
  subtitles?: string[];
  createdAt: Date;
  updatedAt: Date;
}

const QuizQuestionSchema = new Schema({
  question: {
    type: String,
    required: [true, 'Câu hỏi là bắt buộc'],
    maxlength: [500, 'Câu hỏi không được vượt quá 500 ký tự']
  },
  options: [{
    type: String,
    required: [true, 'Lựa chọn là bắt buộc'],
    maxlength: [200, 'Lựa chọn không được vượt quá 200 ký tự']
  }],
  correctAnswer: {
    type: String,
    required: [true, 'Đáp án đúng là bắt buộc']
  },
  explanation: {
    type: String,
    maxlength: [300, 'Giải thích không được vượt quá 300 ký tự']
  }
});

const LessonSchema = new Schema({
  title: {
    type: String,
    required: [true, 'Tiêu đề bài học là bắt buộc'],
    trim: true,
    maxlength: [200, 'Tiêu đề bài học không được vượt quá 200 ký tự']
  },
  description: {
    type: String,
    required: [true, 'Mô tả bài học là bắt buộc'],
    maxlength: [1000, 'Mô tả bài học không được vượt quá 1000 ký tự']
  },
  videoUrl: {
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/.+/.test(v);
      },
      message: 'URL video không hợp lệ'
    }
  },
  materials: [{
    type: String,
    validate: {
      validator: function(v: string) {
        return /^https?:\/\/.+/.test(v);
      },
      message: 'URL tài liệu không hợp lệ'
    }
  }],
  duration: {
    type: Number,
    required: [true, 'Thời lượng bài học là bắt buộc'],
    min: [1, 'Thời lượng bài học phải ít nhất 1 phút']
  },
  order: {
    type: Number,
    required: [true, 'Thứ tự bài học là bắt buộc'],
    min: [1, 'Thứ tự bài học phải bắt đầu từ 1']
  },
  isPreview: {
    type: Boolean,
    default: false
  },
  transcript: {
    type: String,
    maxlength: [10000, 'Transcript không được vượt quá 10000 ký tự']
  },
  objectives: [{
    type: String,
    maxlength: [200, 'Mục tiêu học tập không được vượt quá 200 ký tự']
  }],
  quiz: {
    questions: [QuizQuestionSchema]
  }
});

const RatingSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  rating: {
    type: Number,
    required: [true, 'Đánh giá là bắt buộc'],
    min: [1, 'Đánh giá phải từ 1 đến 5'],
    max: [5, 'Đánh giá phải từ 1 đến 5']
  },
  comment: {
    type: String,
    maxlength: [500, 'Nhận xét không được vượt quá 500 ký tự']
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

const CourseSchema = new Schema<ICourse>({
  title: {
    type: String,
    required: [true, 'Tiêu đề khóa học là bắt buộc'],
    trim: true,
    maxlength: [200, 'Tiêu đề khóa học không được vượt quá 200 ký tự']
  },
  description: {
    type: String,
    required: [true, 'Mô tả khóa học là bắt buộc'],
    maxlength: [5000, 'Mô tả khóa học không được vượt quá 5000 ký tự']
  },
  shortDescription: {
    type: String,
    required: [true, 'Mô tả ngắn là bắt buộc'],
    maxlength: [300, 'Mô tả ngắn không được vượt quá 300 ký tự']
  },
  teacherId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Giáo viên là bắt buộc']
  },
  category: {
    type: String,
    enum: Object.values(CourseCategory),
    required: [true, 'Danh mục khóa học là bắt buộc']
  },
  level: {
    type: String,
    enum: Object.values(CourseLevel),
    required: [true, 'Trình độ khóa học là bắt buộc']
  },
  price: {
    type: Number,
    required: [true, 'Giá khóa học là bắt buộc'],
    min: [0, 'Giá khóa học không được âm']
  },
  duration: {
    type: Number,
    required: [true, 'Thời lượng khóa học là bắt buộc'],
    min: [1, 'Thời lượng khóa học phải ít nhất 1 giờ']
  },
  thumbnail: {
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/.+/.test(v);
      },
      message: 'URL hình ảnh không hợp lệ'
    }
  },
  videoIntro: {
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/.+/.test(v);
      },
      message: 'URL video giới thiệu không hợp lệ'
    }
  },
  isPublished: {
    type: Boolean,
    default: false
  },
  curriculum: [LessonSchema],
  ratings: [RatingSchema],
  averageRating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  totalStudents: {
    type: Number,
    default: 0,
    min: 0
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [50, 'Tag không được vượt quá 50 ký tự']
  }],
  requirements: [{
    type: String,
    maxlength: [200, 'Yêu cầu không được vượt quá 200 ký tự']
  }],
  whatYouWillLearn: [{
    type: String,
    maxlength: [200, 'Mục tiêu học tập không được vượt quá 200 ký tự']
  }],
  language: {
    type: String,
    default: 'vi',
    enum: ['vi', 'en', 'both']
  },
  subtitles: [{
    type: String,
    enum: ['vi', 'en']
  }]
}, {
  timestamps: true
});

// Indexes cho tìm kiếm và hiệu suất
// CourseSchema.index({ title: 'text', description: 'text' }, { default_language: 'none' });
CourseSchema.index({ category: 1, level: 1 });
CourseSchema.index({ teacherId: 1 });
CourseSchema.index({ isPublished: 1 });
CourseSchema.index({ averageRating: -1 });
CourseSchema.index({ totalStudents: -1 });
CourseSchema.index({ createdAt: -1 });

// Middleware để tính toán averageRating
CourseSchema.pre('save', function(next) {
  if (this.ratings && this.ratings.length > 0) {
    const totalRating = this.ratings.reduce((sum, rating) => sum + rating.rating, 0);
    this.averageRating = Math.round((totalRating / this.ratings.length) * 10) / 10;
  }
  next();
});

export default mongoose.models.Course || mongoose.model<ICourse>('Course', CourseSchema);
