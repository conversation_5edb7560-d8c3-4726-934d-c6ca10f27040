import mongoose, { Document, Schema } from 'mongoose';
import { TestType, CourseLevel, QuestionType } from '@/types';

export interface ITest extends Document {
  title: string;
  description: string;
  type: TestType;
  level: CourseLevel;
  duration: number; // in minutes
  totalQuestions: number;
  passingScore: number;
  instructions: string;
  questions: IQuestion[];
  createdBy: mongoose.Types.ObjectId;
  isPublished: boolean;
  tags: string[];
  difficulty: 'easy' | 'medium' | 'hard';
  createdAt: Date;
  updatedAt: Date;
}

export interface IQuestion {
  type: QuestionType;
  question: string;
  options?: string[]; // for multiple choice
  correctAnswer: string | string[]; // can be array for multiple correct answers
  points: number;
  explanation?: string;
  audioUrl?: string; // for listening questions
  imageUrl?: string;
  timeLimit?: number; // specific time limit for this question
  order: number;
}

const QuestionSchema = new Schema({
  type: {
    type: String,
    enum: Object.values(QuestionType),
    required: [true, '<PERSON><PERSON><PERSON> câu hỏi là bắt buộc']
  },
  question: {
    type: String,
    required: [true, 'Nội dung câu hỏi là bắt buộc'],
    maxlength: [2000, 'Câu hỏi không được vượt quá 2000 ký tự']
  },
  options: [{
    type: String,
    maxlength: [500, 'Tùy chọn không được vượt quá 500 ký tự']
  }],
  correctAnswer: {
    type: Schema.Types.Mixed, // Can be string or array
    required: [true, 'Đáp án đúng là bắt buộc']
  },
  points: {
    type: Number,
    required: [true, 'Điểm số là bắt buộc'],
    min: [0, 'Điểm số không được âm'],
    max: [100, 'Điểm số không được vượt quá 100']
  },
  explanation: {
    type: String,
    maxlength: [1000, 'Giải thích không được vượt quá 1000 ký tự']
  },
  audioUrl: {
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/.+/.test(v);
      },
      message: 'URL audio không hợp lệ'
    }
  },
  imageUrl: {
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/.+/.test(v);
      },
      message: 'URL hình ảnh không hợp lệ'
    }
  },
  timeLimit: {
    type: Number,
    min: [10, 'Thời gian tối thiểu là 10 giây']
  },
  order: {
    type: Number,
    required: [true, 'Thứ tự câu hỏi là bắt buộc'],
    min: [1, 'Thứ tự phải bắt đầu từ 1']
  }
});

const TestSchema = new Schema<ITest>({
  title: {
    type: String,
    required: [true, 'Tiêu đề bài kiểm tra là bắt buộc'],
    trim: true,
    maxlength: [200, 'Tiêu đề không được vượt quá 200 ký tự']
  },
  description: {
    type: String,
    required: [true, 'Mô tả bài kiểm tra là bắt buộc'],
    maxlength: [2000, 'Mô tả không được vượt quá 2000 ký tự']
  },
  type: {
    type: String,
    enum: Object.values(TestType),
    required: [true, 'Loại bài kiểm tra là bắt buộc']
  },
  level: {
    type: String,
    enum: Object.values(CourseLevel),
    required: [true, 'Trình độ bài kiểm tra là bắt buộc']
  },
  duration: {
    type: Number,
    required: [true, 'Thời lượng bài kiểm tra là bắt buộc'],
    min: [5, 'Thời lượng tối thiểu là 5 phút'],
    max: [300, 'Thời lượng tối đa là 300 phút']
  },
  totalQuestions: {
    type: Number,
    required: [true, 'Tổng số câu hỏi là bắt buộc'],
    min: [1, 'Phải có ít nhất 1 câu hỏi']
  },
  passingScore: {
    type: Number,
    required: [true, 'Điểm đạt là bắt buộc'],
    min: [0, 'Điểm đạt không được âm'],
    max: [100, 'Điểm đạt không được vượt quá 100%']
  },
  instructions: {
    type: String,
    required: [true, 'Hướng dẫn làm bài là bắt buộc'],
    maxlength: [3000, 'Hướng dẫn không được vượt quá 3000 ký tự']
  },
  questions: [QuestionSchema],
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Người tạo bài kiểm tra là bắt buộc']
  },
  isPublished: {
    type: Boolean,
    default: false
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [50, 'Tag không được vượt quá 50 ký tự']
  }],
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'medium'
  }
}, {
  timestamps: true
});

// Indexes cho tìm kiếm và hiệu suất
TestSchema.index({ title: 'text', description: 'text', tags: 'text' });
TestSchema.index({ type: 1, level: 1 });
TestSchema.index({ createdBy: 1 });
TestSchema.index({ isPublished: 1 });
TestSchema.index({ difficulty: 1 });
TestSchema.index({ createdAt: -1 });

// Middleware để cập nhật totalQuestions
TestSchema.pre('save', function(next) {
  this.totalQuestions = this.questions.length;
  next();
});

// Virtual để tính điểm tối đa
TestSchema.virtual('maxScore').get(function() {
  return this.questions.reduce((total, question) => total + question.points, 0);
});

export default mongoose.models.Test || mongoose.model<ITest>('Test', TestSchema);
