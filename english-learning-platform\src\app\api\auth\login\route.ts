import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { validateEmail } from '@/lib/utils';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { email, password } = body;

    // Validation
    const errors: Record<string, string> = {};

    if (!email) {
      errors.email = 'Email là bắt buộc';
    } else if (!validateEmail(email)) {
      errors.email = 'Email không hợp lệ';
    }

    if (!password) {
      errors.password = 'Mật khẩu là bắt buộc';
    }

    if (Object.keys(errors).length > 0) {
      return NextResponse.json({
        success: false,
        message: 'Dữ liệu không hợp lệ',
        errors
      }, { status: 400 });
    }

    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Email hoặc mật khẩu không chính xác',
        errors: { email: 'Tài khoản không tồn tại' }
      }, { status: 401 });
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return NextResponse.json({
        success: false,
        message: 'Email hoặc mật khẩu không chính xác',
        errors: { password: 'Mật khẩu không chính xác' }
      }, { status: 401 });
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user._id,
        email: user.email,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    // Remove password from response
    const userResponse = user.toJSON();

    // Set HTTP-only cookie
    const response = NextResponse.json({
      success: true,
      message: 'Đăng nhập thành công',
      data: {
        user: userResponse,
        token
      }
    });

    // Set cookie with token
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
    });

    return response;

  } catch (error) {
    console.error('Login error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi hệ thống, vui lòng thử lại sau',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
