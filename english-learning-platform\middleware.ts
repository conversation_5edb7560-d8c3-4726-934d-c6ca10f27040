import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { verifyToken } from '@/lib/auth';

// Define protected routes
const protectedRoutes = [
  '/dashboard',
  '/profile',
  '/tests',
  '/courses',
  '/admin',
  '/teacher'
];

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/logout'
];

// Define admin-only routes
const adminRoutes = [
  '/dashboard/admin'
];

// Define teacher routes
const teacherRoutes = [
  '/dashboard/teacher'
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and API routes (except auth)
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/static') ||
    pathname.includes('.') ||
    (pathname.startsWith('/api') && !pathname.startsWith('/api/auth'))
  ) {
    return NextResponse.next();
  }

  // Check if route is public
  const isPublicRoute = publicRoutes.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  );

  // Check if route is protected
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  );

  // Get token from cookie
  const token = request.cookies.get('auth-token')?.value;

  // If accessing protected route without token, redirect to login
  if (isProtectedRoute && !token) {
    const loginUrl = new URL('/auth/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // If has token, verify it
  if (token) {
    try {
      const user = await verifyToken(token);
      
      if (!user) {
        // Invalid token, clear cookie and redirect to login if accessing protected route
        const response = isProtectedRoute 
          ? NextResponse.redirect(new URL('/auth/login', request.url))
          : NextResponse.next();
        
        response.cookies.delete('auth-token');
        return response;
      }

      // Check role-based access
      if (adminRoutes.some(route => pathname.startsWith(route))) {
        if (user.role !== 'admin') {
          return NextResponse.redirect(new URL('/dashboard', request.url));
        }
      }

      if (teacherRoutes.some(route => pathname.startsWith(route))) {
        if (user.role !== 'teacher' && user.role !== 'admin') {
          return NextResponse.redirect(new URL('/dashboard', request.url));
        }
      }

      // If authenticated user tries to access auth pages, redirect to dashboard
      if (pathname.startsWith('/auth/') && pathname !== '/auth/logout') {
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }

    } catch (error) {
      console.error('Middleware auth error:', error);
      
      // Clear invalid token
      const response = isProtectedRoute 
        ? NextResponse.redirect(new URL('/auth/login', request.url))
        : NextResponse.next();
      
      response.cookies.delete('auth-token');
      return response;
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
