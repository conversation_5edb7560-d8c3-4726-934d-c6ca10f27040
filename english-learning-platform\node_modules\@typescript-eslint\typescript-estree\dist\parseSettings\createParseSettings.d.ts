import * as ts from 'typescript';
import type { TSESTreeOptions } from '../parser-options';
import type { MutableParseSettings } from './index';
export declare function createParseSettings(code: string | ts.SourceFile, tsestreeOptions?: Partial<TSESTreeOptions>): MutableParseSettings;
export declare function clearTSConfigMatchCache(): void;
export declare function clearTSServerProjectService(): void;
//# sourceMappingURL=createParseSettings.d.ts.map