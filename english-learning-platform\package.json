{"name": "english-learning-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "seed": "cross-env MONGODB_URI=mongodb://localhost:27017/e-lms tsx src/scripts/seeders/index.ts", "seed:users": "cross-env MONGODB_URI=mongodb://localhost:27017/e-lms tsx src/scripts/seeders/userSeeder.ts", "seed:courses": "cross-env MONGODB_URI=mongodb://localhost:27017/e-lms tsx src/scripts/seeders/courseSeeder.ts", "seed:tests": "cross-env MONGODB_URI=mongodb://localhost:27017/e-lms tsx src/scripts/seeders/testSeeder.ts"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.523.0", "mongoose": "^8.16.0", "next": "15.3.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}