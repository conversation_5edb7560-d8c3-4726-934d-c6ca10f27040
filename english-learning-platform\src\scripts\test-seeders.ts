// Test script to verify seeders work correctly
import connectDB from '@/lib/mongodb';
import { seedUsers } from './seeders/userSeeder';

async function testSeeders() {
  try {
    console.log('🧪 Testing database seeders...');
    
    // Connect to database
    await connectDB();
    console.log('✅ Connected to MongoDB');

    // Test user seeder
    console.log('\n📝 Testing user seeder...');
    const users = await seedUsers();
    console.log(`✅ User seeder test completed. Created/found ${users.length} users`);

    console.log('\n🎉 Seeder test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error testing seeders:', error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

// Run if called directly
if (require.main === module) {
  testSeeders();
}

export default testSeeders;
