"use client";

import { useState } from "react";
import { Play, Pause, Volume2, Mic, MicOff } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { QuestionType } from "@/types";

interface Question {
  id: string;
  type: QuestionType;
  question: string;
  options?: string[];
  correctAnswer: string | string[];
  points: number;
  audioUrl?: string;
  imageUrl?: string;
  timeLimit?: number;
  order: number;
}

interface QuestionRendererProps {
  question: Question;
  userAnswer?: string | string[];
  onAnswerChange: (answer: string | string[]) => void;
  isReadOnly?: boolean;
  showCorrectAnswer?: boolean;
}

export default function QuestionRenderer({
  question,
  userAnswer,
  onAnswerChange,
  isReadOnly = false,
  showCorrectAnswer = false
}: QuestionRendererProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);

  const handleMultipleChoice = (selectedOption: string) => {
    if (isReadOnly) return;
    onAnswerChange(selectedOption);
  };

  const handleFillInBlank = (value: string) => {
    if (isReadOnly) return;
    onAnswerChange(value);
  };

  const handleTrueFalse = (value: string) => {
    if (isReadOnly) return;
    onAnswerChange(value);
  };

  const handleEssay = (value: string) => {
    if (isReadOnly) return;
    onAnswerChange(value);
  };

  const handleAudioPlay = () => {
    if (question.audioUrl) {
      const audio = new Audio(question.audioUrl);
      setIsPlaying(true);
      audio.play();
      audio.onended = () => setIsPlaying(false);
    }
  };

  const handleStartRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      const chunks: BlobPart[] = [];

      mediaRecorder.ondataavailable = (event) => {
        chunks.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/wav' });
        setAudioBlob(blob);
        onAnswerChange(URL.createObjectURL(blob));
      };

      mediaRecorder.start();
      setIsRecording(true);

      // Stop recording after 2 minutes max
      setTimeout(() => {
        if (mediaRecorder.state === 'recording') {
          mediaRecorder.stop();
          setIsRecording(false);
        }
      }, 120000);

    } catch (error) {
      console.error('Error accessing microphone:', error);
    }
  };

  const handleStopRecording = () => {
    setIsRecording(false);
    // MediaRecorder will handle the stop event
  };

  const renderQuestionContent = () => {
    switch (question.type) {
      case QuestionType.MULTIPLE_CHOICE:
        return (
          <div className="space-y-3">
            {question.options?.map((option, index) => (
              <label
                key={index}
                className={`
                  flex items-center p-4 border rounded-lg cursor-pointer transition-colors
                  ${isReadOnly ? 'cursor-default' : 'cursor-pointer'}
                  ${userAnswer === option
                    ? showCorrectAnswer
                      ? option === question.correctAnswer
                        ? 'border-green-500 bg-green-50'
                        : 'border-red-500 bg-red-50'
                      : 'border-blue-500 bg-blue-50'
                    : showCorrectAnswer && option === question.correctAnswer
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }
                `}
              >
                <input
                  type="radio"
                  name={`question-${question.id}`}
                  value={option}
                  checked={userAnswer === option}
                  onChange={(e) => handleMultipleChoice(e.target.value)}
                  disabled={isReadOnly}
                  className="mr-3"
                />
                <span className="text-gray-900">{option}</span>
                {showCorrectAnswer && option === question.correctAnswer && (
                  <Badge variant="default" className="ml-auto">Đáp án đúng</Badge>
                )}
              </label>
            ))}
          </div>
        );

      case QuestionType.FILL_IN_BLANK:
        return (
          <div className="space-y-4">
            <Input
              value={userAnswer as string || ''}
              onChange={(e) => handleFillInBlank(e.target.value)}
              placeholder="Nhập câu trả lời của bạn..."
              disabled={isReadOnly}
              className={showCorrectAnswer 
                ? userAnswer === question.correctAnswer
                  ? 'border-green-500 bg-green-50'
                  : 'border-red-500 bg-red-50'
                : ''
              }
            />
            {showCorrectAnswer && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <span className="font-medium">Đáp án đúng: </span>
                <span className="text-blue-800">{question.correctAnswer}</span>
              </div>
            )}
          </div>
        );

      case QuestionType.TRUE_FALSE:
        return (
          <div className="space-y-3">
            {['Đúng', 'Sai'].map((option) => (
              <label
                key={option}
                className={`
                  flex items-center p-4 border rounded-lg cursor-pointer transition-colors
                  ${isReadOnly ? 'cursor-default' : 'cursor-pointer'}
                  ${userAnswer === option
                    ? showCorrectAnswer
                      ? option === question.correctAnswer
                        ? 'border-green-500 bg-green-50'
                        : 'border-red-500 bg-red-50'
                      : 'border-blue-500 bg-blue-50'
                    : showCorrectAnswer && option === question.correctAnswer
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }
                `}
              >
                <input
                  type="radio"
                  name={`question-${question.id}`}
                  value={option}
                  checked={userAnswer === option}
                  onChange={(e) => handleTrueFalse(e.target.value)}
                  disabled={isReadOnly}
                  className="mr-3"
                />
                <span className="text-gray-900">{option}</span>
                {showCorrectAnswer && option === question.correctAnswer && (
                  <Badge variant="default" className="ml-auto">Đáp án đúng</Badge>
                )}
              </label>
            ))}
          </div>
        );

      case QuestionType.ESSAY:
        return (
          <div className="space-y-4">
            <textarea
              value={userAnswer as string || ''}
              onChange={(e) => handleEssay(e.target.value)}
              placeholder="Viết câu trả lời của bạn (tối thiểu 50 từ)..."
              disabled={isReadOnly}
              rows={8}
              className={`
                w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500
                ${isReadOnly ? 'bg-gray-50' : ''}
              `}
            />
            <div className="text-sm text-gray-500">
              Số từ: {(userAnswer as string || '').split(' ').filter(word => word.length > 0).length}
            </div>
            {showCorrectAnswer && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <span className="font-medium">Gợi ý đáp án: </span>
                <p className="text-blue-800 mt-2">{question.correctAnswer}</p>
              </div>
            )}
          </div>
        );

      case QuestionType.AUDIO_RESPONSE:
        return (
          <div className="space-y-4">
            {question.audioUrl && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAudioPlay}
                    disabled={isPlaying}
                  >
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    {isPlaying ? 'Đang phát' : 'Nghe audio'}
                  </Button>
                  <Volume2 className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Nghe và ghi âm câu trả lời</span>
                </div>
              </div>
            )}
            
            <div className="p-4 border-2 border-dashed border-gray-300 rounded-lg text-center">
              {!isRecording && !audioBlob && (
                <div>
                  <Mic className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600 mb-3">Nhấn để bắt đầu ghi âm câu trả lời</p>
                  <Button onClick={handleStartRecording} disabled={isReadOnly}>
                    <Mic className="h-4 w-4 mr-2" />
                    Bắt đầu ghi âm
                  </Button>
                </div>
              )}
              
              {isRecording && (
                <div>
                  <div className="animate-pulse">
                    <MicOff className="h-8 w-8 text-red-500 mx-auto mb-2" />
                  </div>
                  <p className="text-red-600 mb-3">Đang ghi âm...</p>
                  <Button variant="destructive" onClick={handleStopRecording}>
                    Dừng ghi âm
                  </Button>
                </div>
              )}
              
              {audioBlob && (
                <div>
                  <Volume2 className="h-8 w-8 text-green-500 mx-auto mb-2" />
                  <p className="text-green-600 mb-3">Đã ghi âm thành công!</p>
                  <audio controls src={userAnswer as string} className="mx-auto" />
                  {!isReadOnly && (
                    <div className="mt-3">
                      <Button variant="outline" onClick={() => {
                        setAudioBlob(null);
                        onAnswerChange('');
                      }}>
                        Ghi âm lại
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        );

      default:
        return (
          <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
            Loại câu hỏi không được hỗ trợ
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Question Header */}
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {question.question}
          </h3>
          {question.imageUrl && (
            <div className="mb-4">
              <img
                src={question.imageUrl}
                alt="Question illustration"
                className="max-w-full h-auto rounded-lg"
              />
            </div>
          )}
        </div>
        <Badge variant="outline" className="ml-4">
          {question.points} điểm
        </Badge>
      </div>

      {/* Question Content */}
      {renderQuestionContent()}
    </div>
  );
}
