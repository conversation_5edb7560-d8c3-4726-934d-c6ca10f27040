import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Test from '@/models/Test';
import TestResult from '@/models/TestResult';
import { getAuthUser } from '@/lib/auth';
import { QuestionType } from '@/types';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find test
    const test = await Test.findById(id);
    if (!test) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy bài kiểm tra'
      }, { status: 404 });
    }

    if (!test.isPublished) {
      return NextResponse.json({
        success: false,
        message: '<PERSON><PERSON><PERSON> kiểm tra chưa được xuất bản'
      }, { status: 403 });
    }

    const body = await request.json();
    const { answers, timeSpent, startedAt } = body;

    // Validation
    if (!answers || typeof answers !== 'object') {
      return NextResponse.json({
        success: false,
        message: 'Dữ liệu câu trả lời không hợp lệ'
      }, { status: 400 });
    }

    if (!timeSpent || timeSpent < 0) {
      return NextResponse.json({
        success: false,
        message: 'Thời gian làm bài không hợp lệ'
      }, { status: 400 });
    }

    // Check if user already submitted this test
    const existingResult = await TestResult.findOne({
      studentId: user.userId,
      testId: id
    });

    if (existingResult) {
      return NextResponse.json({
        success: false,
        message: 'Bạn đã làm bài kiểm tra này rồi'
      }, { status: 409 });
    }

    // Grade the test
    const gradingResult = await gradeTest(test, answers);
    
    // Create test result
    const testResult = new TestResult({
      studentId: user.userId,
      testId: id,
      answers: gradingResult.answers,
      totalScore: gradingResult.totalScore,
      maxScore: gradingResult.maxScore,
      percentage: gradingResult.percentage,
      timeSpent,
      startedAt: new Date(startedAt),
      completedAt: new Date(),
      isPassed: gradingResult.percentage >= test.passingScore,
      detailedAnalysis: gradingResult.detailedAnalysis,
      gradingStatus: gradingResult.needsManualGrading ? 'pending' : 'completed'
    });

    await testResult.save();

    // Populate student and test info
    await testResult.populate([
      { path: 'studentId', select: 'name email' },
      { path: 'testId', select: 'title description type level' }
    ]);

    return NextResponse.json({
      success: true,
      message: 'Nộp bài thành công',
      data: testResult
    }, { status: 201 });

  } catch (error) {
    console.error('Submit test error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi nộp bài kiểm tra',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

async function gradeTest(test: any, userAnswers: Record<string, any>) {
  let totalScore = 0;
  let maxScore = 0;
  let correctAnswers = 0;
  let incorrectAnswers = 0;
  let skippedAnswers = 0;
  let needsManualGrading = false;

  const answers = [];
  const skillScores: Record<string, { score: number; maxScore: number }> = {};

  for (const question of test.questions) {
    const userAnswer = userAnswers[question.id] || null;
    const isSkipped = !userAnswer;
    let isCorrect = false;
    let points = 0;
    let needsManual = false;

    maxScore += question.points;

    if (isSkipped) {
      skippedAnswers++;
    } else {
      // Grade based on question type
      switch (question.type) {
        case QuestionType.MULTIPLE_CHOICE:
        case QuestionType.TRUE_FALSE:
        case QuestionType.FILL_IN_BLANK:
          isCorrect = normalizeAnswer(userAnswer) === normalizeAnswer(question.correctAnswer);
          if (isCorrect) {
            points = question.points;
            correctAnswers++;
          } else {
            incorrectAnswers++;
          }
          break;

        case QuestionType.ESSAY:
        case QuestionType.AUDIO_RESPONSE:
          // These need manual grading
          needsManual = true;
          needsManualGrading = true;
          // For now, give partial credit
          points = Math.floor(question.points * 0.5);
          break;

        default:
          // Unknown question type
          incorrectAnswers++;
          break;
      }
    }

    totalScore += points;

    // Track skill scores
    const skill = getQuestionSkill(question.type, test.type);
    if (!skillScores[skill]) {
      skillScores[skill] = { score: 0, maxScore: 0 };
    }
    skillScores[skill].score += points;
    skillScores[skill].maxScore += question.points;

    answers.push({
      questionId: question.id,
      questionOrder: question.order,
      userAnswer,
      correctAnswer: question.correctAnswer,
      isCorrect,
      points,
      maxPoints: question.points,
      timeSpent: 0, // TODO: Track individual question time
      isSkipped,
      needsManualGrading: needsManual
    });
  }

  // Calculate skill breakdown
  const skillBreakdown = Object.entries(skillScores).map(([skill, scores]) => ({
    skill,
    score: scores.score,
    maxScore: scores.maxScore,
    percentage: scores.maxScore > 0 ? Math.round((scores.score / scores.maxScore) * 100) : 0
  }));

  const percentage = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0;

  return {
    answers,
    totalScore,
    maxScore,
    percentage,
    needsManualGrading,
    detailedAnalysis: {
      correctAnswers,
      incorrectAnswers,
      skippedAnswers,
      skillBreakdown
    }
  };
}

function normalizeAnswer(answer: string): string {
  if (typeof answer !== 'string') return '';
  return answer.toLowerCase().trim().replace(/\s+/g, ' ');
}

function getQuestionSkill(questionType: QuestionType, testType: string): string {
  switch (questionType) {
    case QuestionType.MULTIPLE_CHOICE:
    case QuestionType.FILL_IN_BLANK:
      return testType === 'vocabulary' ? 'Từ vựng' : 'Ngữ pháp';
    case QuestionType.ESSAY:
      return 'Viết';
    case QuestionType.AUDIO_RESPONSE:
      return 'Nói';
    case QuestionType.TRUE_FALSE:
      return 'Đọc hiểu';
    default:
      return 'Tổng hợp';
  }
}
