import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Test from '@/models/Test';
import TestResult from '@/models/TestResult';
import User from '@/models/User';
import { getAuthUser } from '@/lib/auth';
import { QuestionType } from '@/types';
import { AutomaticScoring } from '@/lib/scoring';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB();

    const { id } = await params;
    
    // Check authentication
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    // Find test
    const test = await Test.findById(id);
    if (!test) {
      return NextResponse.json({
        success: false,
        message: '<PERSON>hông tìm thấy bài kiểm tra'
      }, { status: 404 });
    }

    if (!test.isPublished) {
      return NextResponse.json({
        success: false,
        message: '<PERSON><PERSON><PERSON> kiểm tra chưa được xuất bản'
      }, { status: 403 });
    }

    const body = await request.json();
    const { answers, timeSpent, startedAt } = body;

    // Validation
    if (!answers || typeof answers !== 'object') {
      return NextResponse.json({
        success: false,
        message: 'Dữ liệu câu trả lời không hợp lệ'
      }, { status: 400 });
    }

    if (!timeSpent || timeSpent < 0) {
      return NextResponse.json({
        success: false,
        message: 'Thời gian làm bài không hợp lệ'
      }, { status: 400 });
    }

    // Check if user already submitted this test
    const existingResult = await TestResult.findOne({
      studentId: user.userId,
      testId: id
    });

    if (existingResult) {
      return NextResponse.json({
        success: false,
        message: 'Bạn đã làm bài kiểm tra này rồi'
      }, { status: 409 });
    }

    // Grade the test using automatic scoring
    const gradingResult = await gradeTestWithAutomaticScoring(test, answers);
    
    // Create test result
    const testResult = new TestResult({
      studentId: user.userId,
      testId: id,
      answers: gradingResult.answers,
      totalScore: gradingResult.totalScore,
      maxScore: gradingResult.maxScore,
      percentage: gradingResult.percentage,
      timeSpent,
      startedAt: new Date(startedAt),
      completedAt: new Date(),
      isPassed: gradingResult.percentage >= test.passingScore,
      detailedAnalysis: gradingResult.detailedAnalysis,
      gradingStatus: gradingResult.needsManualGrading ? 'pending' : 'completed'
    });

    await testResult.save();

    // Populate student and test info
    await testResult.populate([
      { path: 'studentId', select: 'name email' },
      { path: 'testId', select: 'title description type level' }
    ]);

    return NextResponse.json({
      success: true,
      message: 'Nộp bài thành công',
      data: testResult
    }, { status: 201 });

  } catch (error) {
    console.error('Submit test error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi nộp bài kiểm tra',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

async function gradeTestWithAutomaticScoring(test: any, userAnswers: Record<string, any>) {
  const scoringResults = [];
  const detailedAnswers = [];

  for (const question of test.questions) {
    const userAnswer = userAnswers[question._id];

    if (userAnswer !== undefined && userAnswer !== null) {
      const scoringResult = AutomaticScoring.scoreQuestion(question, {
        questionId: question._id,
        answer: userAnswer
      });

      scoringResults.push(scoringResult);

      detailedAnswers.push({
        questionId: question._id,
        questionOrder: question.order,
        userAnswer: scoringResult.userAnswer,
        correctAnswer: scoringResult.correctAnswer,
        isCorrect: scoringResult.isCorrect,
        points: scoringResult.score,
        maxPoints: scoringResult.maxScore,
        explanation: scoringResult.explanation,
        feedback: scoringResult.feedback,
        isSkipped: false,
        needsManualGrading: question.type === QuestionType.ESSAY || question.type === QuestionType.AUDIO_RESPONSE
      });
    } else {
      // Handle skipped questions
      scoringResults.push({
        questionId: question._id,
        userAnswer: null,
        correctAnswer: question.correctAnswer,
        isCorrect: false,
        score: 0,
        maxScore: question.points,
        feedback: 'Câu hỏi bị bỏ qua'
      });

      detailedAnswers.push({
        questionId: question._id,
        questionOrder: question.order,
        userAnswer: null,
        correctAnswer: question.correctAnswer,
        isCorrect: false,
        points: 0,
        maxPoints: question.points,
        explanation: 'Câu hỏi không được trả lời',
        feedback: 'Bỏ qua',
        isSkipped: true,
        needsManualGrading: false
      });
    }
  }

  // Calculate overall score
  const overallScore = AutomaticScoring.calculateOverallScore(scoringResults);

  // Generate skill breakdown based on question types
  const skillBreakdown = generateSkillBreakdown(test.questions, scoringResults);

  const needsManualGrading = detailedAnswers.some(answer => answer.needsManualGrading);

  return {
    answers: detailedAnswers,
    totalScore: overallScore.totalScore,
    maxScore: overallScore.maxScore,
    percentage: overallScore.percentage,
    needsManualGrading,
    detailedAnalysis: {
      correctAnswers: overallScore.correctAnswers,
      incorrectAnswers: overallScore.incorrectAnswers,
      skippedAnswers: test.questions.length - (overallScore.correctAnswers + overallScore.incorrectAnswers),
      skillBreakdown: skillBreakdown
    }
  };
}

function generateSkillBreakdown(questions: any[], scoringResults: any[]) {
  const skillMap = new Map();

  questions.forEach((question, index) => {
    const result = scoringResults[index];
    if (!result) return;

    let skillName = 'Tổng hợp';

    // Map question types to skills
    switch (question.type) {
      case QuestionType.LISTENING:
        skillName = 'Nghe';
        break;
      case QuestionType.READING:
        skillName = 'Đọc';
        break;
      case QuestionType.ESSAY:
        skillName = 'Viết';
        break;
      case QuestionType.AUDIO_RESPONSE:
        skillName = 'Nói';
        break;
      case QuestionType.MULTIPLE_CHOICE:
      case QuestionType.FILL_IN_BLANK:
        // Determine skill based on context or default to grammar
        skillName = 'Ngữ pháp';
        break;
      case QuestionType.TRUE_FALSE:
        skillName = 'Đọc hiểu';
        break;
      default:
        skillName = 'Tổng hợp';
    }

    if (!skillMap.has(skillName)) {
      skillMap.set(skillName, {
        skill: skillName,
        score: 0,
        maxScore: 0,
        percentage: 0
      });
    }

    const skill = skillMap.get(skillName);
    skill.score += result.score;
    skill.maxScore += result.maxScore;
  });

  // Calculate percentages
  const skillBreakdown = Array.from(skillMap.values()).map(skill => ({
    ...skill,
    percentage: skill.maxScore > 0 ? Math.round((skill.score / skill.maxScore) * 100) : 0
  }));

  return skillBreakdown;
}
