"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON><PERSON>t, Clock, Users, Target, Play, Eye } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { LoadingPage } from "@/components/ui/loading";
import { ErrorPage } from "@/components/ui/error";
import Header from "@/components/layout/Header";
import QuestionRenderer from "@/components/test/QuestionRenderer";

interface TestData {
  _id: string;
  title: string;
  description: string;
  type: string;
  level: string;
  duration: number;
  totalQuestions: number;
  passingScore: number;
  instructions: string;
  questions: Array<{
    _id: string;
    type: string;
    question: string;
    options?: string[];
    correctAnswer: string | string[];
    points: number;
    order: number;
    explanation?: string;
    audioUrl?: string;
    passage?: string;
  }>;
  difficulty: string;
  tags: string[];
  isPublished: boolean;
  createdBy?: {
    name: string;
  };
}

interface TestPreviewPageProps {
  params: {
    id: string;
  };
}

export default function TestPreviewPage({ params }: TestPreviewPageProps) {
  const router = useRouter();
  const [test, setTest] = useState<TestData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPreviewQuestion, setCurrentPreviewQuestion] = useState(0);
  const [previewAnswers, setPreviewAnswers] = useState<Record<string, any>>({});
  const [testId, setTestId] = useState<string>('');

  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params;
      setTestId(resolvedParams.id);
    };
    getParams();
  }, [params]);

  useEffect(() => {
    if (testId) {
      loadTest();
    }
  }, [testId]);

  const loadTest = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/tests/${testId}`);
      const data = await response.json();

      if (data.success) {
        setTest(data.data);
      } else {
        setError(data.message || 'Không thể tải bài kiểm tra');
      }
    } catch (error) {
      console.error('Load test error:', error);
      setError('Lỗi kết nối. Vui lòng thử lại.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePreviewAnswerChange = (questionId: string, answer: any) => {
    setPreviewAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const getTypeLabel = (type: string) => {
    const typeLabels: Record<string, string> = {
      'vocabulary': 'Từ vựng',
      'grammar': 'Ngữ pháp',
      'listening': 'Nghe',
      'reading': 'Đọc',
      'writing': 'Viết',
      'speaking': 'Nói',
      'comprehensive': 'Tổng hợp'
    };
    return typeLabels[type] || type;
  };

  const getLevelLabel = (level: string) => {
    const levelLabels: Record<string, string> = {
      'beginner': 'Cơ bản',
      'intermediate': 'Trung cấp',
      'advanced': 'Nâng cao'
    };
    return levelLabels[level] || level;
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifficultyLabel = (difficulty: string) => {
    const difficultyLabels: Record<string, string> = {
      'easy': 'Dễ',
      'medium': 'Trung bình',
      'hard': 'Khó'
    };
    return difficultyLabels[difficulty] || difficulty;
  };

  const breadcrumbItems = [
    { label: "Luyện thi", href: "/tests" },
    { label: test?.title || "Bài kiểm tra", href: `/tests/${testId}` },
    { label: "Xem trước", current: true }
  ];

  if (isLoading) {
    return <LoadingPage message="Đang tải bài kiểm tra..." />;
  }

  if (error || !test) {
    return <ErrorPage title="Không thể tải bài kiểm tra" message={error || "Bài kiểm tra không tồn tại"} />;
  }

  // Get first 2-3 questions for preview
  const previewQuestions = test.questions.slice(0, Math.min(3, test.questions.length));

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Breadcrumb items={breadcrumbItems} className="mb-6" />

        {/* Test Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900">{test.title}</h1>
                <Badge className={getDifficultyColor(test.difficulty)}>
                  {getDifficultyLabel(test.difficulty)}
                </Badge>
              </div>
              <p className="text-gray-600 mb-4">{test.description}</p>
              
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline">{getTypeLabel(test.type)}</Badge>
                <Badge variant="outline">{getLevelLabel(test.level)}</Badge>
                {test.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary">{tag}</Badge>
                ))}
              </div>
            </div>

            <Button
              variant="outline"
              onClick={() => router.back()}
              className="ml-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
          </div>

          {/* Test Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
              <Clock className="h-5 w-5 text-blue-600" />
              <div>
                <div className="text-sm text-blue-600">Thời gian</div>
                <div className="font-semibold text-blue-900">{test.duration} phút</div>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
              <Target className="h-5 w-5 text-green-600" />
              <div>
                <div className="text-sm text-green-600">Số câu hỏi</div>
                <div className="font-semibold text-green-900">{test.totalQuestions}</div>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
              <Users className="h-5 w-5 text-purple-600" />
              <div>
                <div className="text-sm text-purple-600">Điểm đạt</div>
                <div className="font-semibold text-purple-900">{test.passingScore}%</div>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
              <Eye className="h-5 w-5 text-orange-600" />
              <div>
                <div className="text-sm text-orange-600">Xem trước</div>
                <div className="font-semibold text-orange-900">{previewQuestions.length} câu</div>
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Hướng dẫn làm bài</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose max-w-none">
              <div dangerouslySetInnerHTML={{ __html: test.instructions }} />
            </div>
          </CardContent>
        </Card>

        {/* Preview Questions */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Câu hỏi mẫu ({previewQuestions.length} câu đầu)
            </CardTitle>
            <CardDescription>
              Đây là một số câu hỏi mẫu để bạn làm quen với định dạng bài thi
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-8">
              {previewQuestions.map((question, index) => (
                <div key={question._id} className="border-b border-gray-200 pb-6 last:border-b-0">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium">Câu {index + 1}</h3>
                    <Badge variant="outline">{question.points} điểm</Badge>
                  </div>
                  
                  <QuestionRenderer
                    question={question}
                    userAnswer={previewAnswers[question._id]}
                    onAnswerChange={(answer) => handlePreviewAnswerChange(question._id, answer)}
                    isReadOnly={false}
                    showCorrectAnswer={false}
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-6">
            <div className="text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Sẵn sàng làm bài kiểm tra?
              </h3>
              <p className="text-gray-600 mb-6">
                Bạn đã xem qua các câu hỏi mẫu. Hãy bắt đầu làm bài kiểm tra chính thức để đánh giá trình độ của mình.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  onClick={() => router.push(`/tests/${testId}`)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Play className="h-5 w-5 mr-2" />
                  Bắt đầu làm bài
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => router.push('/tests')}
                >
                  Xem thêm bài kiểm tra khác
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
