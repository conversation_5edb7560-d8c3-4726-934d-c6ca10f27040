"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { <PERSON>, Clock, Users, BookO<PERSON>, Play, CheckCircle, AlertTriangle, Eye, Lock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import Header from "@/components/layout/Header";
import { formatPrice } from "@/lib/utils";

// Interface for course data from API
interface Course {
  _id: string;
  title: string;
  description: string;
  shortDescription?: string;
  teacherId: {
    _id: string;
    name: string;
    profile?: {
      bio?: string;
      avatar?: string;
    };
  };
  category: string;
  level: string;
  price: number;
  duration: number;
  thumbnail?: string;
  videoIntro?: string;
  averageRating: number;
  totalStudents: number;
  curriculum: Array<{
    title: string;
    description: string;
    duration: number;
    order: number;
    isPreview: boolean;
    videoUrl?: string;
    materials?: string[];
  }>;
  tags: string[];
  requirements: string[];
  whatYouWillLearn: string[];
  language: string;
  isPublished: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function CourseDetailPage() {
  const params = useParams();
  const [course, setCourse] = useState<Course | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [enrollmentLoading, setEnrollmentLoading] = useState(false);
  const [previewLesson, setPreviewLesson] = useState<any>(null);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [courseId, setCourseId] = useState<string>('');

  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params;
      setCourseId(resolvedParams.id as string);
    };
    getParams();
  }, [params]);

  useEffect(() => {
    if (courseId) {
      loadCourse();
    }
  }, [courseId]);

  const loadCourse = async () => {
    if (!courseId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/courses/${courseId}`);
      const data = await response.json();

      if (data.success) {
        setCourse(data.data);
        setIsEnrolled(data.isEnrolled || false);
      } else {
        setError(data.message || 'Lỗi khi tải thông tin khóa học');
      }
    } catch (error) {
      console.error('Load course error:', error);
      setError('Lỗi kết nối. Vui lòng thử lại.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEnroll = async () => {
    if (!courseId) return;

    setEnrollmentLoading(true);
    try {
      const response = await fetch(`/api/courses/${courseId}/enroll`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setIsEnrolled(true);
      } else {
        setError(data.message || 'Không thể đăng ký khóa học');
      }
    } catch (error) {
      console.error('Enrollment error:', error);
      setError('Lỗi kết nối. Vui lòng thử lại.');
    } finally {
      setEnrollmentLoading(false);
    }
  };

  const handlePreviewLesson = (lesson: any) => {
    setPreviewLesson(lesson);
    setShowPreviewDialog(true);
  };

  const getPreviewLessons = () => {
    if (!course?.curriculum) return [];
    return course.curriculum.filter(lesson => lesson.isPreview).slice(0, 3);
  };

  const getLevelLabel = (level: string) => {
    switch (level) {
      case 'beginner': return 'Cơ bản';
      case 'intermediate': return 'Trung cấp';
      case 'advanced': return 'Nâng cao';
      default: return level;
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'comprehensive': return 'Tổng hợp';
      case 'listening': return 'Nghe';
      case 'speaking': return 'Nói';
      case 'reading': return 'Đọc';
      case 'writing': return 'Viết';
      default: return category;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Đang tải thông tin khóa học...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="text-red-600 mb-4">
              <AlertTriangle className="h-12 w-12 mx-auto mb-2" />
              <p className="text-lg font-medium">Có lỗi xảy ra</p>
              <p className="text-sm">{error || 'Không tìm thấy khóa học'}</p>
            </div>
            <Button onClick={loadCourse} variant="outline">
              Thử lại
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Course Header */}
        <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Course Info */}
            <div className="lg:col-span-2">
              <div className="flex items-center gap-2 mb-4">
                <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                  {getLevelLabel(course.level)}
                </span>
                <span className="text-xs font-medium text-green-600 bg-green-50 px-2 py-1 rounded">
                  {getCategoryLabel(course.category)}
                </span>
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-4">{course.title}</h1>
              <p className="text-gray-600 mb-6">{course.shortDescription || course.description}</p>

              {/* Course Stats */}
              <div className="flex items-center gap-6 mb-6">
                <div className="flex items-center gap-1">
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                  <span className="font-medium">{course.averageRating.toFixed(1)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Users className="h-5 w-5 text-gray-400" />
                  <span className="text-gray-600">{course.totalStudents} học viên</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-5 w-5 text-gray-400" />
                  <span className="text-gray-600">{course.duration} giờ</span>
                </div>
              </div>

              {/* Preview Lessons */}
              {getPreviewLessons().length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">Xem trước miễn phí</h3>
                  <div className="space-y-2">
                    {getPreviewLessons().map((lesson, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Play className="h-4 w-4 text-blue-600" />
                          <span className="font-medium">{lesson.title}</span>
                          <span className="text-sm text-gray-500">({lesson.duration} phút)</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePreviewLesson(lesson)}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Xem trước
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Enrollment Card */}
            <div className="lg:col-span-1">
              <Card className="sticky top-8">
                <CardHeader>
                  <div className="text-3xl font-bold text-blue-600">
                    {course.price === 0 ? 'Miễn phí' : formatPrice(course.price)}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {!isEnrolled ? (
                    <Button
                      onClick={handleEnroll}
                      disabled={enrollmentLoading}
                      className="w-full"
                      size="lg"
                    >
                      {enrollmentLoading ? 'Đang đăng ký...' : 'Đăng ký ngay'}
                    </Button>
                  ) : (
                    <Button className="w-full" size="lg">
                      Tiếp tục học
                    </Button>
                  )}

                  {getPreviewLessons().length > 0 && (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        const previewLessons = getPreviewLessons();
                        if (previewLessons.length > 0) {
                          handlePreviewLesson(previewLessons[0]);
                        }
                      }}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Xem trước khóa học
                    </Button>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Course Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-8">
            {/* What you'll learn */}
            {course.whatYouWillLearn && course.whatYouWillLearn.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Bạn sẽ học được gì</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {course.whatYouWillLearn.map((item, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{item}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Course Description */}
            <Card>
              <CardHeader>
                <CardTitle>Mô tả khóa học</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none">
                  <p>{course.description}</p>
                </div>
              </CardContent>
            </Card>

            {/* Course Curriculum */}
            {course.curriculum && course.curriculum.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Nội dung khóa học</CardTitle>
                  <CardDescription>
                    {course.curriculum.length} bài học • {course.duration} giờ học
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {course.curriculum.map((lesson, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          {lesson.isPreview ? (
                            <Play className="h-4 w-4 text-blue-600" />
                          ) : (
                            <Lock className="h-4 w-4 text-gray-400" />
                          )}
                          <div>
                            <p className="font-medium">{lesson.title}</p>
                            <p className="text-sm text-gray-500">{lesson.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-500">{lesson.duration} phút</span>
                          {lesson.isPreview && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handlePreviewLesson(lesson)}
                              className="text-blue-600 hover:text-blue-700"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          <div className="lg:col-span-1">
            {/* Instructor Info */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Giảng viên</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold">
                      {course.teacherId.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium">{course.teacherId.name}</p>
                    <p className="text-sm text-gray-500">Giảng viên</p>
                  </div>
                </div>
                {course.teacherId.profile?.bio && (
                  <p className="text-sm text-gray-600">{course.teacherId.profile.bio}</p>
                )}
              </CardContent>
            </Card>

            {/* Requirements */}
            {course.requirements && course.requirements.length > 0 && (
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle>Yêu cầu</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {course.requirements.map((req, index) => (
                      <li key={index} className="text-sm flex items-start gap-2">
                        <span className="text-gray-400 mt-1">•</span>
                        {req}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Tags */}
            {course.tags && course.tags.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Thẻ</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {course.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>

      {/* Preview Dialog */}
      <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>{previewLesson?.title}</DialogTitle>
            <DialogDescription>
              Xem trước bài học miễn phí
            </DialogDescription>
          </DialogHeader>
          <div className="aspect-video bg-black rounded-lg flex items-center justify-center">
            <div className="text-white text-center">
              <Play className="h-16 w-16 mx-auto mb-4" />
              <p>Video preview sẽ được hiển thị ở đây</p>
              <p className="text-sm text-gray-300 mt-2">
                URL: {previewLesson?.videoUrl || 'Chưa có video'}
              </p>
            </div>
          </div>
          <div className="flex justify-between items-center pt-4">
            <div className="text-sm text-gray-600">
              Thời lượng: {previewLesson?.duration} phút
            </div>
            <Button onClick={() => {
              setShowPreviewDialog(false);
              // Scroll to enrollment section
              document.querySelector('[data-enrollment-section]')?.scrollIntoView({ behavior: 'smooth' });
            }}>
              Đăng ký ngay
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}