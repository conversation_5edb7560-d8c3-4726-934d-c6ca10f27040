"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { <PERSON>, Clock, Users, BookO<PERSON>, Play, CheckCircle, AlertTriangle, Eye, Lock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import Header from "@/components/layout/Header";
import { formatPrice } from "@/lib/utils";

// Interface for course data from API
interface Course {
  _id: string;
  title: string;
  description: string;
  shortDescription?: string;
  teacherId: {
    _id: string;
    name: string;
    profile?: {
      bio?: string;
      avatar?: string;
    };
  };
  category: string;
  level: string;
  price: number;
  duration: number;
  thumbnail?: string;
  videoIntro?: string;
  averageRating: number;
  totalStudents: number;
  curriculum: Array<{
    title: string;
    description: string;
    duration: number;
    order: number;
    isPreview: boolean;
    videoUrl?: string;
    materials?: string[];
  }>;
  tags: string[];
  requirements: string[];
  whatYouWillLearn: string[];
  language: string;
  isPublished: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function CourseDetailPage() {
  const params = useParams();
  const [course, setCourse] = useState<Course | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [enrollmentLoading, setEnrollmentLoading] = useState(false);
  const [previewLesson, setPreviewLesson] = useState<any>(null);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);

  useEffect(() => {
    if (params.id) {
      loadCourse();
    }
  }, [params.id]);

  const loadCourse = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/courses/${params.id}`);
      const data = await response.json();

      if (data.success) {
        setCourse(data.data);
        setIsEnrolled(data.isEnrolled || false);
      } else {
        setError(data.message || 'Lỗi khi tải thông tin khóa học');
      }
    } catch (error) {
      console.error('Load course error:', error);
      setError('Lỗi kết nối. Vui lòng thử lại.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEnroll = async () => {
    setEnrollmentLoading(true);
    try {
      const response = await fetch(`/api/courses/${params.id}/enroll`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setIsEnrolled(true);
        // You could show a success message here
      } else {
        setError(data.message || 'Lỗi khi đăng ký khóa học');
      }
    } catch (error) {
      console.error('Enroll error:', error);
      setError('Lỗi kết nối. Vui lòng thử lại.');
    } finally {
      setEnrollmentLoading(false);
    }
  };

  const handlePreviewLesson = (lesson: any) => {
    setPreviewLesson(lesson);
    setShowPreviewDialog(true);
  };

  const getPreviewLessons = () => {
    if (!course?.curriculum) return [];
    return course.curriculum.filter(lesson => lesson.isPreview).slice(0, 3);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Đang tải thông tin khóa học...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="text-red-600 mb-4">
              <AlertTriangle className="h-12 w-12 mx-auto mb-2" />
              <p className="text-lg font-medium">Có lỗi xảy ra</p>
              <p className="text-sm">{error || 'Không tìm thấy khóa học'}</p>
            </div>
            <Button onClick={loadCourse} variant="outline">
              Thử lại
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Course Header */}
        <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Course Info */}
            <div className="lg:col-span-2">
              <div className="flex items-center gap-2 mb-4">
                <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                  {course.level === 'beginner' ? 'Cơ bản' :
                   course.level === 'intermediate' ? 'Trung cấp' : 'Nâng cao'}
                </span>
                <span className="text-xs font-medium text-gray-600 bg-gray-100 px-2 py-1 rounded">
                  {course.category === 'comprehensive' ? 'Tổng hợp' :
                   course.category === 'listening' ? 'Nghe' :
                   course.category === 'speaking' ? 'Nói' :
                   course.category === 'reading' ? 'Đọc' :
                   course.category === 'writing' ? 'Viết' : course.category}
                </span>
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-4">{course.title}</h1>

              <p className="text-lg text-gray-600 mb-6">
                {course.shortDescription || course.description}
              </p>

              <div className="flex items-center gap-6 mb-6">
                <div className="flex items-center">
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                  <span className="text-lg font-medium text-gray-900 ml-1">{course.averageRating}</span>
                  <span className="text-gray-600 ml-1">({course.totalStudents} học viên)</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Clock className="h-5 w-5 mr-1" />
                  {course.duration} giờ
                </div>
                <div className="flex items-center text-gray-600">
                  <BookOpen className="h-5 w-5 mr-1" />
                  {course.curriculum?.length || 0} bài học
                </div>
              </div>

              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-300 rounded-full mr-3"></div>
                <div>
                  <p className="font-medium text-gray-900">{course.teacherId.name}</p>
                  <p className="text-sm text-gray-600">Giáo viên</p>
                </div>
              </div>
            </div>

            {/* Course Preview & Enrollment */}
            <div className="lg:col-span-1">
              <Card data-enrollment-section>
                <CardContent className="p-6">
                  {/* Video Preview */}
                  <div className="aspect-video bg-gray-200 rounded-lg mb-4 flex items-center justify-center relative overflow-hidden">
                    {course.thumbnail ? (
                      <img
                        src={course.thumbnail}
                        alt={course.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500"></div>
                    )}
                    <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                      <Button
                        variant="secondary"
                        size="lg"
                        className="bg-white/90 hover:bg-white text-gray-900"
                        onClick={() => {
                          const previewLessons = getPreviewLessons();
                          if (previewLessons.length > 0) {
                            handlePreviewLesson(previewLessons[0]);
                          }
                        }}
                      >
                        <Play className="h-6 w-6 mr-2" />
                        Xem trước khóa học
                      </Button>
                    </div>
                  </div>

                  {/* Price */}
                  <div className="text-center mb-6">
                    <div className="text-3xl font-bold text-blue-600 mb-2">
                      {formatPrice(course.price)}
                    </div>
                  </div>

                  {/* Enrollment Button */}
                  {isEnrolled ? (
                    <Button className="w-full" size="lg" disabled>
                      <CheckCircle className="h-5 w-5 mr-2" />
                      Đã đăng ký
                    </Button>
                  ) : (
                    <Button
                      className="w-full"
                      size="lg"
                      onClick={handleEnroll}
                      disabled={enrollmentLoading}
                    >
                      {enrollmentLoading ? 'Đang xử lý...' : 'Đăng ký ngay'}
                    </Button>
                  )}

                  {/* Course Features */}
                  <div className="mt-6 space-y-3">
                    <div className="flex items-center text-sm text-gray-600">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      Truy cập trọn đời
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      Chứng chỉ hoàn thành
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      Hỗ trợ từ giáo viên
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        {/* Course Content Tabs */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Course Description */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Mô tả khóa học</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none">
                  <p className="text-gray-700 whitespace-pre-line">{course.description}</p>
                </div>
              </CardContent>
            </Card>

            {/* What You'll Learn */}
            {course.whatYouWillLearn && course.whatYouWillLearn.length > 0 && (
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle>Bạn sẽ học được gì</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {course.whatYouWillLearn.map((item, index) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Course Curriculum */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Nội dung khóa học</CardTitle>
                <CardDescription>
                  {course.curriculum?.length || 0} bài học • {course.duration} giờ học
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {course.curriculum?.map((lesson, index) => (
                    <div key={index} className={`border rounded-lg p-4 ${lesson.isPreview ? 'border-green-200 bg-green-50' : ''}`}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center flex-1">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <span className="text-sm font-medium text-blue-600">{lesson.order}</span>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium text-gray-900">{lesson.title}</h4>
                              {lesson.isPreview ? (
                                <Eye className="h-4 w-4 text-green-600" />
                              ) : (
                                <Lock className="h-4 w-4 text-gray-400" />
                              )}
                            </div>
                            <p className="text-sm text-gray-600">{lesson.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="flex items-center text-sm text-gray-500">
                            <Clock className="h-4 w-4 mr-1" />
                            {lesson.duration} phút
                          </div>
                          {lesson.isPreview ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handlePreviewLesson(lesson)}
                              className="text-green-600 border-green-200 hover:bg-green-50"
                            >
                              <Play className="h-4 w-4 mr-1" />
                              Xem trước
                            </Button>
                          ) : (
                            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                              {isEnrolled ? 'Có sẵn' : 'Cần đăng ký'}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Requirements */}
            {course.requirements && course.requirements.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Yêu cầu</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {course.requirements.map((requirement, index) => (
                      <li key={index} className="flex items-start">
                        <div className="w-2 h-2 bg-gray-400 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                        <span className="text-gray-700">{requirement}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Instructor Info */}
            <Card>
              <CardHeader>
                <CardTitle>Giáo viên</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-gray-300 rounded-full mr-4"></div>
                  <div>
                    <h4 className="font-medium text-gray-900">{course.teacherId.name}</h4>
                    <p className="text-sm text-gray-600">Giáo viên tiếng Anh</p>
                  </div>
                </div>
                {course.teacherId.profile?.bio && (
                  <p className="text-sm text-gray-700">{course.teacherId.profile.bio}</p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Preview Dialog */}
      <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Xem trước: {previewLesson?.title}</DialogTitle>
            <DialogDescription>
              Đây là bài học miễn phí để bạn trải nghiệm khóa học
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {/* Video Player Placeholder */}
            <div className="aspect-video bg-gray-900 rounded-lg flex items-center justify-center">
              <div className="text-center text-white">
                <Play className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">Video Player</p>
                <p className="text-sm opacity-75">
                  {previewLesson?.videoUrl ? 'Đang tải video...' : 'Video sẽ được tích hợp sau'}
                </p>
              </div>
            </div>

            {/* Lesson Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">{previewLesson?.title}</h3>
              <p className="text-gray-600 mb-3">{previewLesson?.description}</p>
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  {previewLesson?.duration} phút
                </div>
                <div className="flex items-center">
                  <Eye className="h-4 w-4 mr-1" />
                  Xem trước miễn phí
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-blue-900">Thích nội dung này?</h4>
                  <p className="text-sm text-blue-700">Đăng ký khóa học để truy cập toàn bộ {course?.curriculum?.length} bài học</p>
                </div>
                <Button onClick={() => {
                  setShowPreviewDialog(false);
                  // Scroll to enrollment section
                  document.querySelector('[data-enrollment-section]')?.scrollIntoView({ behavior: 'smooth' });
                }}>
                  Đăng ký ngay
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
