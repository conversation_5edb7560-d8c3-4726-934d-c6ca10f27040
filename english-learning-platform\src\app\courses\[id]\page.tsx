import { notFound } from "next/navigation";
import { Star, Clock, Users, BookOpen, Play, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Header from "@/components/layout/Header";
import { formatPrice } from "@/lib/utils";

// Mock data for course detail
const mockCourse = {
  id: "1",
  title: "Tiếng Anh Cơ Bản cho Người Mới <PERSON>",
  description: `<PERSON>h<PERSON>a học này được thiết kế dành riêng cho những người chưa có kiến thức về tiếng Anh hoặc có kiến thức rất cơ bản. 
  
  Bạn sẽ học được:
  • Bảng chữ cái và cách phát âm chuẩn
  • Từ vựng cơ bản trong cuộc sống hàng ngày
  • <PERSON><PERSON> pháp cơ bản và cách sử dụng
  • <PERSON><PERSON> năng giao tiếp đơn giản
  • <PERSON><PERSON><PERSON> đọc và viết những câu đơn giản
  
  Phương pháp giảng dạy:
  • Học qua hình ảnh và âm thanh sinh động
  • Thực hành qua các tình huống thực tế
  • Bài tập tương tác và trò chơi học tập
  • Hỗ trợ 1-1 từ giáo viên`,
  shortDescription: "Học tiếng Anh từ con số 0 với phương pháp dễ hiểu và thực tế.",
  category: "comprehensive",
  level: "beginner",
  price: 299000,
  originalPrice: 399000,
  duration: 40,
  thumbnail: "/api/placeholder/800/400",
  videoIntro: "/api/placeholder/video",
  teacher: {
    name: "Cô Minh Anh",
    avatar: "/api/placeholder/80/80",
    bio: "Giáo viên tiếng Anh với 8 năm kinh nghiệm, tốt nghiệp Đại học Ngoại ngữ Hà Nội. Chuyên gia trong việc giảng dạy tiếng Anh cho người mới bắt đầu.",
    experience: "8 năm kinh nghiệm",
    students: 5000
  },
  averageRating: 4.8,
  totalStudents: 1250,
  totalLessons: 25,
  curriculum: [
    {
      title: "Giới thiệu và Bảng chữ cái",
      lessons: [
        { title: "Chào hỏi cơ bản", duration: 15, isPreview: true },
        { title: "Bảng chữ cái A-M", duration: 20, isPreview: false },
        { title: "Bảng chữ cái N-Z", duration: 20, isPreview: false },
        { title: "Phát âm cơ bản", duration: 25, isPreview: false }
      ]
    },
    {
      title: "Từ vựng cơ bản",
      lessons: [
        { title: "Số đếm 1-100", duration: 18, isPreview: false },
        { title: "Màu sắc và hình dạng", duration: 22, isPreview: false },
        { title: "Gia đình và người thân", duration: 25, isPreview: false },
        { title: "Đồ vật trong nhà", duration: 20, isPreview: false }
      ]
    },
    {
      title: "Ngữ pháp cơ bản",
      lessons: [
        { title: "Động từ TO BE", duration: 30, isPreview: false },
        { title: "Câu khẳng định và phủ định", duration: 25, isPreview: false },
        { title: "Câu hỏi Yes/No", duration: 28, isPreview: false },
        { title: "Câu hỏi WH-", duration: 32, isPreview: false }
      ]
    }
  ],
  ratings: [
    {
      user: "Nguyễn Văn A",
      rating: 5,
      comment: "Khóa học rất hay, giáo viên giảng dễ hiểu. Tôi đã học được rất nhiều kiến thức bổ ích.",
      date: "2024-01-15"
    },
    {
      user: "Trần Thị B",
      rating: 5,
      comment: "Phương pháp giảng dạy rất thú vị, không hề nhàm chán. Recommend cho mọi người!",
      date: "2024-01-10"
    },
    {
      user: "Lê Văn C",
      rating: 4,
      comment: "Khóa học tốt, nội dung phong phú. Chỉ mong có thêm nhiều bài tập thực hành hơn.",
      date: "2024-01-05"
    }
  ]
};

interface CourseDetailPageProps {
  params: {
    id: string;
  };
}

export default async function CourseDetailPage({ params }: CourseDetailPageProps) {
  const { id } = await params;

  // In a real app, you would fetch the course data based on the ID
  if (id !== "1") {
    notFound();
  }

  const course = mockCourse;

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Course Header */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <div className="flex items-center gap-2 mb-4">
                <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                  {course.level === 'beginner' ? 'Cơ bản' : 
                   course.level === 'intermediate' ? 'Trung cấp' : 'Nâng cao'}
                </span>
                <span className="text-xs font-medium text-green-600 bg-green-50 px-2 py-1 rounded">
                  {course.category === 'comprehensive' ? 'Tổng hợp' : 
                   course.category === 'listening' ? 'Nghe' :
                   course.category === 'speaking' ? 'Nói' :
                   course.category === 'reading' ? 'Đọc' : 'Viết'}
                </span>
              </div>
              
              <h1 className="text-3xl font-bold text-gray-900 mb-4">{course.title}</h1>
              <p className="text-lg text-gray-600 mb-6">{course.shortDescription}</p>
              
              <div className="flex items-center gap-6 text-sm text-gray-500">
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
                  <span className="font-medium text-gray-900">{course.averageRating}</span>
                  <span className="ml-1">({course.totalStudents} đánh giá)</span>
                </div>
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-1" />
                  {course.totalStudents} học viên
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  {course.duration} giờ
                </div>
                <div className="flex items-center">
                  <BookOpen className="h-4 w-4 mr-1" />
                  {course.totalLessons} bài học
                </div>
              </div>
            </div>

            {/* Course Video */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <div className="aspect-video bg-gray-900 rounded-lg flex items-center justify-center">
                <Button size="lg" className="bg-white/20 hover:bg-white/30">
                  <Play className="h-8 w-8 mr-2" />
                  Xem video giới thiệu
                </Button>
              </div>
            </div>

            {/* Course Description */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Mô tả khóa học</h2>
              <div className="prose max-w-none">
                <p className="whitespace-pre-line text-gray-700">{course.description}</p>
              </div>
            </div>

            {/* Curriculum */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Nội dung khóa học</h2>
              <div className="space-y-4">
                {course.curriculum.map((section, sectionIndex) => (
                  <div key={sectionIndex} className="border border-gray-200 rounded-lg">
                    <div className="p-4 bg-gray-50 border-b border-gray-200">
                      <h3 className="font-semibold text-gray-900">{section.title}</h3>
                      <p className="text-sm text-gray-500">{section.lessons.length} bài học</p>
                    </div>
                    <div className="divide-y divide-gray-200">
                      {section.lessons.map((lesson, lessonIndex) => (
                        <div key={lessonIndex} className="p-4 flex items-center justify-between">
                          <div className="flex items-center">
                            <Play className="h-4 w-4 text-gray-400 mr-3" />
                            <div>
                              <p className="font-medium text-gray-900">{lesson.title}</p>
                              <p className="text-sm text-gray-500">{lesson.duration} phút</p>
                            </div>
                          </div>
                          {lesson.isPreview && (
                            <Button variant="outline" size="sm">
                              Xem trước
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Instructor */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Giáo viên</h2>
              <div className="flex items-start gap-4">
                <div className="w-20 h-20 bg-gray-300 rounded-full flex-shrink-0"></div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">{course.teacher.name}</h3>
                  <p className="text-gray-600 mb-2">{course.teacher.experience} • {course.teacher.students} học viên</p>
                  <p className="text-gray-700">{course.teacher.bio}</p>
                </div>
              </div>
            </div>

            {/* Reviews */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Đánh giá từ học viên</h2>
              <div className="space-y-4">
                {course.ratings.map((rating, index) => (
                  <div key={index} className="border-b border-gray-200 pb-4 last:border-b-0">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < rating.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="font-medium text-gray-900">{rating.user}</span>
                      <span className="text-sm text-gray-500">{rating.date}</span>
                    </div>
                    <p className="text-gray-700">{rating.comment}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-3xl font-bold text-blue-600">
                        {formatPrice(course.price)}
                      </div>
                      {course.originalPrice && (
                        <div className="text-lg text-gray-500 line-through">
                          {formatPrice(course.originalPrice)}
                        </div>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-green-600 font-medium">
                        Giảm {Math.round(((course.originalPrice! - course.price) / course.originalPrice!) * 100)}%
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button className="w-full" size="lg">
                    Đăng ký ngay
                  </Button>
                  <Button variant="outline" className="w-full">
                    Thêm vào giỏ hàng
                  </Button>
                  
                  <div className="text-center text-sm text-gray-500">
                    Đảm bảo hoàn tiền trong 30 ngày
                  </div>
                  
                  <div className="space-y-3 pt-4 border-t border-gray-200">
                    <h3 className="font-semibold text-gray-900">Khóa học bao gồm:</h3>
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        {course.duration} giờ video học
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        {course.totalLessons} bài học
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        Tài liệu học tập
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        Chứng chỉ hoàn thành
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        Truy cập trọn đời
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        Hỗ trợ từ giáo viên
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
