import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Test from '@/models/Test';
import { getAuthUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const type = searchParams.get('type');
    const level = searchParams.get('level');
    const difficulty = searchParams.get('difficulty');
    const search = searchParams.get('search');

    // Build filter object
    const filter: any = { isPublished: true };

    if (type && type !== 'all') {
      filter.type = type;
    }

    if (level && level !== 'all') {
      filter.level = level;
    }

    if (difficulty && difficulty !== 'all') {
      filter.difficulty = difficulty;
    }

    if (search) {
      filter.$text = { $search: search };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get tests with pagination
    const tests = await Test.find(filter)
      .populate('createdBy', 'name email')
      .select('-questions') // Don't include questions in list view
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const total = await Test.countDocuments(filter);
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: tests,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Get tests error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi tải danh sách bài kiểm tra',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    // Check authentication and role
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized - Vui lòng đăng nhập'
      }, { status: 401 });
    }

    if (user.role !== 'teacher' && user.role !== 'admin') {
      return NextResponse.json({
        success: false,
        message: 'Forbidden - Chỉ giáo viên và admin mới có thể tạo bài kiểm tra'
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      title,
      description,
      type,
      level,
      duration,
      passingScore,
      instructions,
      questions,
      tags,
      difficulty
    } = body;

    // Validation
    const errors: Record<string, string> = {};

    if (!title || title.trim().length < 5) {
      errors.title = 'Tiêu đề phải có ít nhất 5 ký tự';
    }

    if (!description || description.trim().length < 10) {
      errors.description = 'Mô tả phải có ít nhất 10 ký tự';
    }

    if (!type) {
      errors.type = 'Loại bài kiểm tra là bắt buộc';
    }

    if (!level) {
      errors.level = 'Trình độ là bắt buộc';
    }

    if (!duration || duration < 5 || duration > 300) {
      errors.duration = 'Thời lượng phải từ 5 đến 300 phút';
    }

    if (!passingScore || passingScore < 0 || passingScore > 100) {
      errors.passingScore = 'Điểm đạt phải từ 0 đến 100%';
    }

    if (!instructions || instructions.trim().length < 10) {
      errors.instructions = 'Hướng dẫn phải có ít nhất 10 ký tự';
    }

    if (!questions || !Array.isArray(questions) || questions.length === 0) {
      errors.questions = 'Phải có ít nhất 1 câu hỏi';
    }

    if (Object.keys(errors).length > 0) {
      return NextResponse.json({
        success: false,
        message: 'Dữ liệu không hợp lệ',
        errors
      }, { status: 400 });
    }

    // Validate questions
    const questionErrors: string[] = [];
    questions.forEach((question: any, index: number) => {
      if (!question.question || question.question.trim().length < 5) {
        questionErrors.push(`Câu ${index + 1}: Nội dung câu hỏi quá ngắn`);
      }

      if (!question.type) {
        questionErrors.push(`Câu ${index + 1}: Loại câu hỏi là bắt buộc`);
      }

      if (!question.correctAnswer) {
        questionErrors.push(`Câu ${index + 1}: Đáp án đúng là bắt buộc`);
      }

      if (!question.points || question.points <= 0) {
        questionErrors.push(`Câu ${index + 1}: Điểm số phải lớn hơn 0`);
      }

      if (question.type === 'multiple_choice' && (!question.options || question.options.length < 2)) {
        questionErrors.push(`Câu ${index + 1}: Câu trắc nghiệm phải có ít nhất 2 lựa chọn`);
      }
    });

    if (questionErrors.length > 0) {
      return NextResponse.json({
        success: false,
        message: 'Câu hỏi không hợp lệ',
        errors: { questions: questionErrors }
      }, { status: 400 });
    }

    // Create test
    const test = new Test({
      title: title.trim(),
      description: description.trim(),
      type,
      level,
      duration,
      passingScore,
      instructions: instructions.trim(),
      questions: questions.map((q: any, index: number) => ({
        ...q,
        order: index + 1
      })),
      createdBy: user.userId,
      tags: tags || [],
      difficulty: difficulty || 'medium',
      isPublished: false // Default to draft
    });

    await test.save();

    // Populate creator info
    await test.populate('createdBy', 'name email');

    return NextResponse.json({
      success: true,
      message: 'Tạo bài kiểm tra thành công',
      data: test
    }, { status: 201 });

  } catch (error) {
    console.error('Create test error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi khi tạo bài kiểm tra',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
