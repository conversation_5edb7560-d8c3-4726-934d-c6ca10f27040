import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';

export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return authResult.error;
    }

    const { user } = authResult;
    const body = await request.json();
    
    await connectDB();

    // Validate input
    const { name, phone, dateOfBirth, bio } = body;

    if (!name || name.trim().length < 2) {
      return NextResponse.json({
        success: false,
        message: 'Tên phải có ít nhất 2 ký tự'
      }, { status: 400 });
    }

    // Update user profile
    const updateData: any = {
      name: name.trim(),
      phone: phone?.trim() || undefined,
      dateOfBirth: dateOfBirth || undefined,
      updatedAt: new Date()
    };

    // Update profile bio
    if (bio !== undefined) {
      updateData['profile.bio'] = bio.trim();
    }

    const updatedUser = await User.findByIdAndUpdate(
      user.userId,
      { $set: updateData },
      { new: true, runValidators: true }
    ).select('-password');

    if (!updatedUser) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy người dùng'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'Cập nhật thông tin thành công',
      data: updatedUser
    });

  } catch (error) {
    console.error('Update profile error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi hệ thống, vui lòng thử lại sau',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
