{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI!;\n\nif (!MONGODB_URI) {\n  throw new Error('<PERSON>ui lòng định nghĩa biến môi trường MONGODB_URI trong .env.local');\n}\n\n/**\n * Global được sử dụng để duy trì kết nối cached trong môi trường development.\n * Điều này ngăn chặn việc tạo quá nhiều kết nối trong quá trình hot reloading.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Kết nối MongoDB thành công');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    console.error('❌ Lỗi kết nối MongoDB:', e);\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;CAGC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/types/index.ts"], "sourcesContent": ["// Enum types\nexport enum UserRole {\n  ADMIN = 'admin',\n  TEACHER = 'teacher',\n  STUDENT = 'student'\n}\n\nexport enum CourseCategory {\n  LISTENING = 'listening',\n  SPEAKING = 'speaking',\n  READING = 'reading',\n  WRITING = 'writing',\n  COMPREHENSIVE = 'comprehensive'\n}\n\nexport enum CourseLevel {\n  BEGINNER = 'beginner',\n  INTERMEDIATE = 'intermediate',\n  ADVANCED = 'advanced'\n}\n\nexport enum TestType {\n  VOCABULARY = 'vocabulary',\n  GRAMMAR = 'grammar',\n  LISTENING = 'listening',\n  SPEAKING = 'speaking',\n  READING = 'reading',\n  WRITING = 'writing',\n  COMPREHENSIVE = 'comprehensive',\n  PRACTICE = 'practice'\n}\n\nexport enum QuestionType {\n  MULTIPLE_CHOICE = 'multiple_choice',\n  FILL_IN_BLANK = 'fill_in_blank',\n  TRUE_FALSE = 'true_false',\n  ESSAY = 'essay',\n  AUDIO_RESPONSE = 'audio_response',\n  DRAG_DROP = 'drag_drop',\n  MATCHING = 'matching'\n}\n\nexport enum PaymentStatus {\n  PENDING = 'pending',\n  COMPLETED = 'completed',\n  FAILED = 'failed'\n}\n\n// User types\nexport interface User {\n  _id: string;\n  email: string;\n  name: string;\n  role: UserRole;\n  avatar?: string;\n  phone?: string;\n  dateOfBirth?: Date;\n  isEmailVerified: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n  profile?: UserProfile;\n}\n\nexport interface UserProfile {\n  bio?: string;\n  experience?: string; // for teachers\n  education?: string;\n  skills?: string[];\n}\n\n// Course types\nexport interface Course {\n  _id: string;\n  title: string;\n  description: string;\n  shortDescription: string;\n  teacherId: string;\n  teacher?: User;\n  category: CourseCategory;\n  level: CourseLevel;\n  price: number;\n  duration: number; // in hours\n  thumbnail?: string;\n  videoIntro?: string;\n  isPublished: boolean;\n  curriculum: Lesson[];\n  ratings: Rating[];\n  averageRating: number;\n  totalStudents: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Lesson {\n  title: string;\n  description: string;\n  videoUrl?: string;\n  materials: string[];\n  duration: number; // in minutes\n}\n\nexport interface Rating {\n  userId: string;\n  user?: User;\n  rating: number;\n  comment: string;\n  createdAt: Date;\n}\n\n// Enrollment types\nexport interface Enrollment {\n  _id: string;\n  studentId: string;\n  student?: User;\n  courseId: string;\n  course?: Course;\n  enrolledAt: Date;\n  progress: number; // percentage\n  completedLessons: number[];\n  lastAccessedAt: Date;\n  paymentStatus: PaymentStatus;\n  paymentId?: string;\n}\n\n// Test types\nexport interface Test {\n  _id: string;\n  title: string;\n  description: string;\n  type: TestType;\n  level: CourseLevel;\n  duration: number; // in minutes\n  totalQuestions: number;\n  passingScore: number;\n  questions: Question[];\n  createdBy: string;\n  creator?: User;\n  isPublished: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Question {\n  type: QuestionType;\n  question: string;\n  options?: string[]; // for multiple choice\n  correctAnswer: string;\n  points: number;\n  audioUrl?: string; // for listening questions\n  imageUrl?: string;\n}\n\nexport interface TestResult {\n  _id: string;\n  studentId: string;\n  student?: User;\n  testId: string;\n  test?: Test;\n  answers: Answer[];\n  totalScore: number;\n  percentage: number;\n  timeSpent: number; // in minutes\n  startedAt: Date;\n  completedAt: Date;\n  feedback?: string;\n}\n\nexport interface Answer {\n  questionIndex: number;\n  answer: string;\n  isCorrect: boolean;\n  points: number;\n}\n\n// API Response types\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\n// Form types\nexport interface LoginForm {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterForm {\n  name: string;\n  email: string;\n  password: string;\n  confirmPassword: string;\n  role?: UserRole;\n}\n\nexport interface CourseForm {\n  title: string;\n  description: string;\n  shortDescription: string;\n  category: CourseCategory;\n  level: CourseLevel;\n  price: number;\n  duration: number;\n  thumbnail?: string;\n  videoIntro?: string;\n  curriculum: Lesson[];\n}\n\n// Dashboard types\nexport interface DashboardStats {\n  totalCourses: number;\n  totalStudents: number;\n  totalRevenue: number;\n  totalTests: number;\n}\n\nexport interface StudentDashboard {\n  enrolledCourses: Course[];\n  recentTests: TestResult[];\n  progress: {\n    courseId: string;\n    courseName: string;\n    progress: number;\n  }[];\n  stats: {\n    totalCourses: number;\n    completedCourses: number;\n    averageScore: number;\n    totalTestsTaken: number;\n  };\n}\n"], "names": [], "mappings": "AAAA,aAAa;;;;;;;;;AACN,IAAA,AAAK,kCAAA;;;;WAAA;;AAML,IAAA,AAAK,wCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,qCAAA;;;;WAAA;;AAML,IAAA,AAAK,kCAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,sCAAA;;;;;;;;WAAA;;AAUL,IAAA,AAAK,uCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/models/Course.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { CourseCategory, CourseLevel } from '@/types';\n\nexport interface ICourse extends Document {\n  title: string;\n  description: string;\n  shortDescription: string;\n  teacherId: mongoose.Types.ObjectId;\n  category: CourseCategory;\n  level: CourseLevel;\n  price: number;\n  duration: number;\n  thumbnail?: string;\n  videoIntro?: string;\n  isPublished: boolean;\n  curriculum: {\n    _id?: mongoose.Types.ObjectId;\n    title: string;\n    description: string;\n    videoUrl?: string;\n    materials: string[];\n    duration: number;\n    order: number;\n    isPreview: boolean;\n    transcript?: string;\n    objectives?: string[];\n    quiz?: {\n      questions: Array<{\n        question: string;\n        options: string[];\n        correctAnswer: string;\n        explanation?: string;\n      }>;\n    };\n  }[];\n  ratings: {\n    userId: mongoose.Types.ObjectId;\n    rating: number;\n    comment: string;\n    createdAt: Date;\n  }[];\n  averageRating: number;\n  totalStudents: number;\n  tags?: string[];\n  requirements?: string[];\n  whatYouWillLearn?: string[];\n  language: string;\n  subtitles?: string[];\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst QuizQuestionSchema = new Schema({\n  question: {\n    type: String,\n    required: [true, 'Câu hỏi là bắt buộc'],\n    maxlength: [500, 'Câu hỏi không được vượt quá 500 ký tự']\n  },\n  options: [{\n    type: String,\n    required: [true, 'Lựa chọn là bắt buộc'],\n    maxlength: [200, 'Lựa chọn không được vượt quá 200 ký tự']\n  }],\n  correctAnswer: {\n    type: String,\n    required: [true, 'Đáp án đúng là bắt buộc']\n  },\n  explanation: {\n    type: String,\n    maxlength: [300, 'Giải thích không được vượt quá 300 ký tự']\n  }\n});\n\nconst LessonSchema = new Schema({\n  title: {\n    type: String,\n    required: [true, 'Tiêu đề bài học là bắt buộc'],\n    trim: true,\n    maxlength: [200, 'Tiêu đề bài học không được vượt quá 200 ký tự']\n  },\n  description: {\n    type: String,\n    required: [true, 'Mô tả bài học là bắt buộc'],\n    maxlength: [1000, 'Mô tả bài học không được vượt quá 1000 ký tự']\n  },\n  videoUrl: {\n    type: String,\n    validate: {\n      validator: function(v: string) {\n        return !v || /^https?:\\/\\/.+/.test(v);\n      },\n      message: 'URL video không hợp lệ'\n    }\n  },\n  materials: [{\n    type: String,\n    validate: {\n      validator: function(v: string) {\n        return /^https?:\\/\\/.+/.test(v);\n      },\n      message: 'URL tài liệu không hợp lệ'\n    }\n  }],\n  duration: {\n    type: Number,\n    required: [true, 'Thời lượng bài học là bắt buộc'],\n    min: [1, 'Thời lượng bài học phải ít nhất 1 phút']\n  },\n  order: {\n    type: Number,\n    required: [true, 'Thứ tự bài học là bắt buộc'],\n    min: [1, 'Thứ tự bài học phải bắt đầu từ 1']\n  },\n  isPreview: {\n    type: Boolean,\n    default: false\n  },\n  transcript: {\n    type: String,\n    maxlength: [10000, 'Transcript không được vượt quá 10000 ký tự']\n  },\n  objectives: [{\n    type: String,\n    maxlength: [200, 'Mục tiêu học tập không được vượt quá 200 ký tự']\n  }],\n  quiz: {\n    questions: [QuizQuestionSchema]\n  }\n});\n\nconst RatingSchema = new Schema({\n  userId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: true\n  },\n  rating: {\n    type: Number,\n    required: [true, 'Đánh giá là bắt buộc'],\n    min: [1, 'Đánh giá phải từ 1 đến 5'],\n    max: [5, 'Đánh giá phải từ 1 đến 5']\n  },\n  comment: {\n    type: String,\n    maxlength: [500, 'Nhận xét không được vượt quá 500 ký tự']\n  },\n  createdAt: {\n    type: Date,\n    default: Date.now\n  }\n});\n\nconst CourseSchema = new Schema<ICourse>({\n  title: {\n    type: String,\n    required: [true, 'Tiêu đề khóa học là bắt buộc'],\n    trim: true,\n    maxlength: [200, 'Tiêu đề khóa học không được vượt quá 200 ký tự']\n  },\n  description: {\n    type: String,\n    required: [true, 'Mô tả khóa học là bắt buộc'],\n    maxlength: [5000, 'Mô tả khóa học không được vượt quá 5000 ký tự']\n  },\n  shortDescription: {\n    type: String,\n    required: [true, 'Mô tả ngắn là bắt buộc'],\n    maxlength: [300, 'Mô tả ngắn không được vượt quá 300 ký tự']\n  },\n  teacherId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: [true, 'Giáo viên là bắt buộc']\n  },\n  category: {\n    type: String,\n    enum: Object.values(CourseCategory),\n    required: [true, 'Danh mục khóa học là bắt buộc']\n  },\n  level: {\n    type: String,\n    enum: Object.values(CourseLevel),\n    required: [true, 'Trình độ khóa học là bắt buộc']\n  },\n  price: {\n    type: Number,\n    required: [true, 'Giá khóa học là bắt buộc'],\n    min: [0, 'Giá khóa học không được âm']\n  },\n  duration: {\n    type: Number,\n    required: [true, 'Thời lượng khóa học là bắt buộc'],\n    min: [1, 'Thời lượng khóa học phải ít nhất 1 giờ']\n  },\n  thumbnail: {\n    type: String,\n    validate: {\n      validator: function(v: string) {\n        return !v || /^https?:\\/\\/.+/.test(v);\n      },\n      message: 'URL hình ảnh không hợp lệ'\n    }\n  },\n  videoIntro: {\n    type: String,\n    validate: {\n      validator: function(v: string) {\n        return !v || /^https?:\\/\\/.+/.test(v);\n      },\n      message: 'URL video giới thiệu không hợp lệ'\n    }\n  },\n  isPublished: {\n    type: Boolean,\n    default: false\n  },\n  curriculum: [LessonSchema],\n  ratings: [RatingSchema],\n  averageRating: {\n    type: Number,\n    default: 0,\n    min: 0,\n    max: 5\n  },\n  totalStudents: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  tags: [{\n    type: String,\n    trim: true,\n    maxlength: [50, 'Tag không được vượt quá 50 ký tự']\n  }],\n  requirements: [{\n    type: String,\n    maxlength: [200, 'Yêu cầu không được vượt quá 200 ký tự']\n  }],\n  whatYouWillLearn: [{\n    type: String,\n    maxlength: [200, 'Mục tiêu học tập không được vượt quá 200 ký tự']\n  }],\n  language: {\n    type: String,\n    default: 'vi',\n    enum: ['vi', 'en', 'both']\n  },\n  subtitles: [{\n    type: String,\n    enum: ['vi', 'en']\n  }]\n}, {\n  timestamps: true\n});\n\n// Indexes cho tìm kiếm và hiệu suất\n// CourseSchema.index({ title: 'text', description: 'text' }, { default_language: 'none' });\nCourseSchema.index({ category: 1, level: 1 });\nCourseSchema.index({ teacherId: 1 });\nCourseSchema.index({ isPublished: 1 });\nCourseSchema.index({ averageRating: -1 });\nCourseSchema.index({ totalStudents: -1 });\nCourseSchema.index({ createdAt: -1 });\n\n// Middleware để tính toán averageRating\nCourseSchema.pre('save', function(next) {\n  if (this.ratings && this.ratings.length > 0) {\n    const totalRating = this.ratings.reduce((sum, rating) => sum + rating.rating, 0);\n    this.averageRating = Math.round((totalRating / this.ratings.length) * 10) / 10;\n  }\n  next();\n});\n\nexport default mongoose.models.Course || mongoose.model<ICourse>('Course', CourseSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAmDA,MAAM,qBAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAsB;QACvC,WAAW;YAAC;YAAK;SAAwC;IAC3D;IACA,SAAS;QAAC;YACR,MAAM;YACN,UAAU;gBAAC;gBAAM;aAAuB;YACxC,WAAW;gBAAC;gBAAK;aAAyC;QAC5D;KAAE;IACF,eAAe;QACb,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;IAC7C;IACA,aAAa;QACX,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;AACF;AAEA,MAAM,eAAe,IAAI,yGAAA,CAAA,SAAM,CAAC;IAC9B,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAA8B;QAC/C,MAAM;QACN,WAAW;YAAC;YAAK;SAAgD;IACnE;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA4B;QAC7C,WAAW;YAAC;YAAM;SAA+C;IACnE;IACA,UAAU;QACR,MAAM;QACN,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,CAAC,KAAK,iBAAiB,IAAI,CAAC;YACrC;YACA,SAAS;QACX;IACF;IACA,WAAW;QAAC;YACV,MAAM;YACN,UAAU;gBACR,WAAW,SAAS,CAAS;oBAC3B,OAAO,iBAAiB,IAAI,CAAC;gBAC/B;gBACA,SAAS;YACX;QACF;KAAE;IACF,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAiC;QAClD,KAAK;YAAC;YAAG;SAAyC;IACpD;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAA6B;QAC9C,KAAK;YAAC;YAAG;SAAmC;IAC9C;IACA,WAAW;QACT,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,WAAW;YAAC;YAAO;SAA6C;IAClE;IACA,YAAY;QAAC;YACX,MAAM;YACN,WAAW;gBAAC;gBAAK;aAAiD;QACpE;KAAE;IACF,MAAM;QACJ,WAAW;YAAC;SAAmB;IACjC;AACF;AAEA,MAAM,eAAe,IAAI,yGAAA,CAAA,SAAM,CAAC;IAC9B,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;IACZ;IACA,QAAQ;QACN,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,KAAK;YAAC;YAAG;SAA2B;QACpC,KAAK;YAAC;YAAG;SAA2B;IACtC;IACA,SAAS;QACP,MAAM;QACN,WAAW;YAAC;YAAK;SAAyC;IAC5D;IACA,WAAW;QACT,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;AACF;AAEA,MAAM,eAAe,IAAI,yGAAA,CAAA,SAAM,CAAU;IACvC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAA+B;QAChD,MAAM;QACN,WAAW;YAAC;YAAK;SAAiD;IACpE;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA6B;QAC9C,WAAW;YAAC;YAAM;SAAgD;IACpE;IACA,kBAAkB;QAChB,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,WAAW;QACT,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;YAAC;YAAM;SAAwB;IAC3C;IACA,UAAU;QACR,MAAM;QACN,MAAM,OAAO,MAAM,CAAC,uHAAA,CAAA,iBAAc;QAClC,UAAU;YAAC;YAAM;SAAgC;IACnD;IACA,OAAO;QACL,MAAM;QACN,MAAM,OAAO,MAAM,CAAC,uHAAA,CAAA,cAAW;QAC/B,UAAU;YAAC;YAAM;SAAgC;IACnD;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,KAAK;YAAC;YAAG;SAA6B;IACxC;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAkC;QACnD,KAAK;YAAC;YAAG;SAAyC;IACpD;IACA,WAAW;QACT,MAAM;QACN,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,CAAC,KAAK,iBAAiB,IAAI,CAAC;YACrC;YACA,SAAS;QACX;IACF;IACA,YAAY;QACV,MAAM;QACN,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,CAAC,KAAK,iBAAiB,IAAI,CAAC;YACrC;YACA,SAAS;QACX;IACF;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,YAAY;QAAC;KAAa;IAC1B,SAAS;QAAC;KAAa;IACvB,eAAe;QACb,MAAM;QACN,SAAS;QACT,KAAK;QACL,KAAK;IACP;IACA,eAAe;QACb,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;gBAAC;gBAAI;aAAmC;QACrD;KAAE;IACF,cAAc;QAAC;YACb,MAAM;YACN,WAAW;gBAAC;gBAAK;aAAwC;QAC3D;KAAE;IACF,kBAAkB;QAAC;YACjB,MAAM;YACN,WAAW;gBAAC;gBAAK;aAAiD;QACpE;KAAE;IACF,UAAU;QACR,MAAM;QACN,SAAS;QACT,MAAM;YAAC;YAAM;YAAM;SAAO;IAC5B;IACA,WAAW;QAAC;YACV,MAAM;YACN,MAAM;gBAAC;gBAAM;aAAK;QACpB;KAAE;AACJ,GAAG;IACD,YAAY;AACd;AAEA,oCAAoC;AACpC,4FAA4F;AAC5F,aAAa,KAAK,CAAC;IAAE,UAAU;IAAG,OAAO;AAAE;AAC3C,aAAa,KAAK,CAAC;IAAE,WAAW;AAAE;AAClC,aAAa,KAAK,CAAC;IAAE,aAAa;AAAE;AACpC,aAAa,KAAK,CAAC;IAAE,eAAe,CAAC;AAAE;AACvC,aAAa,KAAK,CAAC;IAAE,eAAe,CAAC;AAAE;AACvC,aAAa,KAAK,CAAC;IAAE,WAAW,CAAC;AAAE;AAEnC,wCAAwC;AACxC,aAAa,GAAG,CAAC,QAAQ,SAAS,IAAI;IACpC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG;QAC3C,MAAM,cAAc,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE;QAC9E,IAAI,CAAC,aAAa,GAAG,KAAK,KAAK,CAAC,AAAC,cAAc,IAAI,CAAC,OAAO,CAAC,MAAM,GAAI,MAAM;IAC9E;IACA;AACF;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAU,UAAU", "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/models/Enrollment.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { PaymentStatus } from '@/types';\n\nexport interface IBookmark {\n  timestamp: number; // video timestamp in seconds\n  note: string;\n  createdAt: Date;\n}\n\nexport interface ILessonProgress {\n  lessonId: string;\n  completedAt?: Date;\n  timeSpent: number; // in seconds\n  videoProgress: number; // percentage watched (0-100)\n  quizScore?: number;\n  notes?: string;\n  bookmarks?: IBookmark[];\n  isCompleted: boolean;\n}\n\nexport interface IEnrollment extends Document {\n  studentId: mongoose.Types.ObjectId;\n  courseId: mongoose.Types.ObjectId;\n  enrolledAt: Date;\n  completedAt?: Date;\n  progress: number; // percentage (0-100)\n  completedLessons: string[]; // lesson IDs\n  lessonProgress: ILessonProgress[];\n  lastAccessedAt: Date;\n  paymentStatus: PaymentStatus;\n  paymentId?: string;\n  paymentAmount?: number;\n  certificateIssued: boolean;\n  certificateId?: string;\n  totalTimeSpent: number; // in seconds\n  currentLesson?: string; // current lesson ID\n  rating?: number; // 1-5 stars\n  review?: string;\n  reviewedAt?: Date;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst BookmarkSchema = new Schema({\n  timestamp: {\n    type: Number,\n    required: [true, 'Timestamp là bắt buộc'],\n    min: [0, 'Timestamp không được âm']\n  },\n  note: {\n    type: String,\n    required: [true, 'Ghi chú là bắt buộc'],\n    maxlength: [500, 'Ghi chú không được vượt quá 500 ký tự']\n  },\n  createdAt: {\n    type: Date,\n    default: Date.now\n  }\n});\n\nconst LessonProgressSchema = new Schema({\n  lessonId: {\n    type: String,\n    required: [true, 'ID bài học là bắt buộc']\n  },\n  completedAt: {\n    type: Date\n  },\n  timeSpent: {\n    type: Number,\n    default: 0,\n    min: [0, 'Thời gian không được âm']\n  },\n  videoProgress: {\n    type: Number,\n    default: 0,\n    min: [0, 'Tiến độ video không được âm'],\n    max: [100, 'Tiến độ video không được vượt quá 100%']\n  },\n  quizScore: {\n    type: Number,\n    min: [0, 'Điểm quiz không được âm'],\n    max: [100, 'Điểm quiz không được vượt quá 100%']\n  },\n  notes: {\n    type: String,\n    maxlength: [2000, 'Ghi chú không được vượt quá 2000 ký tự']\n  },\n  bookmarks: [BookmarkSchema],\n  isCompleted: {\n    type: Boolean,\n    default: false\n  }\n});\n\nconst EnrollmentSchema = new Schema<IEnrollment>({\n  studentId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: [true, 'ID học viên là bắt buộc']\n  },\n  courseId: {\n    type: Schema.Types.ObjectId,\n    ref: 'Course',\n    required: [true, 'ID khóa học là bắt buộc']\n  },\n  enrolledAt: {\n    type: Date,\n    default: Date.now\n  },\n  completedAt: {\n    type: Date\n  },\n  progress: {\n    type: Number,\n    default: 0,\n    min: [0, 'Tiến độ không được âm'],\n    max: [100, 'Tiến độ không được vượt quá 100%']\n  },\n  completedLessons: [{\n    type: String\n  }],\n  lessonProgress: [LessonProgressSchema],\n  lastAccessedAt: {\n    type: Date,\n    default: Date.now\n  },\n  paymentStatus: {\n    type: String,\n    enum: Object.values(PaymentStatus),\n    default: PaymentStatus.PENDING\n  },\n  paymentId: {\n    type: String,\n    sparse: true\n  },\n  paymentAmount: {\n    type: Number,\n    min: [0, 'Số tiền thanh toán không được âm']\n  },\n  certificateIssued: {\n    type: Boolean,\n    default: false\n  },\n  certificateId: {\n    type: String,\n    sparse: true\n  },\n  totalTimeSpent: {\n    type: Number,\n    default: 0,\n    min: [0, 'Tổng thời gian học không được âm']\n  },\n  currentLesson: {\n    type: String\n  },\n  rating: {\n    type: Number,\n    min: [1, 'Đánh giá tối thiểu là 1 sao'],\n    max: [5, 'Đánh giá tối đa là 5 sao']\n  },\n  review: {\n    type: String,\n    maxlength: [1000, 'Đánh giá không được vượt quá 1000 ký tự']\n  },\n  reviewedAt: {\n    type: Date\n  }\n}, {\n  timestamps: true\n});\n\n// Compound index để đảm bảo một học viên chỉ đăng ký một khóa học một lần\nEnrollmentSchema.index({ studentId: 1, courseId: 1 }, { unique: true });\n\n// Indexes khác\nEnrollmentSchema.index({ studentId: 1 });\nEnrollmentSchema.index({ courseId: 1 });\nEnrollmentSchema.index({ paymentStatus: 1 });\nEnrollmentSchema.index({ enrolledAt: -1 });\n\nexport default mongoose.models.Enrollment || mongoose.model<IEnrollment>('Enrollment', EnrollmentSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA0CA,MAAM,iBAAiB,IAAI,yGAAA,CAAA,SAAM,CAAC;IAChC,WAAW;QACT,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,KAAK;YAAC;YAAG;SAA0B;IACrC;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAsB;QACvC,WAAW;YAAC;YAAK;SAAwC;IAC3D;IACA,WAAW;QACT,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;AACF;AAEA,MAAM,uBAAuB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACtC,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;IAC5C;IACA,aAAa;QACX,MAAM;IACR;IACA,WAAW;QACT,MAAM;QACN,SAAS;QACT,KAAK;YAAC;YAAG;SAA0B;IACrC;IACA,eAAe;QACb,MAAM;QACN,SAAS;QACT,KAAK;YAAC;YAAG;SAA8B;QACvC,KAAK;YAAC;YAAK;SAAyC;IACtD;IACA,WAAW;QACT,MAAM;QACN,KAAK;YAAC;YAAG;SAA0B;QACnC,KAAK;YAAC;YAAK;SAAqC;IAClD;IACA,OAAO;QACL,MAAM;QACN,WAAW;YAAC;YAAM;SAAyC;IAC7D;IACA,WAAW;QAAC;KAAe;IAC3B,aAAa;QACX,MAAM;QACN,SAAS;IACX;AACF;AAEA,MAAM,mBAAmB,IAAI,yGAAA,CAAA,SAAM,CAAc;IAC/C,WAAW;QACT,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;YAAC;YAAM;SAA0B;IAC7C;IACA,UAAU;QACR,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;YAAC;YAAM;SAA0B;IAC7C;IACA,YAAY;QACV,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,aAAa;QACX,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,SAAS;QACT,KAAK;YAAC;YAAG;SAAwB;QACjC,KAAK;YAAC;YAAK;SAAmC;IAChD;IACA,kBAAkB;QAAC;YACjB,MAAM;QACR;KAAE;IACF,gBAAgB;QAAC;KAAqB;IACtC,gBAAgB;QACd,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,eAAe;QACb,MAAM;QACN,MAAM,OAAO,MAAM,CAAC,uHAAA,CAAA,gBAAa;QACjC,SAAS,uHAAA,CAAA,gBAAa,CAAC,OAAO;IAChC;IACA,WAAW;QACT,MAAM;QACN,QAAQ;IACV;IACA,eAAe;QACb,MAAM;QACN,KAAK;YAAC;YAAG;SAAmC;IAC9C;IACA,mBAAmB;QACjB,MAAM;QACN,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,QAAQ;IACV;IACA,gBAAgB;QACd,MAAM;QACN,SAAS;QACT,KAAK;YAAC;YAAG;SAAmC;IAC9C;IACA,eAAe;QACb,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,KAAK;YAAC;YAAG;SAA8B;QACvC,KAAK;YAAC;YAAG;SAA2B;IACtC;IACA,QAAQ;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAA0C;IAC9D;IACA,YAAY;QACV,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,0EAA0E;AAC1E,iBAAiB,KAAK,CAAC;IAAE,WAAW;IAAG,UAAU;AAAE,GAAG;IAAE,QAAQ;AAAK;AAErE,eAAe;AACf,iBAAiB,KAAK,CAAC;IAAE,WAAW;AAAE;AACtC,iBAAiB,KAAK,CAAC;IAAE,UAAU;AAAE;AACrC,iBAAiB,KAAK,CAAC;IAAE,eAAe;AAAE;AAC1C,iBAAiB,KAAK,CAAC;IAAE,YAAY,CAAC;AAAE;uCAEzB,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,UAAU,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAc,cAAc", "debugId": null}}, {"offset": {"line": 813, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\nimport { UserRole } from '@/types';\n\nexport interface IUser extends Document {\n  email: string;\n  password: string;\n  name: string;\n  role: UserRole;\n  avatar?: string;\n  phone?: string;\n  dateOfBirth?: Date;\n  isEmailVerified: boolean;\n  profile?: {\n    bio?: string;\n    experience?: string;\n    education?: string;\n    skills?: string[];\n  };\n  createdAt: Date;\n  updatedAt: Date;\n  comparePassword(candidatePassword: string): Promise<boolean>;\n}\n\nconst UserSchema = new Schema<IUser>({\n  email: {\n    type: String,\n    required: [true, 'Email là bắt buộc'],\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: [/^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/, 'Email không hợp lệ']\n  },\n  password: {\n    type: String,\n    required: [true, '<PERSON>ật khẩu là bắt buộc'],\n    minlength: [6, 'Mật khẩu phải có ít nhất 6 ký tự']\n  },\n  name: {\n    type: String,\n    required: [true, 'Tên là bắt buộc'],\n    trim: true,\n    maxlength: [100, 'Tên không được vượt quá 100 ký tự']\n  },\n  role: {\n    type: String,\n    enum: Object.values(UserRole),\n    default: UserRole.STUDENT\n  },\n  avatar: {\n    type: String,\n    default: null\n  },\n  phone: {\n    type: String,\n    match: [/^[0-9]{10,11}$/, 'Số điện thoại không hợp lệ']\n  },\n  dateOfBirth: {\n    type: Date\n  },\n  isEmailVerified: {\n    type: Boolean,\n    default: false\n  },\n  profile: {\n    bio: {\n      type: String,\n      maxlength: [500, 'Bio không được vượt quá 500 ký tự']\n    },\n    experience: {\n      type: String,\n      maxlength: [1000, 'Kinh nghiệm không được vượt quá 1000 ký tự']\n    },\n    education: {\n      type: String,\n      maxlength: [500, 'Học vấn không được vượt quá 500 ký tự']\n    },\n    skills: [{\n      type: String,\n      maxlength: [50, 'Kỹ năng không được vượt quá 50 ký tự']\n    }]\n  }\n}, {\n  timestamps: true\n});\n\n// Index cho tìm kiếm\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\n\n// Hash password trước khi lưu\nUserSchema.pre('save', async function(next) {\n  if (!this.isModified('password')) return next();\n  \n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error as Error);\n  }\n});\n\n// Method để so sánh password\nUserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Loại bỏ password khỏi JSON response\nUserSchema.methods.toJSON = function() {\n  const userObject = this.toObject();\n  delete userObject.password;\n  return userObject;\n};\n\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAsBA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAQ;IACnC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;YAAC;YAA+C;SAAqB;IAC9E;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAAmC;IACpD;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAkB;QACnC,MAAM;QACN,WAAW;YAAC;YAAK;SAAoC;IACvD;IACA,MAAM;QACJ,MAAM;QACN,MAAM,OAAO,MAAM,CAAC,uHAAA,CAAA,WAAQ;QAC5B,SAAS,uHAAA,CAAA,WAAQ,CAAC,OAAO;IAC3B;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,OAAO;YAAC;YAAkB;SAA6B;IACzD;IACA,aAAa;QACX,MAAM;IACR;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,SAAS;QACP,KAAK;YACH,MAAM;YACN,WAAW;gBAAC;gBAAK;aAAoC;QACvD;QACA,YAAY;YACV,MAAM;YACN,WAAW;gBAAC;gBAAM;aAA6C;QACjE;QACA,WAAW;YACT,MAAM;YACN,WAAW;gBAAC;gBAAK;aAAwC;QAC3D;QACA,QAAQ;YAAC;gBACP,MAAM;gBACN,WAAW;oBAAC;oBAAI;iBAAuC;YACzD;SAAE;IACJ;AACF,GAAG;IACD,YAAY;AACd;AAEA,qBAAqB;AACrB,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAE3B,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAe,IAAI;IACxC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,6BAA6B;AAC7B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAyB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,sCAAsC;AACtC,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO;AACT;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 953, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport User from '@/models/User';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';\n\nexport interface AuthUser {\n  userId: string;\n  email: string;\n  role: string;\n}\n\nexport async function verifyToken(token: string): Promise<AuthUser | null> {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET) as AuthUser;\n    return decoded;\n  } catch (error) {\n    console.error('Token verification failed:', error);\n    return null;\n  }\n}\n\nexport async function getAuthUser(request: NextRequest): Promise<AuthUser | null> {\n  try {\n    // Try to get token from cookie first\n    let token = request.cookies.get('auth-token')?.value;\n    \n    // If no cookie, try Authorization header\n    if (!token) {\n      const authHeader = request.headers.get('authorization');\n      if (authHeader && authHeader.startsWith('Bearer ')) {\n        token = authHeader.substring(7);\n      }\n    }\n\n    if (!token) {\n      return null;\n    }\n\n    return await verifyToken(token);\n  } catch (error) {\n    console.error('Get auth user failed:', error);\n    return null;\n  }\n}\n\nexport async function requireAuth(request: NextRequest): Promise<{ user: AuthUser } | { error: Response }> {\n  const user = await getAuthUser(request);\n  \n  if (!user) {\n    return {\n      error: new Response(\n        JSON.stringify({\n          success: false,\n          message: 'Unauthorized - Token required'\n        }),\n        { \n          status: 401,\n          headers: { 'Content-Type': 'application/json' }\n        }\n      )\n    };\n  }\n\n  return { user };\n}\n\nexport async function requireRole(request: NextRequest, allowedRoles: string[]): Promise<{ user: AuthUser } | { error: Response }> {\n  const authResult = await requireAuth(request);\n  \n  if ('error' in authResult) {\n    return authResult;\n  }\n\n  if (!allowedRoles.includes(authResult.user.role)) {\n    return {\n      error: new Response(\n        JSON.stringify({\n          success: false,\n          message: 'Forbidden - Insufficient permissions'\n        }),\n        { \n          status: 403,\n          headers: { 'Content-Type': 'application/json' }\n        }\n      )\n    };\n  }\n\n  return authResult;\n}\n\nexport async function getCurrentUser(userId: string) {\n  try {\n    await connectDB();\n    const user = await User.findById(userId).select('-password');\n    return user;\n  } catch (error) {\n    console.error('Get current user failed:', error);\n    return null;\n  }\n}\n\n// Helper function to generate JWT token\nexport function generateToken(payload: { userId: string; email: string; role: string }): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });\n}\n\n// Helper function to hash password (for password reset, etc.)\nexport async function hashPassword(password: string): Promise<string> {\n  const bcrypt = require('bcryptjs');\n  const salt = await bcrypt.genSalt(12);\n  return bcrypt.hash(password, salt);\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;AACA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAQtC,eAAe,YAAY,KAAa;IAC7C,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAEO,eAAe,YAAY,OAAoB;IACpD,IAAI;QACF,qCAAqC;QACrC,IAAI,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAE/C,yCAAyC;QACzC,IAAI,CAAC,OAAO;YACV,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;YACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;gBAClD,QAAQ,WAAW,SAAS,CAAC;YAC/B;QACF;QAEA,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,OAAO,MAAM,YAAY;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF;AAEO,eAAe,YAAY,OAAoB;IACpD,MAAM,OAAO,MAAM,YAAY;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;YACL,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBACb,SAAS;gBACT,SAAS;YACX,IACA;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;IACF;IAEA,OAAO;QAAE;IAAK;AAChB;AAEO,eAAe,YAAY,OAAoB,EAAE,YAAsB;IAC5E,MAAM,aAAa,MAAM,YAAY;IAErC,IAAI,WAAW,YAAY;QACzB,OAAO;IACT;IAEA,IAAI,CAAC,aAAa,QAAQ,CAAC,WAAW,IAAI,CAAC,IAAI,GAAG;QAChD,OAAO;YACL,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBACb,SAAS;gBACT,SAAS;YACX,IACA;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;IACF;IAEA,OAAO;AACT;AAEO,eAAe,eAAe,MAAc;IACjD,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAAQ,MAAM,CAAC;QAChD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAGO,SAAS,cAAc,OAAwD;IACpF,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAGO,eAAe,aAAa,QAAgB;IACjD,MAAM;IACN,MAAM,OAAO,MAAM,OAAO,OAAO,CAAC;IAClC,OAAO,OAAO,IAAI,CAAC,UAAU;AAC/B", "debugId": null}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/app/api/courses/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport Course from '@/models/Course';\nimport Enrollment from '@/models/Enrollment';\nimport { getAuthUser } from '@/lib/auth';\n\ninterface RouteParams {\n  params: {\n    id: string;\n  };\n}\n\nexport async function GET(request: NextRequest, { params }: RouteParams) {\n  try {\n    await connectDB();\n\n    const { id } = params;\n\n    // Find course\n    const course = await Course.findById(id)\n      .populate('teacherId', 'name email profile');\n\n    if (!course) {\n      return NextResponse.json({\n        success: false,\n        message: '<PERSON>hông tìm thấy khóa học'\n      }, { status: 404 });\n    }\n\n    // Check if course is published or user has permission\n    const user = await getAuthUser(request);\n    const canAccess = course.isPublished ||\n                     (user && (user.role === 'admin' || user.userId === course.teacherId._id.toString()));\n\n    if (!canAccess) {\n      return NextResponse.json({\n        success: false,\n        message: '<PERSON><PERSON><PERSON><PERSON> học chưa được xuất bản'\n      }, { status: 403 });\n    }\n\n    // Get enrollment info if user is logged in\n    let enrollmentInfo = null;\n    if (user && user.role === 'student') {\n      enrollmentInfo = await Enrollment.findOne({\n        studentId: user.userId,\n        courseId: id\n      });\n    }\n\n    // Get total enrollment count\n    const totalEnrollments = await Enrollment.countDocuments({ courseId: id });\n\n    // Prepare course data\n    let courseData = course.toJSON();\n    \n    // For non-enrolled students, hide premium content\n    if (user && user.role === 'student' && !enrollmentInfo) {\n      courseData.curriculum = courseData.curriculum.map((lesson: any) => ({\n        ...lesson,\n        videoUrl: lesson.isPreview ? lesson.videoUrl : undefined,\n        materials: lesson.isPreview ? lesson.materials : [],\n        transcript: undefined,\n        quiz: undefined\n      }));\n    }\n\n    // Add enrollment and progress info\n    const response = {\n      ...courseData,\n      totalEnrollments,\n      isEnrolled: !!enrollmentInfo,\n      enrollmentInfo: enrollmentInfo ? {\n        progress: enrollmentInfo.progress,\n        completedLessons: enrollmentInfo.completedLessons,\n        lastAccessedAt: enrollmentInfo.lastAccessedAt,\n        currentLesson: enrollmentInfo.currentLesson,\n        totalTimeSpent: enrollmentInfo.totalTimeSpent,\n        certificateIssued: enrollmentInfo.certificateIssued\n      } : null\n    };\n\n    return NextResponse.json({\n      success: true,\n      data: response\n    });\n\n  } catch (error) {\n    console.error('Get course error:', error);\n    \n    return NextResponse.json({\n      success: false,\n      message: 'Lỗi khi tải khóa học',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n\nexport async function PUT(request: NextRequest, { params }: RouteParams) {\n  try {\n    await connectDB();\n\n    const { id } = params;\n    \n    // Check authentication and permission\n    const user = await getAuthUser(request);\n    if (!user) {\n      return NextResponse.json({\n        success: false,\n        message: 'Unauthorized - Vui lòng đăng nhập'\n      }, { status: 401 });\n    }\n\n    // Find course\n    const course = await Course.findById(id);\n    if (!course) {\n      return NextResponse.json({\n        success: false,\n        message: 'Không tìm thấy khóa học'\n      }, { status: 404 });\n    }\n\n    // Check permission\n    const canEdit = user.role === 'admin' || user.userId === course.teacherId.toString();\n    if (!canEdit) {\n      return NextResponse.json({\n        success: false,\n        message: 'Forbidden - Bạn không có quyền chỉnh sửa khóa học này'\n      }, { status: 403 });\n    }\n\n    const body = await request.json();\n    const {\n      title,\n      description,\n      category,\n      level,\n      price,\n      thumbnail,\n      curriculum,\n      tags,\n      requirements,\n      whatYouWillLearn,\n      language,\n      subtitles,\n      isPublished\n    } = body;\n\n    // Update course\n    const updateData: any = {};\n    \n    if (title !== undefined) updateData.title = title.trim();\n    if (description !== undefined) updateData.description = description.trim();\n    if (category !== undefined) updateData.category = category;\n    if (level !== undefined) updateData.level = level;\n    if (price !== undefined) updateData.price = price;\n    if (thumbnail !== undefined) updateData.thumbnail = thumbnail;\n    if (curriculum !== undefined) {\n      updateData.curriculum = curriculum.map((lesson: any, index: number) => ({\n        ...lesson,\n        order: index + 1\n      }));\n      // Recalculate total duration\n      updateData.totalDuration = curriculum.reduce((sum: number, lesson: any) => sum + lesson.duration, 0);\n    }\n    if (tags !== undefined) updateData.tags = tags;\n    if (requirements !== undefined) updateData.requirements = requirements;\n    if (whatYouWillLearn !== undefined) updateData.whatYouWillLearn = whatYouWillLearn;\n    if (language !== undefined) updateData.language = language;\n    if (subtitles !== undefined) updateData.subtitles = subtitles;\n    if (isPublished !== undefined) updateData.isPublished = isPublished;\n\n    const updatedCourse = await Course.findByIdAndUpdate(\n      id,\n      updateData,\n      { new: true, runValidators: true }\n    ).populate('teacherId', 'name email profile');\n\n    return NextResponse.json({\n      success: true,\n      message: 'Cập nhật khóa học thành công',\n      data: updatedCourse\n    });\n\n  } catch (error) {\n    console.error('Update course error:', error);\n    \n    return NextResponse.json({\n      success: false,\n      message: 'Lỗi khi cập nhật khóa học',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n\nexport async function DELETE(request: NextRequest, { params }: RouteParams) {\n  try {\n    await connectDB();\n\n    const { id } = params;\n    \n    // Check authentication and permission\n    const user = await getAuthUser(request);\n    if (!user) {\n      return NextResponse.json({\n        success: false,\n        message: 'Unauthorized - Vui lòng đăng nhập'\n      }, { status: 401 });\n    }\n\n    // Find course\n    const course = await Course.findById(id);\n    if (!course) {\n      return NextResponse.json({\n        success: false,\n        message: 'Không tìm thấy khóa học'\n      }, { status: 404 });\n    }\n\n    // Check permission\n    const canDelete = user.role === 'admin' || user.userId === course.teacherId.toString();\n    if (!canDelete) {\n      return NextResponse.json({\n        success: false,\n        message: 'Forbidden - Bạn không có quyền xóa khóa học này'\n      }, { status: 403 });\n    }\n\n    // Check if course has enrollments\n    const enrollmentCount = await Enrollment.countDocuments({ courseId: id });\n    if (enrollmentCount > 0) {\n      return NextResponse.json({\n        success: false,\n        message: 'Không thể xóa khóa học đã có học viên đăng ký'\n      }, { status: 409 });\n    }\n\n    await Course.findByIdAndDelete(id);\n\n    return NextResponse.json({\n      success: true,\n      message: 'Xóa khóa học thành công'\n    });\n\n  } catch (error) {\n    console.error('Delete course error:', error);\n    \n    return NextResponse.json({\n      success: false,\n      message: 'Lỗi khi xóa khóa học',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAQO,eAAe,IAAI,OAAoB,EAAE,EAAE,MAAM,EAAe;IACrE,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,EAAE,EAAE,GAAG;QAEf,cAAc;QACd,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,IAClC,QAAQ,CAAC,aAAa;QAEzB,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,sDAAsD;QACtD,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,MAAM,YAAY,OAAO,WAAW,IAClB,QAAQ,CAAC,KAAK,IAAI,KAAK,WAAW,KAAK,MAAM,KAAK,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE;QAEnG,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,2CAA2C;QAC3C,IAAI,iBAAiB;QACrB,IAAI,QAAQ,KAAK,IAAI,KAAK,WAAW;YACnC,iBAAiB,MAAM,6HAAA,CAAA,UAAU,CAAC,OAAO,CAAC;gBACxC,WAAW,KAAK,MAAM;gBACtB,UAAU;YACZ;QACF;QAEA,6BAA6B;QAC7B,MAAM,mBAAmB,MAAM,6HAAA,CAAA,UAAU,CAAC,cAAc,CAAC;YAAE,UAAU;QAAG;QAExE,sBAAsB;QACtB,IAAI,aAAa,OAAO,MAAM;QAE9B,kDAAkD;QAClD,IAAI,QAAQ,KAAK,IAAI,KAAK,aAAa,CAAC,gBAAgB;YACtD,WAAW,UAAU,GAAG,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC,SAAgB,CAAC;oBAClE,GAAG,MAAM;oBACT,UAAU,OAAO,SAAS,GAAG,OAAO,QAAQ,GAAG;oBAC/C,WAAW,OAAO,SAAS,GAAG,OAAO,SAAS,GAAG,EAAE;oBACnD,YAAY;oBACZ,MAAM;gBACR,CAAC;QACH;QAEA,mCAAmC;QACnC,MAAM,WAAW;YACf,GAAG,UAAU;YACb;YACA,YAAY,CAAC,CAAC;YACd,gBAAgB,iBAAiB;gBAC/B,UAAU,eAAe,QAAQ;gBACjC,kBAAkB,eAAe,gBAAgB;gBACjD,gBAAgB,eAAe,cAAc;gBAC7C,eAAe,eAAe,aAAa;gBAC3C,gBAAgB,eAAe,cAAc;gBAC7C,mBAAmB,eAAe,iBAAiB;YACrD,IAAI;QACN;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QAEnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,IAAI,OAAoB,EAAE,EAAE,MAAM,EAAe;IACrE,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,EAAE,EAAE,GAAG;QAEf,sCAAsC;QACtC,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,cAAc;QACd,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,QAAQ,CAAC;QACrC,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,mBAAmB;QACnB,MAAM,UAAU,KAAK,IAAI,KAAK,WAAW,KAAK,MAAM,KAAK,OAAO,SAAS,CAAC,QAAQ;QAClF,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,KAAK,EACL,WAAW,EACX,QAAQ,EACR,KAAK,EACL,KAAK,EACL,SAAS,EACT,UAAU,EACV,IAAI,EACJ,YAAY,EACZ,gBAAgB,EAChB,QAAQ,EACR,SAAS,EACT,WAAW,EACZ,GAAG;QAEJ,gBAAgB;QAChB,MAAM,aAAkB,CAAC;QAEzB,IAAI,UAAU,WAAW,WAAW,KAAK,GAAG,MAAM,IAAI;QACtD,IAAI,gBAAgB,WAAW,WAAW,WAAW,GAAG,YAAY,IAAI;QACxE,IAAI,aAAa,WAAW,WAAW,QAAQ,GAAG;QAClD,IAAI,UAAU,WAAW,WAAW,KAAK,GAAG;QAC5C,IAAI,UAAU,WAAW,WAAW,KAAK,GAAG;QAC5C,IAAI,cAAc,WAAW,WAAW,SAAS,GAAG;QACpD,IAAI,eAAe,WAAW;YAC5B,WAAW,UAAU,GAAG,WAAW,GAAG,CAAC,CAAC,QAAa,QAAkB,CAAC;oBACtE,GAAG,MAAM;oBACT,OAAO,QAAQ;gBACjB,CAAC;YACD,6BAA6B;YAC7B,WAAW,aAAa,GAAG,WAAW,MAAM,CAAC,CAAC,KAAa,SAAgB,MAAM,OAAO,QAAQ,EAAE;QACpG;QACA,IAAI,SAAS,WAAW,WAAW,IAAI,GAAG;QAC1C,IAAI,iBAAiB,WAAW,WAAW,YAAY,GAAG;QAC1D,IAAI,qBAAqB,WAAW,WAAW,gBAAgB,GAAG;QAClE,IAAI,aAAa,WAAW,WAAW,QAAQ,GAAG;QAClD,IAAI,cAAc,WAAW,WAAW,SAAS,GAAG;QACpD,IAAI,gBAAgB,WAAW,WAAW,WAAW,GAAG;QAExD,MAAM,gBAAgB,MAAM,yHAAA,CAAA,UAAM,CAAC,iBAAiB,CAClD,IACA,YACA;YAAE,KAAK;YAAM,eAAe;QAAK,GACjC,QAAQ,CAAC,aAAa;QAExB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QAEtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,OAAO,OAAoB,EAAE,EAAE,MAAM,EAAe;IACxE,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,EAAE,EAAE,GAAG;QAEf,sCAAsC;QACtC,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,cAAc;QACd,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,QAAQ,CAAC;QACrC,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,mBAAmB;QACnB,MAAM,YAAY,KAAK,IAAI,KAAK,WAAW,KAAK,MAAM,KAAK,OAAO,SAAS,CAAC,QAAQ;QACpF,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,kCAAkC;QAClC,MAAM,kBAAkB,MAAM,6HAAA,CAAA,UAAU,CAAC,cAAc,CAAC;YAAE,UAAU;QAAG;QACvE,IAAI,kBAAkB,GAAG;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,yHAAA,CAAA,UAAM,CAAC,iBAAiB,CAAC;QAE/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QAEtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}