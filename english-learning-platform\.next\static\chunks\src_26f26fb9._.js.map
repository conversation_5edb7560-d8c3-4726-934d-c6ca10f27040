{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('vi-VN', {\n    style: 'currency',\n    currency: 'VND'\n  }).format(price);\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('vi-VN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  }).format(dateObj);\n}\n\nexport function formatDuration(minutes: number): string {\n  const hours = Math.floor(minutes / 60);\n  const remainingMinutes = minutes % 60;\n\n  if (hours === 0) {\n    return `${remainingMinutes} phút`;\n  } else if (remainingMinutes === 0) {\n    return `${hours} giờ`;\n  } else {\n    return `${hours} giờ ${remainingMinutes} phút`;\n  }\n}\n\nexport function calculateProgress(completedLessons: number[], totalLessons: number): number {\n  if (totalLessons === 0) return 0;\n  return Math.round((completedLessons.length / totalLessons) * 100);\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2);\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '') // Remove diacritics\n    .replace(/[^a-z0-9 -]/g, '') // Remove special characters\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen\n    .trim();\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n\n  if (password.length < 6) {\n    errors.push('Mật khẩu phải có ít nhất 6 ký tự');\n  }\n\n  if (!/[A-Z]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 chữ hoa');\n  }\n\n  if (!/[a-z]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 chữ thường');\n  }\n\n  if (!/[0-9]/.test(password)) {\n    errors.push('Mật khẩu phải có ít nhất 1 số');\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\nexport function generateRandomString(length: number): string {\n  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += characters.charAt(Math.floor(Math.random() * characters.length));\n  }\n  return result;\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,mBAAmB,UAAU;IAEnC,IAAI,UAAU,GAAG;QACf,OAAO,GAAG,iBAAiB,KAAK,CAAC;IACnC,OAAO,IAAI,qBAAqB,GAAG;QACjC,OAAO,GAAG,MAAM,IAAI,CAAC;IACvB,OAAO;QACL,OAAO,GAAG,MAAM,KAAK,EAAE,iBAAiB,KAAK,CAAC;IAChD;AACF;AAEO,SAAS,kBAAkB,gBAA0B,EAAE,YAAoB;IAChF,IAAI,iBAAiB,GAAG,OAAO;IAC/B,OAAO,KAAK,KAAK,CAAC,AAAC,iBAAiB,MAAM,GAAG,eAAgB;AAC/D;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAAI,oBAAoB;KACpD,OAAO,CAAC,gBAAgB,IAAI,4BAA4B;KACxD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,OAAO,KAAK,8CAA8C;KAClE,IAAI;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,qBAAqB,MAAc;IACjD,MAAM,aAAa;IACnB,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,WAAW,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM;IAC1E;IACA,OAAO;AACT;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { useState } from \"react\";\nimport { Book<PERSON>pen, Menu, X } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { useAuth } from \"@/contexts/AuthContext\";\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const { user, isAuthenticated, logout } = useAuth();\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  const handleLogout = async () => {\n    await logout();\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <BookOpen className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-xl font-bold text-gray-900\">\n              English Learning Platform\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <Link\n              href=\"/courses\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Khóa Học\n            </Link>\n            <Link\n              href=\"/tests\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Luyện Thi\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Giới Thiệu\n            </Link>\n            <Link\n              href=\"/contact\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Liên Hệ\n            </Link>\n          </nav>\n\n          {/* Desktop Auth/User Menu */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {isAuthenticated && user ? (\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                    <Avatar className=\"h-8 w-8\">\n                      <AvatarImage src={user.avatar} alt={user.name} />\n                      <AvatarFallback>\n                        {user.name.split(' ').map((n: string) => n[0]).join('')}\n                      </AvatarFallback>\n                    </Avatar>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                  <DropdownMenuLabel className=\"font-normal\">\n                    <div className=\"flex flex-col space-y-1\">\n                      <p className=\"text-sm font-medium leading-none\">{user.name}</p>\n                      <p className=\"text-xs leading-none text-muted-foreground\">\n                        {user.email}\n                      </p>\n                    </div>\n                  </DropdownMenuLabel>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard\">Dashboard</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/profile\">Hồ sơ cá nhân</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/settings\">Cài đặt</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem onClick={handleLogout}>\n                    Đăng xuất\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            ) : (\n              <>\n                <Link href=\"/auth/login\">\n                  <Button variant=\"ghost\">\n                    Đăng Nhập\n                  </Button>\n                </Link>\n                <Link href=\"/auth/register\">\n                  <Button>\n                    Đăng Ký\n                  </Button>\n                </Link>\n              </>\n            )}\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            className=\"md:hidden p-2\"\n            onClick={toggleMenu}\n            aria-label=\"Toggle menu\"\n          >\n            {isMenuOpen ? (\n              <X className=\"h-6 w-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"h-6 w-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <Link\n                href=\"/courses\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Khóa Học\n              </Link>\n              <Link\n                href=\"/tests\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Luyện Thi\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Giới Thiệu\n              </Link>\n              <Link\n                href=\"/contact\"\n                className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Liên Hệ\n              </Link>\n\n              <div className=\"flex flex-col space-y-2 pt-4 border-t border-gray-200\">\n                {isAuthenticated && user ? (\n                  <>\n                    <div className=\"flex items-center space-x-3 px-3 py-2\">\n                      <Avatar className=\"h-8 w-8\">\n                        <AvatarImage src={user.avatar} alt={user.name} />\n                        <AvatarFallback>\n                          {user.name.split(' ').map((n: string) => n[0]).join('')}\n                        </AvatarFallback>\n                      </Avatar>\n                      <div>\n                        <p className=\"text-sm font-medium\">{user.name}</p>\n                        <p className=\"text-xs text-gray-500\">{user.email}</p>\n                      </div>\n                    </div>\n                    <Link href=\"/dashboard\" onClick={() => setIsMenuOpen(false)}>\n                      <Button variant=\"ghost\" className=\"w-full justify-start\">\n                        Dashboard\n                      </Button>\n                    </Link>\n                    <Link href=\"/profile\" onClick={() => setIsMenuOpen(false)}>\n                      <Button variant=\"ghost\" className=\"w-full justify-start\">\n                        Hồ sơ cá nhân\n                      </Button>\n                    </Link>\n                    <Button\n                      variant=\"ghost\"\n                      className=\"w-full justify-start\"\n                      onClick={handleLogout}\n                    >\n                      Đăng xuất\n                    </Button>\n                  </>\n                ) : (\n                  <>\n                    <Link href=\"/auth/login\" onClick={() => setIsMenuOpen(false)}>\n                      <Button variant=\"ghost\" className=\"w-full justify-start\">\n                        Đăng Nhập\n                      </Button>\n                    </Link>\n                    <Link href=\"/auth/register\" onClick={() => setIsMenuOpen(false)}>\n                      <Button className=\"w-full\">\n                        Đăng Ký\n                      </Button>\n                    </Link>\n                  </>\n                )}\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAQA;AACA;;;AAfA;;;;;;;;AAiBe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhD,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,MAAM,eAAe;QACnB,MAAM;QACN,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAMzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACZ,mBAAmB,qBAClB,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,WAAU;sDAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,qIAAA,CAAA,cAAW;wDAAC,KAAK,KAAK,MAAM;wDAAE,KAAK,KAAK,IAAI;;;;;;kEAC7C,6LAAC,qIAAA,CAAA,iBAAc;kEACZ,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;kDAK5D,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,WAAU;wCAAO,OAAM;wCAAM,UAAU;;0DAC1D,6LAAC,+IAAA,CAAA,oBAAiB;gDAAC,WAAU;0DAC3B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAoC,KAAK,IAAI;;;;;;sEAC1D,6LAAC;4DAAE,WAAU;sEACV,KAAK,KAAK;;;;;;;;;;;;;;;;;0DAIjB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0DACtB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAa;;;;;;;;;;;0DAE1B,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAW;;;;;;;;;;;0DAExB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAY;;;;;;;;;;;0DAEzB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0DACtB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS;0DAAc;;;;;;;;;;;;;;;;;qDAM7C;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAQ;;;;;;;;;;;kDAI1B,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;;;;;;;;sCAShB,6LAAC;4BACC,WAAU;4BACV,SAAS;4BACT,cAAW;sCAEV,2BACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAID,6LAAC;gCAAI,WAAU;0CACZ,mBAAmB,qBAClB;;sDACE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,WAAU;;sEAChB,6LAAC,qIAAA,CAAA,cAAW;4DAAC,KAAK,KAAK,MAAM;4DAAE,KAAK,KAAK,IAAI;;;;;;sEAC7C,6LAAC,qIAAA,CAAA,iBAAc;sEACZ,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;8DAGxD,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAuB,KAAK,IAAI;;;;;;sEAC7C,6LAAC;4DAAE,WAAU;sEAAyB,KAAK,KAAK;;;;;;;;;;;;;;;;;;sDAGpD,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,SAAS,IAAM,cAAc;sDACnD,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAAuB;;;;;;;;;;;sDAI3D,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,SAAS,IAAM,cAAc;sDACjD,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAAuB;;;;;;;;;;;sDAI3D,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;sDACV;;;;;;;iEAKH;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,SAAS,IAAM,cAAc;sDACpD,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAAuB;;;;;;;;;;;sDAI3D,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAiB,SAAS,IAAM,cAAc;sDACvD,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAajD;GAjNwB;;QAEoB,kIAAA,CAAA,UAAO;;;KAF3B", "debugId": null}}, {"offset": {"line": 1385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/TA2/english-learning-platform/src/app/tests/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { Search, Filter, Clock, Users, Target, Play, Award, TrendingUp, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Eye } from \"lucide-react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from \"@/components/ui/tabs\";\nimport Header from \"@/components/layout/Header\";\n\n// Interface for test data from API\ninterface Test {\n  _id: string;\n  title: string;\n  description: string;\n  type: string;\n  level: string;\n  duration: number;\n  totalQuestions: number;\n  difficulty: string;\n  tags?: string[];\n  createdBy?: {\n    name: string;\n  };\n  createdAt: string;\n  isPublished: boolean;\n}\n\nconst testCategories = [\n  { value: \"all\", label: \"<PERSON>ất cả\", icon: Target },\n  { value: \"vocabulary\", label: \"Từ vựng\", icon: Target },\n  { value: \"grammar\", label: \"Ngữ pháp\", icon: Target },\n  { value: \"listening\", label: \"<PERSON>he\", icon: Target },\n  { value: \"speaking\", label: \"Nói\", icon: Target },\n  { value: \"reading\", label: \"Đọc\", icon: Target },\n  { value: \"writing\", label: \"Viết\", icon: Target }\n];\n\nconst levels = [\n  { value: \"all\", label: \"Tất cả trình độ\" },\n  { value: \"beginner\", label: \"Cơ bản\" },\n  { value: \"intermediate\", label: \"Trung cấp\" },\n  { value: \"advanced\", label: \"Nâng cao\" }\n];\n\nconst getDifficultyColor = (difficulty: string) => {\n  switch (difficulty) {\n    case \"easy\": return \"bg-green-100 text-green-800\";\n    case \"medium\": return \"bg-yellow-100 text-yellow-800\";\n    case \"hard\": return \"bg-red-100 text-red-800\";\n    default: return \"bg-gray-100 text-gray-800\";\n  }\n};\n\nconst getTypeLabel = (type: string) => {\n  const typeMap: Record<string, string> = {\n    vocabulary: \"Từ vựng\",\n    grammar: \"Ngữ pháp\", \n    listening: \"Nghe\",\n    speaking: \"Nói\",\n    reading: \"Đọc\",\n    writing: \"Viết\"\n  };\n  return typeMap[type] || type;\n};\n\nconst getLevelLabel = (level: string) => {\n  const levelMap: Record<string, string> = {\n    beginner: \"Cơ bản\",\n    intermediate: \"Trung cấp\",\n    advanced: \"Nâng cao\"\n  };\n  return levelMap[level] || level;\n};\n\nexport default function TestsPage() {\n  const [tests, setTests] = useState<Test[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [selectedCategory, setSelectedCategory] = useState(\"all\");\n  const [selectedLevel, setSelectedLevel] = useState(\"all\");\n  const [stats, setStats] = useState({\n    totalTests: 0,\n    totalParticipants: 0,\n    averageScore: 0,\n    averageDuration: 0\n  });\n\n  // Load tests from API\n  useEffect(() => {\n    loadTests();\n  }, [selectedCategory, selectedLevel, searchTerm]);\n\n  const loadTests = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const params = new URLSearchParams();\n      if (selectedCategory !== 'all') params.append('type', selectedCategory);\n      if (selectedLevel !== 'all') params.append('level', selectedLevel);\n      if (searchTerm) params.append('search', searchTerm);\n\n      const response = await fetch(`/api/tests?${params.toString()}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setTests(data.data);\n        // Calculate stats from real data\n        setStats({\n          totalTests: data.data.length,\n          totalParticipants: data.data.length * 100, // Estimate\n          averageScore: 75, // Default for now\n          averageDuration: data.data.reduce((sum: number, test: Test) => sum + test.duration, 0) / (data.data.length || 1)\n        });\n      } else {\n        setError(data.message || 'Lỗi khi tải danh sách bài kiểm tra');\n      }\n    } catch (error) {\n      console.error('Load tests error:', error);\n      setError('Lỗi kết nối. Vui lòng thử lại.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleRetry = () => {\n    loadTests();\n  };\n\n  // Show loading state\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Đang tải danh sách bài kiểm tra...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error state\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center py-12\">\n            <div className=\"text-red-600 mb-4\">\n              <AlertTriangle className=\"h-12 w-12 mx-auto mb-2\" />\n              <p className=\"text-lg font-medium\">Có lỗi xảy ra</p>\n              <p className=\"text-sm\">{error}</p>\n            </div>\n            <Button onClick={handleRetry} variant=\"outline\">\n              Thử lại\n            </Button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Page Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Luyện Thi Tiếng Anh</h1>\n          <p className=\"text-lg text-gray-600\">\n            Kiểm tra và nâng cao trình độ tiếng Anh của bạn với các bài thi đa dạng\n          </p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Tổng bài thi</CardTitle>\n              <Target className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{stats.totalTests}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Đa dạng các loại bài thi\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Lượt thi</CardTitle>\n              <Users className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {stats.totalParticipants.toLocaleString()}\n              </div>\n              <p className=\"text-xs text-muted-foreground\">\n                Tổng lượt thi của tất cả bài\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Điểm trung bình</CardTitle>\n              <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {Math.round(stats.averageScore)}%\n              </div>\n              <p className=\"text-xs text-muted-foreground\">\n                Điểm số trung bình của tất cả bài thi\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Thời gian trung bình</CardTitle>\n              <Clock className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {Math.round(stats.averageDuration)} phút\n              </div>\n              <p className=\"text-xs text-muted-foreground\">\n                Thời gian làm bài trung bình\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Search and Filter */}\n        <div className=\"bg-white rounded-lg shadow-sm p-6 mb-8\">\n          <div className=\"flex flex-col lg:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                <Input\n                  placeholder=\"Tìm kiếm bài thi...\"\n                  className=\"pl-10\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n              </div>\n            </div>\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <select\n                className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n              >\n                {testCategories.map(category => (\n                  <option key={category.value} value={category.value}>\n                    {category.label}\n                  </option>\n                ))}\n              </select>\n              <select\n                className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                value={selectedLevel}\n                onChange={(e) => setSelectedLevel(e.target.value)}\n              >\n                {levels.map(level => (\n                  <option key={level.value} value={level.value}>\n                    {level.label}\n                  </option>\n                ))}\n              </select>\n              <Button variant=\"outline\">\n                <Filter className=\"h-4 w-4 mr-2\" />\n                Lọc ({tests.length})\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Test Categories Tabs */}\n        <Tabs defaultValue=\"all\" className=\"space-y-6\">\n          <TabsList className=\"grid w-full grid-cols-4 lg:grid-cols-7\">\n            {testCategories.map(category => (\n              <TabsTrigger key={category.value} value={category.value} className=\"text-xs\">\n                {category.label}\n              </TabsTrigger>\n            ))}\n          </TabsList>\n\n          <TabsContent value=\"all\" className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {tests.map(test => (\n                <Card key={test._id} className=\"hover:shadow-lg transition-shadow\">\n                  <CardHeader>\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <Badge variant=\"outline\">\n                        {getTypeLabel(test.type)}\n                      </Badge>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(test.difficulty)}`}>\n                        {test.difficulty === 'easy' ? 'Dễ' : test.difficulty === 'medium' ? 'Trung bình' : 'Khó'}\n                      </span>\n                    </div>\n                    <CardTitle className=\"text-lg line-clamp-2\">{test.title}</CardTitle>\n                    <CardDescription className=\"line-clamp-2\">\n                      {test.description}\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                      <div className=\"flex items-center\">\n                        <Clock className=\"h-4 w-4 mr-1\" />\n                        {test.duration} phút\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Target className=\"h-4 w-4 mr-1\" />\n                        {test.totalQuestions} câu\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Users className=\"h-4 w-4 mr-1\" />\n                        {Math.floor(Math.random() * 1000) + 100}\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <p className=\"text-sm text-gray-600\">Điểm TB: <span className=\"font-medium\">{Math.floor(Math.random() * 30) + 70}%</span></p>\n                        <p className=\"text-xs text-gray-500\">{getLevelLabel(test.level)}</p>\n                      </div>\n                      <div className=\"flex gap-2\">\n                        <Link href={`/tests/${test._id}/preview`}>\n                          <Button variant=\"outline\" size=\"sm\">\n                            <Eye className=\"h-4 w-4 mr-1\" />\n                            Xem trước\n                          </Button>\n                        </Link>\n                        <Link href={`/tests/${test._id}`}>\n                          <Button size=\"sm\">\n                            <Play className=\"h-4 w-4 mr-1\" />\n                            Bắt đầu\n                          </Button>\n                        </Link>\n                      </div>\n                    </div>\n\n                    <div className=\"flex flex-wrap gap-1\">\n                      {test.tags?.slice(0, 3).map((tag, index) => (\n                        <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                          {tag}\n                        </Badge>\n                      ))}\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          </TabsContent>\n\n          {/* Individual category tabs would filter the tests */}\n          {testCategories.slice(1).map(category => (\n            <TabsContent key={category.value} value={category.value} className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {tests\n                  .filter((test: Test) => test.type === category.value)\n                  .map((test: Test) => (\n                    <Card key={test._id} className=\"hover:shadow-lg transition-shadow\">\n                      <CardHeader>\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <Badge variant=\"outline\">\n                            {getTypeLabel(test.type)}\n                          </Badge>\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(test.difficulty)}`}>\n                            {test.difficulty === 'easy' ? 'Dễ' : test.difficulty === 'medium' ? 'Trung bình' : 'Khó'}\n                          </span>\n                        </div>\n                        <CardTitle className=\"text-lg line-clamp-2\">{test.title}</CardTitle>\n                        <CardDescription className=\"line-clamp-2\">\n                          {test.description}\n                        </CardDescription>\n                      </CardHeader>\n                      <CardContent className=\"space-y-4\">\n                        <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                          <div className=\"flex items-center\">\n                            <Clock className=\"h-4 w-4 mr-1\" />\n                            {test.duration} phút\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Target className=\"h-4 w-4 mr-1\" />\n                            {test.totalQuestions} câu\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Users className=\"h-4 w-4 mr-1\" />\n                            {Math.floor(Math.random() * 1000) + 100}\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <p className=\"text-sm text-gray-600\">Điểm TB: <span className=\"font-medium\">{Math.floor(Math.random() * 30) + 70}%</span></p>\n                            <p className=\"text-xs text-gray-500\">{getLevelLabel(test.level)}</p>\n                          </div>\n                          <div className=\"flex gap-2\">\n                            <Link href={`/tests/${test._id}/preview`}>\n                              <Button variant=\"outline\" size=\"sm\">\n                                <Eye className=\"h-4 w-4 mr-1\" />\n                                Xem trước\n                              </Button>\n                            </Link>\n                            <Link href={`/tests/${test._id}`}>\n                              <Button size=\"sm\">\n                                <Play className=\"h-4 w-4 mr-1\" />\n                                Bắt đầu\n                              </Button>\n                            </Link>\n                          </div>\n                        </div>\n\n                        <div className=\"flex flex-wrap gap-1\">\n                          {test.tags?.slice(0, 3).map((tag, index) => (\n                            <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                              {tag}\n                            </Badge>\n                          ))}\n                        </div>\n                      </CardContent>\n                    </Card>\n                  ))}\n              </div>\n            </TabsContent>\n          ))}\n        </Tabs>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AA8BA,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAO,OAAO;QAAU,MAAM,yMAAA,CAAA,SAAM;IAAC;IAC9C;QAAE,OAAO;QAAc,OAAO;QAAW,MAAM,yMAAA,CAAA,SAAM;IAAC;IACtD;QAAE,OAAO;QAAW,OAAO;QAAY,MAAM,yMAAA,CAAA,SAAM;IAAC;IACpD;QAAE,OAAO;QAAa,OAAO;QAAQ,MAAM,yMAAA,CAAA,SAAM;IAAC;IAClD;QAAE,OAAO;QAAY,OAAO;QAAO,MAAM,yMAAA,CAAA,SAAM;IAAC;IAChD;QAAE,OAAO;QAAW,OAAO;QAAO,MAAM,yMAAA,CAAA,SAAM;IAAC;IAC/C;QAAE,OAAO;QAAW,OAAO;QAAQ,MAAM,yMAAA,CAAA,SAAM;IAAC;CACjD;AAED,MAAM,SAAS;IACb;QAAE,OAAO;QAAO,OAAO;IAAkB;IACzC;QAAE,OAAO;QAAY,OAAO;IAAS;IACrC;QAAE,OAAO;QAAgB,OAAO;IAAY;IAC5C;QAAE,OAAO;QAAY,OAAO;IAAW;CACxC;AAED,MAAM,qBAAqB,CAAC;IAC1B,OAAQ;QACN,KAAK;YAAQ,OAAO;QACpB,KAAK;YAAU,OAAO;QACtB,KAAK;YAAQ,OAAO;QACpB;YAAS,OAAO;IAClB;AACF;AAEA,MAAM,eAAe,CAAC;IACpB,MAAM,UAAkC;QACtC,YAAY;QACZ,SAAS;QACT,WAAW;QACX,UAAU;QACV,SAAS;QACT,SAAS;IACX;IACA,OAAO,OAAO,CAAC,KAAK,IAAI;AAC1B;AAEA,MAAM,gBAAgB,CAAC;IACrB,MAAM,WAAmC;QACvC,UAAU;QACV,cAAc;QACd,UAAU;IACZ;IACA,OAAO,QAAQ,CAAC,MAAM,IAAI;AAC5B;AAEe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,YAAY;QACZ,mBAAmB;QACnB,cAAc;QACd,iBAAiB;IACnB;IAEA,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG;QAAC;QAAkB;QAAe;KAAW;IAEhD,MAAM,YAAY;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,qBAAqB,OAAO,OAAO,MAAM,CAAC,QAAQ;YACtD,IAAI,kBAAkB,OAAO,OAAO,MAAM,CAAC,SAAS;YACpD,IAAI,YAAY,OAAO,MAAM,CAAC,UAAU;YAExC,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,QAAQ,IAAI;YAC9D,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS,KAAK,IAAI;gBAClB,iCAAiC;gBACjC,SAAS;oBACP,YAAY,KAAK,IAAI,CAAC,MAAM;oBAC5B,mBAAmB,KAAK,IAAI,CAAC,MAAM,GAAG;oBACtC,cAAc;oBACd,iBAAiB,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,KAAa,OAAe,MAAM,KAAK,QAAQ,EAAE,KAAK,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;gBACjH;YACF,OAAO;gBACL,SAAS,KAAK,OAAO,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB;IACF;IAEA,qBAAqB;IACrB,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;IAKvC;IAEA,mBAAmB;IACnB,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC;wCAAE,WAAU;kDAAW;;;;;;;;;;;;0CAE1B,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAa,SAAQ;0CAAU;;;;;;;;;;;;;;;;;;;;;;;IAO1D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAMvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;kDAEpB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DAAsB,MAAM,UAAU;;;;;;0DACrD,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAMjD,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DACZ,MAAM,iBAAiB,CAAC,cAAc;;;;;;0DAEzC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAMjD,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,KAAK,CAAC,MAAM,YAAY;oDAAE;;;;;;;0DAElC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAMjD,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,KAAK,CAAC,MAAM,eAAe;oDAAE;;;;;;;0DAErC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;kCAQnD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,WAAU;gDACV,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;8CAInD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;sDAElD,eAAe,GAAG,CAAC,CAAA,yBAClB,6LAAC;oDAA4B,OAAO,SAAS,KAAK;8DAC/C,SAAS,KAAK;mDADJ,SAAS,KAAK;;;;;;;;;;sDAK/B,6LAAC;4CACC,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;sDAE/C,OAAO,GAAG,CAAC,CAAA,sBACV,6LAAC;oDAAyB,OAAO,MAAM,KAAK;8DACzC,MAAM,KAAK;mDADD,MAAM,KAAK;;;;;;;;;;sDAK5B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;;8DACd,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;gDAC7B,MAAM,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAO3B,6LAAC,mIAAA,CAAA,OAAI;wBAAC,cAAa;wBAAM,WAAU;;0CACjC,6LAAC,mIAAA,CAAA,WAAQ;gCAAC,WAAU;0CACjB,eAAe,GAAG,CAAC,CAAA,yBAClB,6LAAC,mIAAA,CAAA,cAAW;wCAAsB,OAAO,SAAS,KAAK;wCAAE,WAAU;kDAChE,SAAS,KAAK;uCADC,SAAS,KAAK;;;;;;;;;;0CAMpC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAM,WAAU;0CACjC,cAAA,6LAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAA,qBACT,6LAAC,mIAAA,CAAA,OAAI;4CAAgB,WAAU;;8DAC7B,6LAAC,mIAAA,CAAA,aAAU;;sEACT,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;8EACZ,aAAa,KAAK,IAAI;;;;;;8EAEzB,6LAAC;oEAAK,WAAW,CAAC,2CAA2C,EAAE,mBAAmB,KAAK,UAAU,GAAG;8EACjG,KAAK,UAAU,KAAK,SAAS,OAAO,KAAK,UAAU,KAAK,WAAW,eAAe;;;;;;;;;;;;sEAGvF,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAwB,KAAK,KAAK;;;;;;sEACvD,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEACxB,KAAK,WAAW;;;;;;;;;;;;8DAGrB,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,KAAK,QAAQ;wEAAC;;;;;;;8EAEjB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,yMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEACjB,KAAK,cAAc;wEAAC;;;;;;;8EAEvB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;;;;;;;;;;;;;sEAIxC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;;gFAAwB;8FAAS,6LAAC;oFAAK,WAAU;;wFAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;wFAAG;;;;;;;;;;;;;sFACjH,6LAAC;4EAAE,WAAU;sFAAyB,cAAc,KAAK,KAAK;;;;;;;;;;;;8EAEhE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,+JAAA,CAAA,UAAI;4EAAC,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,QAAQ,CAAC;sFACtC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gFAAC,SAAQ;gFAAU,MAAK;;kGAC7B,6LAAC,mMAAA,CAAA,MAAG;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;sFAIpC,6LAAC,+JAAA,CAAA,UAAI;4EAAC,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;sFAC9B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gFAAC,MAAK;;kGACX,6LAAC,qMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;;;;;;;;;;;;;sEAOzC,6LAAC;4DAAI,WAAU;sEACZ,KAAK,IAAI,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,sBAChC,6LAAC,oIAAA,CAAA,QAAK;oEAAa,SAAQ;oEAAY,WAAU;8EAC9C;mEADS;;;;;;;;;;;;;;;;;2CAtDT,KAAK,GAAG;;;;;;;;;;;;;;;4BAkExB,eAAe,KAAK,CAAC,GAAG,GAAG,CAAC,CAAA,yBAC3B,6LAAC,mIAAA,CAAA,cAAW;oCAAsB,OAAO,SAAS,KAAK;oCAAE,WAAU;8CACjE,cAAA,6LAAC;wCAAI,WAAU;kDACZ,MACE,MAAM,CAAC,CAAC,OAAe,KAAK,IAAI,KAAK,SAAS,KAAK,EACnD,GAAG,CAAC,CAAC,qBACJ,6LAAC,mIAAA,CAAA,OAAI;gDAAgB,WAAU;;kEAC7B,6LAAC,mIAAA,CAAA,aAAU;;0EACT,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;kFACZ,aAAa,KAAK,IAAI;;;;;;kFAEzB,6LAAC;wEAAK,WAAW,CAAC,2CAA2C,EAAE,mBAAmB,KAAK,UAAU,GAAG;kFACjG,KAAK,UAAU,KAAK,SAAS,OAAO,KAAK,UAAU,KAAK,WAAW,eAAe;;;;;;;;;;;;0EAGvF,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAwB,KAAK,KAAK;;;;;;0EACvD,6LAAC,mIAAA,CAAA,kBAAe;gEAAC,WAAU;0EACxB,KAAK,WAAW;;;;;;;;;;;;kEAGrB,6LAAC,mIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAChB,KAAK,QAAQ;4EAAC;;;;;;;kFAEjB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EACjB,KAAK,cAAc;4EAAC;;;;;;;kFAEvB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAChB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;;;;;;;;;;;;;0EAIxC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;;oFAAwB;kGAAS,6LAAC;wFAAK,WAAU;;4FAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;4FAAG;;;;;;;;;;;;;0FACjH,6LAAC;gFAAE,WAAU;0FAAyB,cAAc,KAAK,KAAK;;;;;;;;;;;;kFAEhE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,+JAAA,CAAA,UAAI;gFAAC,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,QAAQ,CAAC;0FACtC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oFAAC,SAAQ;oFAAU,MAAK;;sGAC7B,6LAAC,mMAAA,CAAA,MAAG;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;0FAIpC,6LAAC,+JAAA,CAAA,UAAI;gFAAC,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;0FAC9B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oFAAC,MAAK;;sGACX,6LAAC,qMAAA,CAAA,OAAI;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;;;;;;;;;;;;;0EAOzC,6LAAC;gEAAI,WAAU;0EACZ,KAAK,IAAI,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,sBAChC,6LAAC,oIAAA,CAAA,QAAK;wEAAa,SAAQ;wEAAY,WAAU;kFAC9C;uEADS;;;;;;;;;;;;;;;;;+CAtDT,KAAK,GAAG;;;;;;;;;;mCALT,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;AA0E5C;GA7WwB;KAAA", "debugId": null}}]}