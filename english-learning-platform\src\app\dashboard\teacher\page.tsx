"use client";

import { useState } from "react";
import Link from "next/link";
import { 
  BookO<PERSON>, 
  Users, 
  TrendingUp, 
  DollarSign, 
  Plus, 
  Edit, 
  Eye,
  BarChart3,
  Calendar,
  MessageSquare,
  Star
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import Header from "@/components/layout/Header";
import { formatPrice } from "@/lib/utils";

// Mock data for teacher
const mockTeacher = {
  name: "<PERSON><PERSON>",
  email: "<EMAIL>",
  avatar: "",
  role: "teacher",
  joinDate: "2023-06-15",
  totalCourses: 5,
  totalStudents: 1250,
  totalRevenue: 45000000,
  averageRating: 4.8,
  monthlyRevenue: 8500000
};

const mockCourses = [
  {
    id: "1",
    title: "<PERSON><PERSON><PERSON><PERSON> cho <PERSON>",
    thumbnail: "/api/placeholder/300/200",
    students: 450,
    revenue: 15000000,
    rating: 4.8,
    totalRatings: 89,
    status: "published",
    createdAt: "2023-08-15",
    lastUpdated: "2024-01-10"
  },
  {
    id: "2",
    title: "Tiếng Anh Giao Tiếp Nâng Cao",
    thumbnail: "/api/placeholder/300/200", 
    students: 320,
    revenue: 12000000,
    rating: 4.7,
    totalRatings: 65,
    status: "published",
    createdAt: "2023-10-20",
    lastUpdated: "2024-01-08"
  },
  {
    id: "3",
    title: "Luyện Phát Âm Tiếng Anh Chuẩn",
    thumbnail: "/api/placeholder/300/200",
    students: 280,
    revenue: 9500000,
    rating: 4.9,
    totalRatings: 42,
    status: "published", 
    createdAt: "2023-12-01",
    lastUpdated: "2024-01-15"
  },
  {
    id: "4",
    title: "Tiếng Anh Thương Mại Cơ Bản",
    thumbnail: "/api/placeholder/300/200",
    students: 150,
    revenue: 6000000,
    rating: 4.6,
    totalRatings: 28,
    status: "draft",
    createdAt: "2024-01-05",
    lastUpdated: "2024-01-18"
  }
];

const mockStudentProgress = [
  {
    studentName: "Nguyễn Văn A",
    courseName: "Tiếng Anh Cơ Bản",
    progress: 75,
    lastActive: "2024-01-20",
    totalLessons: 25,
    completedLessons: 19
  },
  {
    studentName: "Trần Thị B", 
    courseName: "Tiếng Anh Giao Tiếp",
    progress: 60,
    lastActive: "2024-01-19",
    totalLessons: 30,
    completedLessons: 18
  },
  {
    studentName: "Lê Văn C",
    courseName: "Luyện Phát Âm",
    progress: 40,
    lastActive: "2024-01-18",
    totalLessons: 20,
    completedLessons: 8
  }
];

const mockRecentReviews = [
  {
    studentName: "Nguyễn Văn A",
    courseName: "Tiếng Anh Cơ Bản",
    rating: 5,
    comment: "Khóa học rất hay, cô giảng dễ hiểu. Tôi đã học được rất nhiều kiến thức bổ ích.",
    date: "2024-01-19"
  },
  {
    studentName: "Trần Thị B",
    courseName: "Tiếng Anh Giao Tiếp", 
    rating: 5,
    comment: "Phương pháp giảng dạy rất thú vị, không hề nhàm chán. Recommend cho mọi người!",
    date: "2024-01-18"
  },
  {
    studentName: "Lê Văn C",
    courseName: "Luyện Phát Âm",
    rating: 4,
    comment: "Khóa học tốt, nội dung phong phú. Chỉ mong có thêm nhiều bài tập thực hành hơn.",
    date: "2024-01-17"
  }
];

export default function TeacherDashboard() {
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Dashboard Giáo Viên
            </h1>
            <p className="text-lg text-gray-600">
              Quản lý khóa học và theo dõi hiệu quả giảng dạy
            </p>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Tạo khóa học mới
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tổng khóa học</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockTeacher.totalCourses}</div>
              <p className="text-xs text-muted-foreground">
                3 đang hoạt động, 1 nháp
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tổng học viên</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockTeacher.totalStudents.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                +45 học viên mới tuần này
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Doanh thu tháng này</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatPrice(mockTeacher.monthlyRevenue)}</div>
              <p className="text-xs text-muted-foreground">
                +12% so với tháng trước
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Đánh giá trung bình</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockTeacher.averageRating}</div>
              <p className="text-xs text-muted-foreground">
                Từ 224 đánh giá
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Tổng quan</TabsTrigger>
            <TabsTrigger value="courses">Khóa học</TabsTrigger>
            <TabsTrigger value="students">Học viên</TabsTrigger>
            <TabsTrigger value="analytics">Thống kê</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle>Hoạt động gần đây</CardTitle>
                  <CardDescription>
                    Tiến độ học tập của học viên
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {mockStudentProgress.map((student, index) => (
                    <div key={index} className="flex items-center space-x-4 p-3 border rounded-lg">
                      <div className="flex-1">
                        <h3 className="font-medium text-sm">{student.studentName}</h3>
                        <p className="text-xs text-gray-500">{student.courseName}</p>
                        <div className="flex items-center mt-2">
                          <Progress value={student.progress} className="flex-1 mr-2" />
                          <span className="text-xs text-gray-500">{student.progress}%</span>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500">
                        {student.lastActive}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Recent Reviews */}
              <Card>
                <CardHeader>
                  <CardTitle>Đánh giá mới nhất</CardTitle>
                  <CardDescription>
                    Phản hồi từ học viên
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {mockRecentReviews.map((review, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium text-sm">{review.studentName}</h3>
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-3 w-3 ${
                                i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                      <p className="text-xs text-gray-600 mb-1">{review.courseName}</p>
                      <p className="text-sm text-gray-700">{review.comment}</p>
                      <p className="text-xs text-gray-500 mt-2">{review.date}</p>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Courses Tab */}
          <TabsContent value="courses" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Quản lý khóa học</h2>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Tạo khóa học mới
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {mockCourses.map(course => (
                <Card key={course.id}>
                  <div className="aspect-video bg-gray-200 rounded-t-lg"></div>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <Badge variant={course.status === 'published' ? 'default' : 'secondary'}>
                        {course.status === 'published' ? 'Đã xuất bản' : 'Nháp'}
                      </Badge>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
                        <span className="text-sm">{course.rating}</span>
                      </div>
                    </div>
                    <CardTitle className="text-lg">{course.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Học viên</p>
                        <p className="font-medium">{course.students}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Doanh thu</p>
                        <p className="font-medium">{formatPrice(course.revenue)}</p>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="h-4 w-4 mr-1" />
                        Xem
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <Edit className="h-4 w-4 mr-1" />
                        Sửa
                      </Button>
                      <Button variant="outline" size="sm">
                        <BarChart3 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Students Tab */}
          <TabsContent value="students" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Danh sách học viên</CardTitle>
                <CardDescription>
                  Theo dõi tiến độ học tập của học viên
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockStudentProgress.map((student, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
                        <div>
                          <h3 className="font-medium">{student.studentName}</h3>
                          <p className="text-sm text-gray-500">{student.courseName}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <p className="text-sm font-medium">{student.progress}% hoàn thành</p>
                          <p className="text-xs text-gray-500">
                            {student.completedLessons}/{student.totalLessons} bài học
                          </p>
                        </div>
                        <div className="w-24">
                          <Progress value={student.progress} />
                        </div>
                        <Button variant="outline" size="sm">
                          <MessageSquare className="h-4 w-4 mr-1" />
                          Nhắn tin
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Doanh thu theo tháng</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-100 rounded">
                    <p className="text-gray-500">Biểu đồ doanh thu sẽ được hiển thị ở đây</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Số lượng học viên mới</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-100 rounded">
                    <p className="text-gray-500">Biểu đồ học viên mới sẽ được hiển thị ở đây</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
