import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import Test from '@/models/Test';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return authResult.error;
    }

    const { user } = authResult;
    const { id } = params;

    await connectDB();

    // Get test details
    const test = await Test.findById(id);
    if (!test) {
      return NextResponse.json({
        success: false,
        message: 'Không tìm thấy bài kiểm tra'
      }, { status: 404 });
    }

    // For now, return mock result data - in a real app you'd fetch from TestResult collection
    const mockResult = {
      id: `result-${id}`,
      testId: id,
      testTitle: test.title,
      studentName: user.name,
      totalScore: 85,
      maxScore: 100,
      percentage: 85,
      timeSpent: 1800, // 30 minutes in seconds
      startedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
      completedAt: new Date().toISOString(),
      isPassed: true,
      passingScore: 70,
      detailedAnalysis: {
        correctAnswers: 17,
        incorrectAnswers: 3,
        skippedAnswers: 0,
        skillBreakdown: [
          { skill: "Từ vựng cơ bản", score: 45, maxScore: 50, percentage: 90 },
          { skill: "Từ vựng nâng cao", score: 25, maxScore: 30, percentage: 83 },
          { skill: "Thành ngữ", score: 15, maxScore: 20, percentage: 75 }
        ]
      },
      answers: [
        {
          questionOrder: 1,
          question: "What does 'beautiful' mean?",
          userAnswer: "Đẹp",
          correctAnswer: "Đẹp",
          isCorrect: true,
          points: 5,
          maxPoints: 5,
          explanation: "Beautiful có nghĩa là đẹp, xinh đẹp."
        },
        {
          questionOrder: 2,
          question: "Choose the correct translation for 'house':",
          userAnswer: "Nhà",
          correctAnswer: "Nhà", 
          isCorrect: true,
          points: 5,
          maxPoints: 5,
          explanation: "House có nghĩa là ngôi nhà, căn nhà."
        },
        {
          questionOrder: 3,
          question: "What is the meaning of 'difficult'?",
          userAnswer: "Dễ dàng",
          correctAnswer: "Khó khăn",
          isCorrect: false,
          points: 0,
          maxPoints: 5,
          explanation: "Difficult có nghĩa là khó khăn, khó làm, không phải dễ dàng."
        }
      ],
      feedback: "Bạn đã làm rất tốt! Cần cải thiện thêm về từ vựng nâng cao và thành ngữ.",
      recommendations: [
        "Ôn tập thêm từ vựng về tính từ mô tả cảm xúc",
        "Luyện tập thêm các thành ngữ thông dụng",
        "Đọc thêm các bài văn ngắn để mở rộng vốn từ"
      ]
    };

    return NextResponse.json({
      success: true,
      data: mockResult
    });

  } catch (error) {
    console.error('Get test result error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Lỗi hệ thống, vui lòng thử lại sau',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
