"use client";

import { useState } from "react";
import Link from "next/link";
import { 
  Users, 
  BookOpen, 
  DollarSign, 
  TrendingUp,
  UserPlus,
  Settings,
  Shield,
  BarChart3,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import Header from "@/components/layout/Header";
import { formatPrice } from "@/lib/utils";

// Mock data for admin
const mockSystemStats = {
  totalUsers: 5420,
  totalCourses: 156,
  totalRevenue: 2450000000,
  monthlyRevenue: 185000000,
  activeUsers: 3240,
  newUsersThisMonth: 342,
  coursesPublishedThisMonth: 12,
  averageRating: 4.7
};

const mockRecentUsers = [
  {
    id: "1",
    name: "Nguyễn Văn A",
    email: "<EMAIL>",
    role: "student",
    joinDate: "2024-01-20",
    status: "active",
    avatar: ""
  },
  {
    id: "2", 
    name: "Trần Thị B",
    email: "<EMAIL>",
    role: "teacher",
    joinDate: "2024-01-19",
    status: "pending",
    avatar: ""
  },
  {
    id: "3",
    name: "Lê Văn C", 
    email: "<EMAIL>",
    role: "student",
    joinDate: "2024-01-18",
    status: "active",
    avatar: ""
  }
];

const mockRecentCourses = [
  {
    id: "1",
    title: "Advanced English Grammar",
    instructor: "Cô Minh Anh",
    students: 45,
    status: "published",
    createdAt: "2024-01-15",
    revenue: 15000000
  },
  {
    id: "2",
    title: "Business English Communication",
    instructor: "Thầy Đức Minh", 
    students: 32,
    status: "pending",
    createdAt: "2024-01-18",
    revenue: 8500000
  },
  {
    id: "3",
    title: "IELTS Speaking Masterclass",
    instructor: "Cô Thu Hà",
    students: 67,
    status: "published", 
    createdAt: "2024-01-12",
    revenue: 22000000
  }
];

const mockSystemAlerts = [
  {
    id: "1",
    type: "warning",
    title: "Server Load High",
    message: "CPU usage đang ở mức 85%. Cần theo dõi.",
    time: "5 phút trước"
  },
  {
    id: "2",
    type: "info", 
    title: "Backup Completed",
    message: "Database backup đã hoàn thành thành công.",
    time: "2 giờ trước"
  },
  {
    id: "3",
    type: "success",
    title: "Payment Processed",
    message: "Đã xử lý thành công 45 giao dịch thanh toán.",
    time: "4 giờ trước"
  }
];

const mockTopTeachers = [
  {
    name: "Cô Minh Anh",
    courses: 8,
    students: 1250,
    revenue: 45000000,
    rating: 4.9
  },
  {
    name: "Thầy Đức Minh",
    courses: 6,
    students: 890,
    revenue: 32000000,
    rating: 4.8
  },
  {
    name: "Cô Thu Hà",
    courses: 5,
    students: 720,
    revenue: 28000000,
    rating: 4.7
  }
];

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Admin Dashboard
            </h1>
            <p className="text-lg text-gray-600">
              Quản lý và giám sát toàn bộ hệ thống
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Cài đặt
            </Button>
            <Button>
              <UserPlus className="h-4 w-4 mr-2" />
              Thêm người dùng
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tổng người dùng</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockSystemStats.totalUsers.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                +{mockSystemStats.newUsersThisMonth} người dùng mới tháng này
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tổng khóa học</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockSystemStats.totalCourses}</div>
              <p className="text-xs text-muted-foreground">
                +{mockSystemStats.coursesPublishedThisMonth} khóa học mới tháng này
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Doanh thu tháng này</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatPrice(mockSystemStats.monthlyRevenue)}</div>
              <p className="text-xs text-muted-foreground">
                +15% so với tháng trước
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Người dùng hoạt động</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockSystemStats.activeUsers.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {Math.round((mockSystemStats.activeUsers / mockSystemStats.totalUsers) * 100)}% tổng số người dùng
              </p>
            </CardContent>
          </Card>
        </div>

        {/* System Alerts */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Cảnh báo hệ thống
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockSystemAlerts.map(alert => (
                <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    {alert.type === 'warning' && <AlertTriangle className="h-4 w-4 text-yellow-500" />}
                    {alert.type === 'success' && <CheckCircle className="h-4 w-4 text-green-500" />}
                    {alert.type === 'info' && <Clock className="h-4 w-4 text-blue-500" />}
                    <div>
                      <h3 className="font-medium text-sm">{alert.title}</h3>
                      <p className="text-sm text-gray-600">{alert.message}</p>
                    </div>
                  </div>
                  <span className="text-xs text-gray-500">{alert.time}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Tổng quan</TabsTrigger>
            <TabsTrigger value="users">Người dùng</TabsTrigger>
            <TabsTrigger value="courses">Khóa học</TabsTrigger>
            <TabsTrigger value="analytics">Thống kê</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Teachers */}
              <Card>
                <CardHeader>
                  <CardTitle>Giáo viên xuất sắc</CardTitle>
                  <CardDescription>
                    Top giáo viên có hiệu suất cao nhất
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {mockTopTeachers.map((teacher, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium">{index + 1}</span>
                        </div>
                        <div>
                          <h3 className="font-medium text-sm">{teacher.name}</h3>
                          <p className="text-xs text-gray-500">
                            {teacher.courses} khóa học • {teacher.students} học viên
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{formatPrice(teacher.revenue)}</p>
                        <div className="flex items-center">
                          <span className="text-xs text-gray-500 mr-1">⭐ {teacher.rating}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle>Hoạt động gần đây</CardTitle>
                  <CardDescription>
                    Người dùng và khóa học mới
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium text-sm mb-3">Người dùng mới</h4>
                    {mockRecentUsers.slice(0, 3).map(user => (
                      <div key={user.id} className="flex items-center justify-between py-2">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={user.avatar} />
                            <AvatarFallback>
                              {user.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="text-sm font-medium">{user.name}</p>
                            <p className="text-xs text-gray-500">{user.email}</p>
                          </div>
                        </div>
                        <Badge variant={user.role === 'teacher' ? 'default' : 'secondary'}>
                          {user.role === 'teacher' ? 'Giáo viên' : 'Học viên'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Quản lý người dùng</h2>
              <Button>
                <UserPlus className="h-4 w-4 mr-2" />
                Thêm người dùng
              </Button>
            </div>
            
            <Card>
              <CardHeader>
                <CardTitle>Danh sách người dùng</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockRecentUsers.map(user => (
                    <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <Avatar>
                          <AvatarImage src={user.avatar} />
                          <AvatarFallback>
                            {user.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="font-medium">{user.name}</h3>
                          <p className="text-sm text-gray-500">{user.email}</p>
                          <p className="text-xs text-gray-400">Tham gia: {user.joinDate}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Badge variant={user.role === 'teacher' ? 'default' : 'secondary'}>
                          {user.role === 'teacher' ? 'Giáo viên' : 'Học viên'}
                        </Badge>
                        <Badge variant={user.status === 'active' ? 'default' : 'secondary'}>
                          {user.status === 'active' ? 'Hoạt động' : 'Chờ duyệt'}
                        </Badge>
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Courses Tab */}
          <TabsContent value="courses" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Quản lý khóa học</h2>
              <Button>
                <Shield className="h-4 w-4 mr-2" />
                Duyệt khóa học
              </Button>
            </div>
            
            <Card>
              <CardHeader>
                <CardTitle>Khóa học gần đây</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockRecentCourses.map(course => (
                    <div key={course.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-16 h-16 bg-gray-200 rounded-lg"></div>
                        <div>
                          <h3 className="font-medium">{course.title}</h3>
                          <p className="text-sm text-gray-500">Giảng viên: {course.instructor}</p>
                          <p className="text-xs text-gray-400">
                            {course.students} học viên • {formatPrice(course.revenue)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Badge variant={course.status === 'published' ? 'default' : 'secondary'}>
                          {course.status === 'published' ? 'Đã xuất bản' : 'Chờ duyệt'}
                        </Badge>
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Doanh thu theo tháng</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-100 rounded">
                    <p className="text-gray-500">Biểu đồ doanh thu sẽ được hiển thị ở đây</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Người dùng hoạt động</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-100 rounded">
                    <p className="text-gray-500">Biểu đồ người dùng hoạt động sẽ được hiển thị ở đây</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
