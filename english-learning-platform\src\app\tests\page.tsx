import Link from "next/link";
import { Search, Filter, Clock, Users, Target, Play, Award, TrendingUp } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Header from "@/components/layout/Header";

// Mock data for tests
const mockTests = [
  {
    id: "1",
    title: "Kiểm tra từ vựng cơ bản",
    description: "<PERSON><PERSON>h giá kiến thức từ vựng tiếng Anh cơ bản với 50 câu hỏi trắc nghiệm",
    type: "vocabulary",
    level: "beginner",
    duration: 30,
    totalQuestions: 50,
    participants: 1250,
    averageScore: 75,
    difficulty: "easy",
    tags: ["từ vựng", "c<PERSON> bản", "trắc nghiệm"]
  },
  {
    id: "2",
    title: "IELTS Listening Practice Test",
    description: "Bài luyện thi IELTS Listening với 4 phần thi chuẩn quốc tế",
    type: "listening",
    level: "intermediate",
    duration: 40,
    totalQuestions: 40,
    participants: 890,
    averageScore: 68,
    difficulty: "medium",
    tags: ["IELTS", "listening", "quốc tế"]
  },
  {
    id: "3",
    title: "Ngữ pháp tiếng Anh nâng cao",
    description: "Kiểm tra kiến thức ngữ pháp từ trung cấp đến nâng cao",
    type: "grammar",
    level: "advanced",
    duration: 45,
    totalQuestions: 60,
    participants: 650,
    averageScore: 72,
    difficulty: "hard",
    tags: ["ngữ pháp", "nâng cao", "grammar"]
  },
  {
    id: "4",
    title: "Luyện nói tiếng Anh giao tiếp",
    description: "Thực hành kỹ năng nói qua các tình huống giao tiếp thực tế",
    type: "speaking",
    level: "intermediate",
    duration: 25,
    totalQuestions: 15,
    participants: 420,
    averageScore: 78,
    difficulty: "medium",
    tags: ["speaking", "giao tiếp", "thực hành"]
  },
  {
    id: "5",
    title: "TOEIC Reading Comprehension",
    description: "Bài thi đọc hiểu TOEIC với các dạng bài chuẩn",
    type: "reading",
    level: "intermediate",
    duration: 60,
    totalQuestions: 75,
    participants: 780,
    averageScore: 70,
    difficulty: "medium",
    tags: ["TOEIC", "reading", "đọc hiểu"]
  },
  {
    id: "6",
    title: "Luyện viết IELTS Writing Task 2",
    description: "Thực hành viết luận IELTS Writing Task 2 với các chủ đề phổ biến",
    type: "writing",
    level: "advanced",
    duration: 40,
    totalQuestions: 1,
    participants: 340,
    averageScore: 65,
    difficulty: "hard",
    tags: ["IELTS", "writing", "luận"]
  }
];

const testCategories = [
  { value: "all", label: "Tất cả", icon: Target },
  { value: "vocabulary", label: "Từ vựng", icon: Target },
  { value: "grammar", label: "Ngữ pháp", icon: Target },
  { value: "listening", label: "Nghe", icon: Target },
  { value: "speaking", label: "Nói", icon: Target },
  { value: "reading", label: "Đọc", icon: Target },
  { value: "writing", label: "Viết", icon: Target }
];

const levels = [
  { value: "all", label: "Tất cả trình độ" },
  { value: "beginner", label: "Cơ bản" },
  { value: "intermediate", label: "Trung cấp" },
  { value: "advanced", label: "Nâng cao" }
];

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case "easy": return "bg-green-100 text-green-800";
    case "medium": return "bg-yellow-100 text-yellow-800";
    case "hard": return "bg-red-100 text-red-800";
    default: return "bg-gray-100 text-gray-800";
  }
};

const getTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    vocabulary: "Từ vựng",
    grammar: "Ngữ pháp", 
    listening: "Nghe",
    speaking: "Nói",
    reading: "Đọc",
    writing: "Viết"
  };
  return typeMap[type] || type;
};

const getLevelLabel = (level: string) => {
  const levelMap: Record<string, string> = {
    beginner: "Cơ bản",
    intermediate: "Trung cấp",
    advanced: "Nâng cao"
  };
  return levelMap[level] || level;
};

export default function TestsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Luyện Thi Tiếng Anh</h1>
          <p className="text-lg text-gray-600">
            Kiểm tra và nâng cao trình độ tiếng Anh của bạn với các bài thi đa dạng
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tổng bài thi</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockTests.length}</div>
              <p className="text-xs text-muted-foreground">
                Đa dạng các loại bài thi
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Lượt thi</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {mockTests.reduce((sum, test) => sum + test.participants, 0).toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                Tổng lượt thi của tất cả bài
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Điểm trung bình</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(mockTests.reduce((sum, test) => sum + test.averageScore, 0) / mockTests.length)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Điểm số trung bình của tất cả bài thi
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Thời gian trung bình</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(mockTests.reduce((sum, test) => sum + test.duration, 0) / mockTests.length)} phút
              </div>
              <p className="text-xs text-muted-foreground">
                Thời gian làm bài trung bình
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Tìm kiếm bài thi..."
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <select className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                {testCategories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
              <select className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                {levels.map(level => (
                  <option key={level.value} value={level.value}>
                    {level.label}
                  </option>
                ))}
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Lọc
              </Button>
            </div>
          </div>
        </div>

        {/* Test Categories Tabs */}
        <Tabs defaultValue="all" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 lg:grid-cols-7">
            {testCategories.map(category => (
              <TabsTrigger key={category.value} value={category.value} className="text-xs">
                {category.label}
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value="all" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {mockTests.map(test => (
                <Card key={test.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="outline">
                        {getTypeLabel(test.type)}
                      </Badge>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(test.difficulty)}`}>
                        {test.difficulty === 'easy' ? 'Dễ' : test.difficulty === 'medium' ? 'Trung bình' : 'Khó'}
                      </span>
                    </div>
                    <CardTitle className="text-lg line-clamp-2">{test.title}</CardTitle>
                    <CardDescription className="line-clamp-2">
                      {test.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {test.duration} phút
                      </div>
                      <div className="flex items-center">
                        <Target className="h-4 w-4 mr-1" />
                        {test.totalQuestions} câu
                      </div>
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-1" />
                        {test.participants}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Điểm TB: <span className="font-medium">{test.averageScore}%</span></p>
                        <p className="text-xs text-gray-500">{getLevelLabel(test.level)}</p>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Award className="h-4 w-4" />
                        </Button>
                        <Link href={`/tests/${test.id}`}>
                          <Button size="sm">
                            <Play className="h-4 w-4 mr-1" />
                            Bắt đầu
                          </Button>
                        </Link>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-1">
                      {test.tags.slice(0, 3).map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Individual category tabs would filter the tests */}
          {testCategories.slice(1).map(category => (
            <TabsContent key={category.value} value={category.value} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {mockTests
                  .filter(test => test.type === category.value)
                  .map(test => (
                    <Card key={test.id} className="hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className="flex items-center justify-between mb-2">
                          <Badge variant="outline">
                            {getTypeLabel(test.type)}
                          </Badge>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(test.difficulty)}`}>
                            {test.difficulty === 'easy' ? 'Dễ' : test.difficulty === 'medium' ? 'Trung bình' : 'Khó'}
                          </span>
                        </div>
                        <CardTitle className="text-lg line-clamp-2">{test.title}</CardTitle>
                        <CardDescription className="line-clamp-2">
                          {test.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {test.duration} phút
                          </div>
                          <div className="flex items-center">
                            <Target className="h-4 w-4 mr-1" />
                            {test.totalQuestions} câu
                          </div>
                          <div className="flex items-center">
                            <Users className="h-4 w-4 mr-1" />
                            {test.participants}
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-gray-600">Điểm TB: <span className="font-medium">{test.averageScore}%</span></p>
                            <p className="text-xs text-gray-500">{getLevelLabel(test.level)}</p>
                          </div>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              <Award className="h-4 w-4" />
                            </Button>
                            <Link href={`/tests/${test.id}`}>
                              <Button size="sm">
                                <Play className="h-4 w-4 mr-1" />
                                Bắt đầu
                              </Button>
                            </Link>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-1">
                          {test.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  );
}
