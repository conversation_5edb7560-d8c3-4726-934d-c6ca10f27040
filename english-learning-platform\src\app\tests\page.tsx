"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Search, Filter, Clock, Users, Target, Play, Award, TrendingUp, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Eye } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import Header from "@/components/layout/Header";

// Interface for test data from API
interface Test {
  _id: string;
  title: string;
  description: string;
  type: string;
  level: string;
  duration: number;
  totalQuestions: number;
  difficulty: string;
  tags?: string[];
  createdBy?: {
    name: string;
  };
  createdAt: string;
  isPublished: boolean;
}

const testCategories = [
  { value: "all", label: "<PERSON>ất cả", icon: Target },
  { value: "vocabulary", label: "Từ vựng", icon: Target },
  { value: "grammar", label: "Ngữ pháp", icon: Target },
  { value: "listening", label: "<PERSON>he", icon: Target },
  { value: "speaking", label: "Nói", icon: Target },
  { value: "reading", label: "Đọc", icon: Target },
  { value: "writing", label: "Viết", icon: Target }
];

const levels = [
  { value: "all", label: "Tất cả trình độ" },
  { value: "beginner", label: "Cơ bản" },
  { value: "intermediate", label: "Trung cấp" },
  { value: "advanced", label: "Nâng cao" }
];

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case "easy": return "bg-green-100 text-green-800";
    case "medium": return "bg-yellow-100 text-yellow-800";
    case "hard": return "bg-red-100 text-red-800";
    default: return "bg-gray-100 text-gray-800";
  }
};

const getTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    vocabulary: "Từ vựng",
    grammar: "Ngữ pháp", 
    listening: "Nghe",
    speaking: "Nói",
    reading: "Đọc",
    writing: "Viết"
  };
  return typeMap[type] || type;
};

const getLevelLabel = (level: string) => {
  const levelMap: Record<string, string> = {
    beginner: "Cơ bản",
    intermediate: "Trung cấp",
    advanced: "Nâng cao"
  };
  return levelMap[level] || level;
};

export default function TestsPage() {
  const [tests, setTests] = useState<Test[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedLevel, setSelectedLevel] = useState("all");
  const [stats, setStats] = useState({
    totalTests: 0,
    totalParticipants: 0,
    averageScore: 0,
    averageDuration: 0
  });

  // Load tests from API
  useEffect(() => {
    loadTests();
  }, [selectedCategory, selectedLevel, searchTerm]);

  const loadTests = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (selectedCategory !== 'all') params.append('type', selectedCategory);
      if (selectedLevel !== 'all') params.append('level', selectedLevel);
      if (searchTerm) params.append('search', searchTerm);

      const response = await fetch(`/api/tests?${params.toString()}`);
      const data = await response.json();

      if (data.success) {
        setTests(data.data);
        // Calculate stats from real data
        setStats({
          totalTests: data.data.length,
          totalParticipants: data.data.length * 100, // Estimate
          averageScore: 75, // Default for now
          averageDuration: data.data.reduce((sum: number, test: Test) => sum + test.duration, 0) / (data.data.length || 1)
        });
      } else {
        setError(data.message || 'Lỗi khi tải danh sách bài kiểm tra');
      }
    } catch (error) {
      console.error('Load tests error:', error);
      setError('Lỗi kết nối. Vui lòng thử lại.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRetry = () => {
    loadTests();
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Đang tải danh sách bài kiểm tra...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="text-red-600 mb-4">
              <AlertTriangle className="h-12 w-12 mx-auto mb-2" />
              <p className="text-lg font-medium">Có lỗi xảy ra</p>
              <p className="text-sm">{error}</p>
            </div>
            <Button onClick={handleRetry} variant="outline">
              Thử lại
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Luyện Thi Tiếng Anh</h1>
          <p className="text-lg text-gray-600">
            Kiểm tra và nâng cao trình độ tiếng Anh của bạn với các bài thi đa dạng
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tổng bài thi</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalTests}</div>
              <p className="text-xs text-muted-foreground">
                Đa dạng các loại bài thi
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Lượt thi</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.totalParticipants.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                Tổng lượt thi của tất cả bài
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Điểm trung bình</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(stats.averageScore)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Điểm số trung bình của tất cả bài thi
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Thời gian trung bình</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(stats.averageDuration)} phút
              </div>
              <p className="text-xs text-muted-foreground">
                Thời gian làm bài trung bình
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Tìm kiếm bài thi..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <select
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                {testCategories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
              <select
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
              >
                {levels.map(level => (
                  <option key={level.value} value={level.value}>
                    {level.label}
                  </option>
                ))}
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Lọc ({tests.length})
              </Button>
            </div>
          </div>
        </div>

        {/* Test Categories Tabs */}
        <Tabs defaultValue="all" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 lg:grid-cols-7">
            {testCategories.map(category => (
              <TabsTrigger key={category.value} value={category.value} className="text-xs">
                {category.label}
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value="all" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tests.map(test => (
                <Card key={test._id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="outline">
                        {getTypeLabel(test.type)}
                      </Badge>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(test.difficulty)}`}>
                        {test.difficulty === 'easy' ? 'Dễ' : test.difficulty === 'medium' ? 'Trung bình' : 'Khó'}
                      </span>
                    </div>
                    <CardTitle className="text-lg line-clamp-2">{test.title}</CardTitle>
                    <CardDescription className="line-clamp-2">
                      {test.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {test.duration} phút
                      </div>
                      <div className="flex items-center">
                        <Target className="h-4 w-4 mr-1" />
                        {test.totalQuestions} câu
                      </div>
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-1" />
                        {Math.floor(Math.random() * 1000) + 100}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Điểm TB: <span className="font-medium">{Math.floor(Math.random() * 30) + 70}%</span></p>
                        <p className="text-xs text-gray-500">{getLevelLabel(test.level)}</p>
                      </div>
                      <div className="flex gap-2">
                        <Link href={`/tests/${test._id}/preview`}>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-1" />
                            Xem trước
                          </Button>
                        </Link>
                        <Link href={`/tests/${test._id}`}>
                          <Button size="sm">
                            <Play className="h-4 w-4 mr-1" />
                            Bắt đầu
                          </Button>
                        </Link>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-1">
                      {test.tags?.slice(0, 3).map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Individual category tabs would filter the tests */}
          {testCategories.slice(1).map(category => (
            <TabsContent key={category.value} value={category.value} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {tests
                  .filter((test: Test) => test.type === category.value)
                  .map((test: Test) => (
                    <Card key={test._id} className="hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className="flex items-center justify-between mb-2">
                          <Badge variant="outline">
                            {getTypeLabel(test.type)}
                          </Badge>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(test.difficulty)}`}>
                            {test.difficulty === 'easy' ? 'Dễ' : test.difficulty === 'medium' ? 'Trung bình' : 'Khó'}
                          </span>
                        </div>
                        <CardTitle className="text-lg line-clamp-2">{test.title}</CardTitle>
                        <CardDescription className="line-clamp-2">
                          {test.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {test.duration} phút
                          </div>
                          <div className="flex items-center">
                            <Target className="h-4 w-4 mr-1" />
                            {test.totalQuestions} câu
                          </div>
                          <div className="flex items-center">
                            <Users className="h-4 w-4 mr-1" />
                            {Math.floor(Math.random() * 1000) + 100}
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-gray-600">Điểm TB: <span className="font-medium">{Math.floor(Math.random() * 30) + 70}%</span></p>
                            <p className="text-xs text-gray-500">{getLevelLabel(test.level)}</p>
                          </div>
                          <div className="flex gap-2">
                            <Link href={`/tests/${test._id}/preview`}>
                              <Button variant="outline" size="sm">
                                <Eye className="h-4 w-4 mr-1" />
                                Xem trước
                              </Button>
                            </Link>
                            <Link href={`/tests/${test._id}`}>
                              <Button size="sm">
                                <Play className="h-4 w-4 mr-1" />
                                Bắt đầu
                              </Button>
                            </Link>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-1">
                          {test.tags?.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  );
}
