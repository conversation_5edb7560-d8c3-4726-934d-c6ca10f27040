"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import AudioPlayer from "./AudioPlayer";
import AudioRecorder from "./AudioRecorder";
import { Question, QuestionType } from "@/types";

interface QuestionComponentProps {
  question: Question;
  questionIndex: number;
  answer?: string | string[];
  onAnswerChange: (answer: string | string[]) => void;
  isSubmitted?: boolean;
  showCorrectAnswer?: boolean;
  timeRemaining?: number;
}

// Listening Multiple Choice Component
export function ListeningMultipleChoice({
  question,
  questionIndex,
  answer,
  onAnswerChange,
  isSubmitted,
  showCorrectAnswer
}: QuestionComponentProps) {
  const [hasPlayedAudio, setHasPlayedAudio] = useState(false);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg">
          Câu {questionIndex + 1}: Nghe và chọn đáp án đúng
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Audio Player */}
        {question.audioUrl && (
          <AudioPlayer
            audioUrl={question.audioUrl}
            title="Nghe đoạn hội thoại/bài nghe"
            onPlay={() => setHasPlayedAudio(true)}
            allowReplay={true}
            maxReplays={2}
          />
        )}

        {/* Question Text */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <p className="text-gray-800">{question.question}</p>
        </div>

        {/* Multiple Choice Options */}
        <RadioGroup
          value={answer as string}
          onValueChange={onAnswerChange}
          disabled={isSubmitted}
        >
          {question.options?.map((option, index) => {
            const optionLetter = String.fromCharCode(65 + index); // A, B, C, D
            const isCorrect = showCorrectAnswer && option === question.correctAnswer;
            const isSelected = answer === option;
            const isWrong = showCorrectAnswer && isSelected && !isCorrect;

            return (
              <div
                key={index}
                className={`flex items-center space-x-2 p-3 rounded-lg border ${
                  isCorrect
                    ? 'bg-green-50 border-green-200'
                    : isWrong
                    ? 'bg-red-50 border-red-200'
                    : 'bg-white border-gray-200'
                }`}
              >
                <RadioGroupItem value={option} id={`option-${questionIndex}-${index}`} />
                <Label
                  htmlFor={`option-${questionIndex}-${index}`}
                  className="flex-1 cursor-pointer"
                >
                  <span className="font-medium mr-2">{optionLetter}.</span>
                  {option}
                </Label>
              </div>
            );
          })}
        </RadioGroup>

        {!hasPlayedAudio && (
          <div className="text-amber-600 text-sm">
            ⚠️ Vui lòng nghe audio trước khi trả lời
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Listening Fill in the Blank Component
export function ListeningFillBlank({
  question,
  questionIndex,
  answer,
  onAnswerChange,
  isSubmitted,
  showCorrectAnswer
}: QuestionComponentProps) {
  const [blanks, setBlanks] = useState<string[]>([]);
  const [hasPlayedAudio, setHasPlayedAudio] = useState(false);

  useEffect(() => {
    // Initialize blanks array based on question format
    const blankCount = (question.question.match(/_+/g) || []).length;
    setBlanks(new Array(blankCount).fill(''));
    
    // If answer is provided, populate blanks
    if (answer && Array.isArray(answer)) {
      setBlanks(answer);
    }
  }, [question, answer]);

  const handleBlankChange = (index: number, value: string) => {
    const newBlanks = [...blanks];
    newBlanks[index] = value;
    setBlanks(newBlanks);
    onAnswerChange(newBlanks);
  };

  const renderQuestionWithBlanks = () => {
    const parts = question.question.split(/_+/);
    const result = [];

    for (let i = 0; i < parts.length; i++) {
      result.push(<span key={`text-${i}`}>{parts[i]}</span>);
      
      if (i < parts.length - 1) {
        const correctAnswer = Array.isArray(question.correctAnswer) 
          ? question.correctAnswer[i] 
          : '';
        const userAnswer = blanks[i] || '';
        const isCorrect = showCorrectAnswer && userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();
        const isWrong = showCorrectAnswer && userAnswer && !isCorrect;

        result.push(
          <Input
            key={`blank-${i}`}
            value={blanks[i] || ''}
            onChange={(e) => handleBlankChange(i, e.target.value)}
            disabled={isSubmitted}
            className={`inline-block w-24 mx-1 text-center ${
              isCorrect
                ? 'border-green-500 bg-green-50'
                : isWrong
                ? 'border-red-500 bg-red-50'
                : ''
            }`}
            placeholder="..."
          />
        );
      }
    }

    return result;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg">
          Câu {questionIndex + 1}: Nghe và điền từ còn thiếu
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Audio Player */}
        {question.audioUrl && (
          <AudioPlayer
            audioUrl={question.audioUrl}
            title="Nghe đoạn hội thoại/bài nghe"
            onPlay={() => setHasPlayedAudio(true)}
            allowReplay={true}
            maxReplays={3}
          />
        )}

        {/* Question with Blanks */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <div className="text-gray-800 leading-relaxed">
            {renderQuestionWithBlanks()}
          </div>
        </div>

        {/* Show correct answers if needed */}
        {showCorrectAnswer && Array.isArray(question.correctAnswer) && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm font-medium text-green-800 mb-2">Đáp án đúng:</p>
            <div className="text-sm text-green-700">
              {question.correctAnswer.map((answer, index) => (
                <span key={index} className="mr-4">
                  Chỗ trống {index + 1}: <strong>{answer}</strong>
                </span>
              ))}
            </div>
          </div>
        )}

        {!hasPlayedAudio && (
          <div className="text-amber-600 text-sm">
            ⚠️ Vui lòng nghe audio trước khi trả lời
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Speaking Record Component
export function SpeakingRecord({
  question,
  questionIndex,
  answer,
  onAnswerChange,
  isSubmitted
}: QuestionComponentProps) {
  const handleRecordingComplete = (audioBlob: Blob, duration: number) => {
    // Store the audio blob for later upload
    onAnswerChange(`audio-blob-${Date.now()}`);
  };

  const handleUploadComplete = (audioUrl: string) => {
    onAnswerChange(audioUrl);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg">
          Câu {questionIndex + 1}: Ghi âm câu trả lời
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Question Text */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <p className="text-gray-800">{question.question}</p>
        </div>

        {/* Audio Recorder */}
        <AudioRecorder
          questionId={question._id || `q-${questionIndex}`}
          timeLimit={question.timeLimit || 120}
          onRecordingComplete={handleRecordingComplete}
          onUploadComplete={handleUploadComplete}
          maxAttempts={3}
          allowReRecord={!isSubmitted}
          prompt="Hãy nói rõ ràng và tự nhiên. Bạn có thể chuẩn bị trong vài giây trước khi bắt đầu ghi âm."
        />

        {/* Instructions */}
        <div className="p-3 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-700 mb-2">Hướng dẫn:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Đọc kỹ câu hỏi trước khi ghi âm</li>
            <li>• Nói rõ ràng và với tốc độ vừa phải</li>
            <li>• Trả lời đầy đủ theo yêu cầu của câu hỏi</li>
            <li>• Thời gian tối đa: {Math.floor((question.timeLimit || 120) / 60)} phút</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}

// Reading Comprehension Component
export function ReadingComprehension({
  question,
  questionIndex,
  answer,
  onAnswerChange,
  isSubmitted,
  showCorrectAnswer,
  timeRemaining
}: QuestionComponentProps) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg flex justify-between items-center">
          <span>Câu {questionIndex + 1}: Đọc hiểu</span>
          {timeRemaining && timeRemaining > 0 && (
            <div className="text-sm text-gray-500">
              Thời gian còn lại: {Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')}
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Reading Passage */}
        {question.readingPassage && (
          <div className="p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500">
            <h4 className="font-medium text-gray-700 mb-3">Đoạn văn:</h4>
            <div className="text-gray-800 leading-relaxed whitespace-pre-line">
              {question.readingPassage}
            </div>
          </div>
        )}

        {/* Question */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <p className="text-gray-800">{question.question}</p>
        </div>

        {/* Answer Options */}
        {question.type === QuestionType.MULTIPLE_CHOICE && question.options && (
          <RadioGroup
            value={answer as string}
            onValueChange={onAnswerChange}
            disabled={isSubmitted}
          >
            {question.options.map((option, index) => {
              const optionLetter = String.fromCharCode(65 + index);
              const isCorrect = showCorrectAnswer && option === question.correctAnswer;
              const isSelected = answer === option;
              const isWrong = showCorrectAnswer && isSelected && !isCorrect;

              return (
                <div
                  key={index}
                  className={`flex items-center space-x-2 p-3 rounded-lg border ${
                    isCorrect
                      ? 'bg-green-50 border-green-200'
                      : isWrong
                      ? 'bg-red-50 border-red-200'
                      : 'bg-white border-gray-200'
                  }`}
                >
                  <RadioGroupItem value={option} id={`reading-${questionIndex}-${index}`} />
                  <Label
                    htmlFor={`reading-${questionIndex}-${index}`}
                    className="flex-1 cursor-pointer"
                  >
                    <span className="font-medium mr-2">{optionLetter}.</span>
                    {option}
                  </Label>
                </div>
              );
            })}
          </RadioGroup>
        )}

        {question.type === QuestionType.TRUE_FALSE && (
          <RadioGroup
            value={answer as string}
            onValueChange={onAnswerChange}
            disabled={isSubmitted}
          >
            {['True', 'False'].map((option) => {
              const isCorrect = showCorrectAnswer && option === question.correctAnswer;
              const isSelected = answer === option;
              const isWrong = showCorrectAnswer && isSelected && !isCorrect;

              return (
                <div
                  key={option}
                  className={`flex items-center space-x-2 p-3 rounded-lg border ${
                    isCorrect
                      ? 'bg-green-50 border-green-200'
                      : isWrong
                      ? 'bg-red-50 border-red-200'
                      : 'bg-white border-gray-200'
                  }`}
                >
                  <RadioGroupItem value={option} id={`tf-${questionIndex}-${option}`} />
                  <Label
                    htmlFor={`tf-${questionIndex}-${option}`}
                    className="flex-1 cursor-pointer"
                  >
                    {option === 'True' ? 'Đúng' : 'Sai'}
                  </Label>
                </div>
              );
            })}
          </RadioGroup>
        )}

        {question.type === QuestionType.WRITING_SHORT_ANSWER && (
          <div className="space-y-2">
            <Label htmlFor={`short-answer-${questionIndex}`}>Câu trả lời ngắn:</Label>
            <Input
              id={`short-answer-${questionIndex}`}
              value={answer as string || ''}
              onChange={(e) => onAnswerChange(e.target.value)}
              disabled={isSubmitted}
              placeholder="Nhập câu trả lời của bạn..."
              className={
                showCorrectAnswer
                  ? (answer as string)?.toLowerCase().trim() === (question.correctAnswer as string)?.toLowerCase().trim()
                    ? 'border-green-500 bg-green-50'
                    : 'border-red-500 bg-red-50'
                  : ''
              }
            />
            {showCorrectAnswer && (
              <p className="text-sm text-green-600">
                Đáp án đúng: <strong>{question.correctAnswer as string}</strong>
              </p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Writing Essay Component
export function WritingEssay({
  question,
  questionIndex,
  answer,
  onAnswerChange,
  isSubmitted,
  timeRemaining
}: QuestionComponentProps) {
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);

  useEffect(() => {
    const text = (answer as string) || '';
    setCharCount(text.length);
    setWordCount(text.trim() ? text.trim().split(/\s+/).length : 0);
  }, [answer]);

  const handleTextChange = (value: string) => {
    onAnswerChange(value);
  };

  const minWords = question.wordLimit ? Math.floor(question.wordLimit * 0.8) : 150;
  const maxWords = question.wordLimit || 300;
  const isWordCountValid = wordCount >= minWords && wordCount <= maxWords;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg flex justify-between items-center">
          <span>Câu {questionIndex + 1}: Viết luận</span>
          {timeRemaining && timeRemaining > 0 && (
            <div className="text-sm text-gray-500">
              Thời gian còn lại: {Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')}
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Essay Prompt */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-gray-700 mb-2">Đề bài:</h4>
          <p className="text-gray-800">{question.question}</p>
        </div>

        {/* Writing Guidelines */}
        <div className="p-3 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-700 mb-2">Yêu cầu:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Số từ: {minWords} - {maxWords} từ</li>
            <li>• Cấu trúc: Mở bài - Thân bài - Kết luận</li>
            <li>• Sử dụng ngữ pháp và từ vựng chính xác</li>
            <li>• Trình bày ý tưởng rõ ràng và logic</li>
          </ul>
        </div>

        {/* Text Editor */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label htmlFor={`essay-${questionIndex}`}>Bài viết của bạn:</Label>
            <div className={`text-sm ${isWordCountValid ? 'text-green-600' : 'text-red-600'}`}>
              {wordCount}/{maxWords} từ ({charCount} ký tự)
            </div>
          </div>
          <Textarea
            id={`essay-${questionIndex}`}
            value={answer as string || ''}
            onChange={(e) => handleTextChange(e.target.value)}
            disabled={isSubmitted}
            placeholder="Bắt đầu viết bài luận của bạn ở đây..."
            className={`min-h-[300px] resize-none ${
              !isWordCountValid && wordCount > 0 ? 'border-red-500' : ''
            }`}
          />
        </div>

        {/* Word Count Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-500">
            <span>Tối thiểu: {minWords} từ</span>
            <span>Tối đa: {maxWords} từ</span>
          </div>
          <Progress
            value={(wordCount / maxWords) * 100}
            className={`h-2 ${wordCount > maxWords ? 'bg-red-100' : ''}`}
          />
          {wordCount < minWords && (
            <p className="text-xs text-amber-600">
              Cần thêm {minWords - wordCount} từ để đạt yêu cầu tối thiểu
            </p>
          )}
          {wordCount > maxWords && (
            <p className="text-xs text-red-600">
              Vượt quá {wordCount - maxWords} từ so với giới hạn
            </p>
          )}
        </div>

        {/* Writing Tips */}
        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h4 className="font-medium text-yellow-800 mb-2">💡 Gợi ý viết bài:</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Mở bài: Giới thiệu chủ đề và quan điểm của bạn</li>
            <li>• Thân bài: Trình bày các luận điểm với ví dụ cụ thể</li>
            <li>• Kết luận: Tóm tắt và khẳng định lại quan điểm</li>
            <li>• Kiểm tra lại chính tả và ngữ pháp trước khi nộp</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
